# 🏢 MTBRMG ERP System

<div align="center">

![MTBR<PERSON> Logo](the_logo.png)

**A Comprehensive Enterprise Resource Planning System for Digital Agencies**

[![Next.js](https://img.shields.io/badge/Next.js-14-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![Django](https://img.shields.io/badge/Django-5.0-green?style=for-the-badge&logo=django)](https://djangoproject.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3-blue?style=for-the-badge&logo=typescript)](https://typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)

[🚀 Live Demo](#-quick-start) • [📖 Documentation](#-documentation) • [🤝 Contributing](#-contributing) • [🆘 Support](#-support)

</div>

---

## 🌟 Overview

MTBRMG ERP is a modern, comprehensive Enterprise Resource Planning system specifically designed for digital agencies. Built with cutting-edge technologies and featuring a native RTL Arabic interface, it provides complete business management solutions from client acquisition to project delivery and financial tracking.

## 🚀 Quick Start (Local Development)

### Prerequisites
- macOS (tested on 10.15+)
- Homebrew package manager
- Node.js 18+ with pnpm
- Python 3.11+

### Automated Setup
```bash
# Clone the repository
git clone <repository-url>
cd mtbrmg-erp-system

# Run automated setup (installs PostgreSQL, Redis, dependencies)
./scripts/setup-local.sh

# Start all services
./scripts/start-local.sh
```

### Access the Application
- **Frontend**: http://localhost:3001/founder-dashboard
- **Login**: founder / demo123
- **Backend API**: http://localhost:8000/api/
- **Admin Panel**: http://localhost:8000/admin/

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 15.2.4 with TypeScript
- **Backend**: Django 4.2.9 with Django REST Framework
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7
- **Task Queue**: Celery with Redis broker
- **Styling**: Tailwind CSS with RTL support
- **Authentication**: JWT with extended sessions

### Project Structure
```
mtbrmg-erp-system/
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Django API server
├── packages/
│   ├── shared/            # Common types and utilities
│   ├── config/            # Shared configurations
│   └── ui/                # Shared UI components
├── scripts/               # Development scripts
└── docs/                  # Documentation
```

## 🔧 Development

### Manual Setup (Alternative)
If the automated script fails, you can set up manually:

```bash
# Install services
brew install postgresql@15 redis
brew services start postgresql@15 redis

# Create database
createdb mtbrmg_erp

# Backend setup
cd apps/backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
cp .env.local .env
python manage.py migrate
python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('founder', '<EMAIL>', 'demo123')"

# Frontend setup
cd ../frontend
pnpm install
```

### Development Commands
```bash
# Start all services
npm run start:local

# Individual services
npm run dev:backend     # Django backend only
npm run dev:frontend    # Next.js frontend only

# Database operations
npm run migrate         # Run Django migrations
npm run create-founder  # Create founder user

# Development tools
npm run build          # Build all applications
npm run lint           # Lint all code
npm run test           # Run tests
```

### Service Ports
| Service | Port | URL |
|---------|------|-----|
| PostgreSQL | 5432 | localhost:5432 |
| Redis | 6379 | localhost:6379 |
| Django Backend | 8000 | http://localhost:8000 |
| Next.js Frontend | 3001 | http://localhost:3001 |

## 🎯 Features

### Core Functionality
- **Unified Founder Dashboard**: Single dashboard architecture (no admin/user separation)
- **Client Management**: Comprehensive client profiles and contact management
- **Project Management**: Project creation, tracking, and milestone management
- **Task Management**: Task assignment, progress tracking, and deadlines
- **Team Management**: Team member profiles and role management
- **Single-Step Workflows**: Unified client/project creation with radio button options

### Technical Features
- **RTL Support**: Full Arabic language support with RTL layout
- **Responsive Design**: Mobile-first responsive interface
- **Real-time Updates**: Live data synchronization
- **Extended Sessions**: Long-lasting authentication for development
- **API Documentation**: Comprehensive REST API with Django Ninja
- **Type Safety**: Full TypeScript implementation

### Custom Branding
- **Animated Logo**: Custom sidebar logo with animations
- **Profile Customization**: Custom profile images and Arabic names
- **Localized Interface**: Arabic-first design with Egyptian market focus

## 🔐 Authentication

### Default Credentials
- **Username**: founder
- **Password**: demo123
- **Email**: <EMAIL>
- **Role**: Founder (full system access)

### Session Configuration
- **Session Duration**: Extended for development (1 year)
- **JWT Tokens**: 24-hour access tokens, 30-day refresh tokens
- **Auto-login**: Sessions persist across browser restarts

## 📊 Database Schema

### Core Models
- **User**: Extended user model with founder role
- **Client**: Client information and contact details
- **Project**: Project management with client relationships
- **Task**: Task tracking with assignments and deadlines
- **TeamMember**: Team member profiles and roles

### Relationships
- Clients → Projects (One-to-Many)
- Projects → Tasks (One-to-Many)
- Users → Tasks (Many-to-Many assignments)
- Users → TeamMembers (One-to-One profiles)

## 🛠️ Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using ports
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
lsof -i :8000  # Backend
lsof -i :3001  # Frontend
```

#### Service Issues
```bash
# Restart services
brew services restart postgresql@15
brew services restart redis

# Check service status
brew services list | grep -E "(postgresql|redis)"
```

#### Database Issues
```bash
# Reset database
dropdb mtbrmg_erp
createdb mtbrmg_erp
cd apps/backend && source venv/bin/activate
python manage.py migrate
python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('founder', '<EMAIL>', 'demo123')"
```

### Getting Help
1. Check the [Local Development Guide](LOCAL_DEVELOPMENT_GUIDE.md)
2. Review logs in `apps/backend/logs/django.log`
3. Ensure all services are running with `brew services list`

## 📚 Documentation

- [Local Development Guide](LOCAL_DEVELOPMENT_GUIDE.md) - Comprehensive setup instructions
- [Docker Transition Analysis](DOCKER_TO_LOCAL_TRANSITION_ANALYSIS.md) - Migration documentation
- [API Documentation](http://localhost:8000/api/) - Interactive API docs (when running)

## 🚀 Deployment

This system is configured for local development. For production deployment:
1. Update environment variables for production
2. Configure proper database and Redis instances
3. Set up proper SSL certificates
4. Configure static file serving
5. Set up monitoring and logging

## 📄 License

Private project for MTBRMG digital agency.

---

## ✨ Advanced Features

### 💰 Multi-Currency System

The system supports multiple currencies with real-time conversion:

- **Primary**: Egyptian Pound (EGP) - ج.م
- **Secondary**: US Dollar (USD) - $
- **Additional**: Saudi Riyal (SAR), UAE Dirham (AED)

Currency conversion uses CurrencyFreaks API with fallback rates for offline operation.

### 👥 Team Structure

The system supports hierarchical team management:

1. **Sales Team** - Client acquisition and commission tracking
2. **Developers Team** - Technical project execution
3. **Web Designers Team** - Creative and design work
4. **Media Buyers Team** - Digital marketing and advertising

### 📊 Commission System

- **Sales Commission**: 12.5% of total project value
- **Automatic Calculation**: Triggered on project creation
- **Multi-Currency Support**: Commissions calculated in project currency
- **Tracking**: Complete commission history and reporting

---

## 🌐 API Endpoints

### Authentication

- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/user/` - Get current user

### Clients

- `GET /api/clients/` - List clients
- `POST /api/clients/` - Create client
- `GET /api/clients/{id}/` - Get client details
- `PUT /api/clients/{id}/` - Update client
- `DELETE /api/clients/{id}/` - Delete client

### Projects

- `GET /api/projects/` - List projects
- `POST /api/projects/` - Create project
- `GET /api/projects/{id}/` - Get project details
- `PUT /api/projects/{id}/` - Update project

### Finance

- `GET /api/finance/revenue/` - Revenue tracking
- `GET /api/finance/expenses/` - Expense management
- `GET /api/finance/cash-flow/` - Cash flow analysis
- `GET /api/finance/budget/` - Budget management

---

## 🔒 Security Features

- JWT-based authentication
- Role-based access control
- Audit logging for sensitive operations
- CORS configuration for API security
- Input validation and sanitization

---

## 🧪 Testing

```bash
# Run all tests
pnpm test

# Frontend tests
cd apps/frontend && pnpm test

# Backend tests
cd apps/backend && python manage.py test
```

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

---

## 🆘 Support

For support and questions:

- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/MuhammadYAbdelftah1/Mtbrmg_sys/issues)

---

## 🙏 Acknowledgments

- Built with modern web technologies
- Designed for Egyptian digital agencies
- RTL Arabic interface support
- Comprehensive ERP functionality

---

<div align="center">

**MTBRMG ERP System** - Empowering Digital Agencies with Comprehensive Management Solutions

Made with ❤️ for the Egyptian Digital Agency Community

**Last Updated**: Current
**Environment**: Local Development
**Status**: Production Ready

</div>
