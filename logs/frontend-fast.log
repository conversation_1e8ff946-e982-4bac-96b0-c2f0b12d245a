
> @mtbrmg/frontend@1.0.0 dev /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend
> next dev --port 3001

   ▲ Next.js 15.2.4
   - Local:        http://localhost:3001
   - Network:      http://***********:3001
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 1874ms
 ○ Compiling /founder-dashboard ...
 ✓ Compiled /founder-dashboard in 2.9s (1426 modules)
CurrencyService: Getting immediate exchange rates
Cache read error: ReferenceError: localStorage is not defined
    at CurrencyConversionService.getCachedRates (lib/services/currency-conversion.ts:343:21)
    at CurrencyConversionService.getExchangeRatesImmediate (lib/services/currency-conversion.ts:107:28)
    at useCurrencyStore.name (lib/stores/currency-store.ts:40:47)
    at eval (lib/stores/currency-store.ts:36:2)
    at (ssr)/./lib/stores/currency-store.ts (.next/server/app/founder-dashboard/page.js:622:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./components/ui/currency-icon.tsx:13:84)
    at (ssr)/./components/ui/currency-icon.tsx (.next/server/app/founder-dashboard/page.js:490:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./app/founder-dashboard/page.tsx:30:87)
    at (ssr)/./app/founder-dashboard/page.tsx (.next/server/app/founder-dashboard/page.js:314:1)
    at Object.__webpack_require__ [as require] (.next/server/webpack-runtime.js:33:43)
    at JSON.parse (<anonymous>)
  341 |   private getCachedRates(): CurrencyData | null {
  342 |     try {
> 343 |       const cached = localStorage.getItem(this.CACHE_KEY);
      |                     ^
  344 |       if (!cached) return null;
  345 |       
  346 |       return JSON.parse(cached);
CurrencyService: Using default rates immediately
 GET /founder-dashboard 200 in 3461ms
 ✓ Compiled in 162ms (633 modules)
 GET /founder-dashboard 200 in 28ms
 ○ Compiling /founder-dashboard/clients ...
 ✓ Compiled /founder-dashboard/clients in 1102ms (1477 modules)
 GET /founder-dashboard/clients 200 in 1159ms
 ✓ Compiled /founder-dashboard/projects in 294ms (1504 modules)
 GET /founder-dashboard/projects 200 in 327ms
 ✓ Compiled /founder-dashboard/tasks in 353ms (1543 modules)
 GET /founder-dashboard/tasks 200 in 387ms
 GET /founder-dashboard/projects 200 in 38ms
 ○ Compiling /founder-dashboard/projects/new ...
 ✓ Compiled /founder-dashboard/projects/new in 740ms (1594 modules)
 GET /founder-dashboard/projects/new 200 in 771ms
 GET /founder-dashboard/tasks 200 in 25ms
 ✓ Compiled /founder-dashboard/team/sales in 440ms (1605 modules)
 GET /founder-dashboard/team/sales 200 in 508ms
 ✓ Compiled /founder-dashboard/team/sales/commissions in 409ms (1616 modules)
 GET /founder-dashboard/team/sales/commissions 200 in 441ms
 ✓ Compiled /founder-dashboard/team/developers in 480ms (1627 modules)
 GET /founder-dashboard/team/developers 200 in 510ms
 ✓ Compiled /founder-dashboard/team in 486ms (1652 modules)
 GET /founder-dashboard/team?add=development 200 in 537ms
 ✓ Compiled in 513ms (741 modules)
 GET /founder-dashboard/team?add=development 200 in 31ms
 ✓ Compiled in 633ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 71ms
 ✓ Compiled in 430ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 88ms
 ✓ Compiled in 483ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 34ms
 ✓ Compiled in 773ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 53ms
 ✓ Compiled in 831ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 168ms
 ✓ Compiled in 420ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 64ms
 ✓ Compiled in 443ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 31ms
 ✓ Compiled in 571ms (1609 modules)
 GET /founder-dashboard/team?add=development 200 in 60ms
 ✓ Compiled in 206ms (741 modules)
 GET /founder-dashboard/team?add=development 200 in 14ms
 ✓ Compiled in 149ms (741 modules)
 GET /founder-dashboard/team?add=development 200 in 10ms
 ○ Compiling /founder-dashboard ...
 ✓ Compiled /founder-dashboard in 630ms (889 modules)
 GET /founder-dashboard 200 in 668ms
CurrencyService: Getting immediate exchange rates
Cache read error: ReferenceError: localStorage is not defined
    at CurrencyConversionService.getCachedRates (lib/services/currency-conversion.ts:343:21)
    at CurrencyConversionService.getExchangeRatesImmediate (lib/services/currency-conversion.ts:107:28)
    at useCurrencyStore.name (lib/stores/currency-store.ts:40:47)
    at eval (lib/stores/currency-store.ts:36:2)
    at (ssr)/./lib/stores/currency-store.ts (.next/server/app/founder-dashboard/page.js:622:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./components/ui/currency-icon.tsx:13:84)
    at (ssr)/./components/ui/currency-icon.tsx (.next/server/app/founder-dashboard/page.js:490:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./app/founder-dashboard/page.tsx:30:87)
    at (ssr)/./app/founder-dashboard/page.tsx (.next/server/app/founder-dashboard/page.js:314:1)
    at Object.__webpack_require__ [as require] (.next/server/webpack-runtime.js:33:43)
    at JSON.parse (<anonymous>)
  341 |   private getCachedRates(): CurrencyData | null {
  342 |     try {
> 343 |       const cached = localStorage.getItem(this.CACHE_KEY);
      |                     ^
  344 |       if (!cached) return null;
  345 |       
  346 |       return JSON.parse(cached);
CurrencyService: Using default rates immediately
 GET /founder-dashboard 200 in 318ms
 ✓ Compiled in 219ms (741 modules)
 GET /founder-dashboard 200 in 21ms
 ○ Compiling /founder-dashboard/clients ...
 ✓ Compiled /founder-dashboard/clients in 554ms (877 modules)
 GET /founder-dashboard/clients 200 in 590ms
 ✓ Compiled in 214ms (741 modules)
 GET /founder-dashboard/clients 200 in 25ms
 ✓ Compiled in 314ms (741 modules)
 GET /founder-dashboard/clients 200 in 18ms
 GET /founder-dashboard/clients 200 in 120ms
 ✓ Compiled in 213ms (741 modules)
 GET /founder-dashboard/clients 200 in 12ms
 ○ Compiling /founder-dashboard/projects ...
 ✓ Compiled /founder-dashboard/projects in 590ms (871 modules)
 GET /founder-dashboard/projects 200 in 661ms
 ○ Compiling /founder-dashboard/projects/[id] ...
 ✓ Compiled /founder-dashboard/projects/[id] in 610ms (1614 modules)
 GET /founder-dashboard/projects/4 200 in 1533ms
 ✓ Compiled in 202ms (746 modules)
 GET /founder-dashboard/team/sales 200 in 27ms
 GET /founder-dashboard/team/sales 200 in 9ms
 ✓ Compiled /founder-dashboard/team/developers in 365ms (882 modules)
 GET /founder-dashboard/team/developers 200 in 450ms
 ○ Compiling /founder-dashboard/team/designers ...
 ✓ Compiled /founder-dashboard/team/designers in 553ms (1629 modules)
 GET /founder-dashboard/team/designers 200 in 613ms
 ○ Compiling /founder-dashboard/team/media-buyers ...
 ✓ Compiled /founder-dashboard/team/media-buyers in 623ms (1640 modules)
 GET /founder-dashboard/team/media-buyers 200 in 654ms
 GET /founder-dashboard/team/media-buyers 200 in 145ms
 ✓ Compiled in 584ms (1617 modules)
 GET /founder-dashboard/team/media-buyers 200 in 28ms
 ○ Compiling /founder-dashboard/finance/revenue ...
 ✓ Compiled /founder-dashboard/finance/revenue in 650ms (1613 modules)
 GET /founder-dashboard/finance/revenue 200 in 719ms
 ✓ Compiled in 1162ms (1627 modules)
 GET /founder-dashboard/finance/revenue 200 in 211ms
 ○ Compiling /founder-dashboard/finance/expenses ...
 ✓ Compiled /founder-dashboard/finance/expenses in 949ms (1622 modules)
 GET /founder-dashboard/finance/expenses 200 in 987ms
 ○ Compiling /founder-dashboard ...
 ✓ Compiled /founder-dashboard in 548ms (881 modules)
 GET /founder-dashboard 200 in 619ms
CurrencyService: Getting immediate exchange rates
Cache read error: ReferenceError: localStorage is not defined
    at CurrencyConversionService.getCachedRates (lib/services/currency-conversion.ts:343:21)
    at CurrencyConversionService.getExchangeRatesImmediate (lib/services/currency-conversion.ts:107:28)
    at useCurrencyStore.name (lib/stores/currency-store.ts:40:47)
    at eval (lib/stores/currency-store.ts:36:2)
    at (ssr)/./lib/stores/currency-store.ts (.next/server/app/founder-dashboard/page.js:622:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./components/ui/currency-icon.tsx:13:84)
    at (ssr)/./components/ui/currency-icon.tsx (.next/server/app/founder-dashboard/page.js:490:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./app/founder-dashboard/page.tsx:30:87)
    at (ssr)/./app/founder-dashboard/page.tsx (.next/server/app/founder-dashboard/page.js:314:1)
    at Object.__webpack_require__ [as require] (.next/server/webpack-runtime.js:33:43)
    at JSON.parse (<anonymous>)
  341 |   private getCachedRates(): CurrencyData | null {
  342 |     try {
> 343 |       const cached = localStorage.getItem(this.CACHE_KEY);
      |                     ^
  344 |       if (!cached) return null;
  345 |       
  346 |       return JSON.parse(cached);
CurrencyService: Using default rates immediately
 GET /founder-dashboard 200 in 268ms
 ✓ Compiled in 650ms (1643 modules)
 GET /founder-dashboard 200 in 74ms
 ✓ Compiled /founder-dashboard/clients in 208ms (857 modules)
 GET /founder-dashboard/clients 200 in 260ms
 ○ Compiling /founder-dashboard/clients/[id] ...
 ✓ Compiled /founder-dashboard/clients/[id] in 929ms (1621 modules)
 GET /founder-dashboard/clients/1 200 in 1409ms
 GET /founder-dashboard/clients/1 200 in 10ms
 ✓ Compiled /founder-dashboard/projects in 253ms (875 modules)
 GET /founder-dashboard/projects 200 in 328ms
 ○ Compiling /founder-dashboard/projects/new ...
 ✓ Compiled /founder-dashboard/projects/new in 536ms (898 modules)
 GET /founder-dashboard/projects/new 200 in 609ms
 ✓ Compiled in 1029ms (1664 modules)
 GET /founder-dashboard/projects/new 200 in 94ms
 ○ Compiling /founder-dashboard/tasks ...
 ✓ Compiled /founder-dashboard/tasks in 1062ms (919 modules)
 GET /founder-dashboard/tasks 200 in 1119ms
 GET /founder-dashboard/tasks 200 in 60ms
 ✓ Compiled /founder-dashboard/team/sales in 290ms (926 modules)
 GET /founder-dashboard/team/sales 200 in 340ms
 GET /founder-dashboard/team/sales 200 in 14ms
 GET /founder-dashboard/team/sales 200 in 158ms
 ✓ Compiled in 1027ms (1665 modules)
 GET /founder-dashboard/team/sales 200 in 57ms
 ✓ Compiled in 2.4s (1629 modules)
 GET /founder-dashboard/team/sales 200 in 1210ms
 ○ Compiling /founder-dashboard/demo ...
 ✓ Compiled /founder-dashboard/demo in 930ms (1635 modules)
 GET /founder-dashboard/demo 200 in 1007ms
[zustand devtools middleware] Please install/enable Redux devtools extension
 GET /founder-dashboard/tasks 200 in 193ms
 GET /founder-dashboard/demo 200 in 48ms
 ○ Compiling /founder-dashboard/settings ...
 ✓ Compiled /founder-dashboard/settings in 874ms (1651 modules)
 GET /founder-dashboard/settings 200 in 948ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET /founder-dashboard/demo 200 in 96ms
 GET /founder-dashboard/clients 200 in 19ms
 ✓ Compiled /founder-dashboard/clients/[id] in 320ms (877 modules)
 GET /founder-dashboard/clients/1 200 in 419ms
 ✓ Compiled /founder-dashboard/projects in 447ms (894 modules)
 GET /founder-dashboard/projects 200 in 552ms
 GET /founder-dashboard/tasks 200 in 26ms
 GET /founder-dashboard/team/sales 200 in 33ms
 ✓ Compiled /founder-dashboard/team/media-buyers in 351ms (901 modules)
 GET /founder-dashboard/team/media-buyers 200 in 397ms
 GET /founder-dashboard/team/media-buyers 200 in 265ms
 GET /founder-dashboard/demo 200 in 39ms
 ○ Compiling /_not-found ...
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /_not-found in 4.4s (1622 modules)
 GET /founder-dashboard/team/media-buyers 200 in 3549ms
 GET /founder-dashboard/team/media-buyers 200 in 1890ms
 ✓ Compiled in 1122ms (1608 modules)
 ○ Compiling /founder-dashboard ...
 ✓ Compiled /founder-dashboard in 778ms (860 modules)
 GET /founder-dashboard 200 in 824ms
 GET /founder-dashboard 200 in 9ms
 ✓ Compiled /founder-dashboard/clients in 482ms (856 modules)
 GET /founder-dashboard/clients 200 in 519ms
 GET /founder-dashboard/clients 200 in 9ms
 GET /founder-dashboard 200 in 32ms
CurrencyService: Getting immediate exchange rates
Cache read error: ReferenceError: localStorage is not defined
    at CurrencyConversionService.getCachedRates (lib/services/currency-conversion.ts:343:21)
    at CurrencyConversionService.getExchangeRatesImmediate (lib/services/currency-conversion.ts:107:28)
    at useCurrencyStore.name (lib/stores/currency-store.ts:40:47)
    at eval (lib/stores/currency-store.ts:36:2)
    at (ssr)/./lib/stores/currency-store.ts (.next/server/app/founder-dashboard/page.js:622:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./components/ui/currency-icon.tsx:13:84)
    at (ssr)/./components/ui/currency-icon.tsx (.next/server/app/founder-dashboard/page.js:490:1)
    at __webpack_require__ (.next/server/webpack-runtime.js:33:43)
    at eval (webpack-internal:///(ssr)/./app/founder-dashboard/page.tsx:30:87)
    at (ssr)/./app/founder-dashboard/page.tsx (.next/server/app/founder-dashboard/page.js:314:1)
    at Object.__webpack_require__ [as require] (.next/server/webpack-runtime.js:33:43)
    at JSON.parse (<anonymous>)
  341 |   private getCachedRates(): CurrencyData | null {
  342 |     try {
> 343 |       const cached = localStorage.getItem(this.CACHE_KEY);
      |                     ^
  344 |       if (!cached) return null;
  345 |       
  346 |       return JSON.parse(cached);
CurrencyService: Using default rates immediately
 GET /founder-dashboard 200 in 222ms
 ELIFECYCLE  Command failed.
