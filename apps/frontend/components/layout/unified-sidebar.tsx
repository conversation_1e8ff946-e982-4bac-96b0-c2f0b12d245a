'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useAuthStore } from '@/lib/stores/auth-store';
import { cn } from '@/lib/utils';
import { UserRole } from '@mtbrmg/shared';
import {
    BarChart3,
    CheckSquare,
    ChevronDown,
    ChevronRight,
    Code,
    Crown,
    DollarSign,
    FileText,
    FolderOpen,
    Palette,
    PiggyBank,
    Receipt,
    Settings,
    Target,
    TrendingDown,
    TrendingUp,
    UserCheck,
    Users
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: UserRole[];
  badge?: string;
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    name: 'لوحة تحكم المؤسس',
    href: '/founder-dashboard',
    icon: Crown,
    roles: [UserRole.ADMIN, UserRole.DEVELOPER], // Allow both admin and developer for founder
    badge: 'مؤسس',
  },
  {
    name: 'إدارة العملاء',
    href: '/founder-dashboard/clients',
    icon: Users,
    roles: [UserRole.ADMIN, UserRole.SALES_MANAGER],
  },
  {
    name: 'إدارة المشاريع',
    href: '/founder-dashboard/projects',
    icon: FolderOpen,
    roles: [UserRole.ADMIN, UserRole.SALES_MANAGER, UserRole.DEVELOPER],
  },
  {
    name: 'إدارة المهام',
    href: '/founder-dashboard/tasks',
    icon: CheckSquare,
    roles: [UserRole.ADMIN, UserRole.SALES_MANAGER, UserRole.DEVELOPER, UserRole.DESIGNER],
  },
  {
    name: 'إدارة الفريق',
    href: '/founder-dashboard/team',
    icon: UserCheck,
    roles: [UserRole.ADMIN],
    children: [
      {
        name: 'فريق المبيعات',
        href: '/founder-dashboard/team/sales',
        icon: TrendingUp,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'فريق المطورين',
        href: '/founder-dashboard/team/developers',
        icon: Code,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'فريق مصممي الويب',
        href: '/founder-dashboard/team/designers',
        icon: Palette,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'فريق مشتري الإعلانات',
        href: '/founder-dashboard/team/media-buyers',
        icon: Target,
        roles: [UserRole.ADMIN],
      },
    ],
  },
  {
    name: 'الشؤون المالية والمحاسبة',
    href: '/founder-dashboard/finance',
    icon: DollarSign,
    roles: [UserRole.ADMIN],
    children: [
      {
        name: 'إدارة الإيرادات',
        href: '/founder-dashboard/finance/revenue',
        icon: TrendingUp,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'تتبع المصروفات',
        href: '/founder-dashboard/finance/expenses',
        icon: TrendingDown,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'تحليل التدفق النقدي',
        href: '/founder-dashboard/finance/cash-flow',
        icon: Receipt,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'التقارير المالية',
        href: '/founder-dashboard/finance/reports',
        icon: FileText,
        roles: [UserRole.ADMIN],
      },
      {
        name: 'تخطيط الميزانية',
        href: '/founder-dashboard/finance/budget',
        icon: PiggyBank,
        roles: [UserRole.ADMIN],
      },
    ],
  },
  {
    name: 'التقارير والإحصائيات',
    href: '/founder-dashboard/analytics',
    icon: BarChart3,
    roles: [UserRole.ADMIN, UserRole.SALES_MANAGER],
  },
  {
    name: 'الإعدادات',
    href: '/founder-dashboard/settings',
    icon: Settings,
    roles: [UserRole.ADMIN],
  },
];

interface UnifiedSidebarProps {
  className?: string;
}

export function UnifiedSidebar({ className }: UnifiedSidebarProps) {
  const pathname = usePathname();
  const { user } = useAuthStore();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const hasAccess = (roles: UserRole[]) => {
    if (!user) return false;
    return roles.includes(user.role as UserRole);
  };

  const isActive = (href: string) => {
    if (href === '/founder-dashboard') {
      return pathname === '/founder-dashboard';
    }
    return pathname.startsWith(href);
  };

  const filteredNavigation = navigationItems.filter(item => hasAccess(item.roles));

  // Debug logging (remove in production)
  console.log('Sidebar Debug:', {
    user: user ? { username: user.username, role: user.role } : null,
    isAuthenticated: !!user,
    totalNavigationItems: navigationItems.length,
    filteredNavigationItems: filteredNavigation.length,
    filteredItems: filteredNavigation.map(item => item.name)
  });

  return (
    <div className={cn("h-full flex flex-col bg-white border-l border-gray-200", className)}>
      {/* Logo Section - Enhanced for Mobile */}
      <div className="p-4 sm:p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="relative w-10 h-10 sm:w-12 sm:h-12 transition-all duration-300 hover:scale-110 touch-target">
            <Image
              src="/the_logo.png"
              alt="MTBRMG ERP Logo"
              fill
              className="object-contain animate-logo-glow hover:animate-logo-pulse"
              priority
            />
          </div>
          <div className="flex-1 min-w-0">
            <h1 className="text-base sm:text-lg font-bold text-gray-900 mobile-truncate">MTBRMG ERP</h1>
            <p className="text-xs sm:text-sm text-gray-500 mobile-truncate">نظام إدارة الوكالة</p>
          </div>
        </div>
      </div>

      {/* Navigation - Enhanced for Mobile */}
      <nav className="flex-1 p-4 sm:p-6 space-y-2 overflow-y-auto mobile-container">
        {filteredNavigation.map((item) => {
          const isItemActive = isActive(item.href);
          const isExpanded = expandedItems.includes(item.name);
          const hasChildren = item.children && item.children.length > 0;

          if (hasChildren) {
            return (
              <Collapsible key={item.name} open={isExpanded} onOpenChange={() => toggleExpanded(item.name)}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-between px-3 sm:px-4 py-3 text-sm font-medium rounded-lg transition-colors min-h-[44px] touch-target",
                      isItemActive
                        ? "bg-blue-50 text-blue-700"
                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon className="h-5 w-5" />
                      <span>{item.name}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1 mt-1">
                  {item.children?.filter(child => hasAccess(child.roles)).map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={cn(
                        "flex items-center gap-3 px-3 sm:px-4 py-2 mr-4 sm:mr-6 text-sm font-medium rounded-lg transition-colors min-h-[40px] touch-target",
                        isActive(child.href)
                          ? "bg-blue-50 text-blue-700"
                          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      )}
                    >
                      <child.icon className="h-4 w-4" />
                      <span>{child.name}</span>
                    </Link>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            );
          }

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center gap-3 px-3 sm:px-4 py-3 text-sm font-medium rounded-lg transition-colors min-h-[44px] touch-target",
                isItemActive
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              )}
            >
              <item.icon className="h-5 w-5" />
              <span>{item.name}</span>
              {item.badge && (
                <Badge variant="secondary" className="text-xs">
                  {item.badge}
                </Badge>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Quick Stats Section - Enhanced for Mobile */}
      <div className="p-4 sm:p-6 border-t border-gray-200">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-3 sm:p-4 border border-blue-100">
          <h3 className="text-xs sm:text-sm font-semibold text-gray-900 mb-3 sm:mb-4">الإحصائيات السريعة</h3>
          <div className="space-y-2 sm:space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-xs sm:text-sm text-gray-600 mobile-truncate">المشاريع النشطة</span>
              <span className="text-xs sm:text-sm font-bold text-blue-600">12</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs sm:text-sm text-gray-600 mobile-truncate">المهام المعلقة</span>
              <span className="text-xs sm:text-sm font-bold text-orange-600">8</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs sm:text-sm text-gray-600 mobile-truncate">العملاء الجدد</span>
              <span className="text-xs sm:text-sm font-bold text-green-600">3</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
