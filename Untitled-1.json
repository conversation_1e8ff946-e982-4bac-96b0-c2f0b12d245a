{"knownSymbols": {"formatCurrency": [{"name": "formatCurrency", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}, {"name": "formatCurrency", "module": "@mtbrmg/shared", "isDefault": false}], "formatDate": [{"name": "formatDate", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "formatRelativeTime": [{"name": "formatRelativeTime", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}, {"name": "formatRelativeTime", "module": "@mtbrmg/shared", "isDefault": false}], "validateEgyptianPhone": [{"name": "validateEgyptianPhone", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "getInitials": [{"name": "getInitials", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "calculateProgress": [{"name": "calculateProgress", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "getStatusColor": [{"name": "getStatusColor", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}, {"name": "getStatusColor", "module": "@mtbrmg/shared", "isDefault": false}], "debounce": [{"name": "debounce", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "generateId": [{"name": "generateId", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "truncateText": [{"name": "truncateText", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/utils.ts", "type": "const", "isDefault": false}], "ResponsiveTable": [{"name": "ResponsiveTable", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/responsive-table.tsx", "type": "function", "isDefault": true}], "useState": [{"name": "useState", "module": "react", "isDefault": false}], "ChevronDown": [{"name": "ChevronDown", "module": "lucide-react", "isDefault": false}], "ChevronLeft": [{"name": "ChevronLeft", "module": "lucide-react", "isDefault": false}], "ChevronRight": [{"name": "ChevronRight", "module": "lucide-react", "isDefault": false}], "Download": [{"name": "Download", "module": "lucide-react", "isDefault": false}], "Filter": [{"name": "Filter", "module": "lucide-react", "isDefault": false}], "MoreHorizontal": [{"name": "MoreHorizontal", "module": "lucide-react", "isDefault": false}], "Search": [{"name": "Search", "module": "lucide-react", "isDefault": false}], "Button": [{"name": "<PERSON><PERSON>", "module": "@/components/ui/button", "isDefault": false}], "Input": [{"name": "Input", "module": "@/components/ui/input", "isDefault": false}], "Badge": [{"name": "Badge", "module": "@/components/ui/badge", "isDefault": false}], "Card": [{"name": "Card", "module": "@/components/ui/card", "isDefault": false}], "DropdownMenu": [{"name": "DropdownMenu", "module": "@/components/ui/dropdown-menu", "isDefault": false}], "DropdownMenuContent": [{"name": "DropdownMenuContent", "module": "@/components/ui/dropdown-menu", "isDefault": false}], "DropdownMenuItem": [{"name": "DropdownMenuItem", "module": "@/components/ui/dropdown-menu", "isDefault": false}], "DropdownMenuTrigger": [{"name": "DropdownMenuTrigger", "module": "@/components/ui/dropdown-menu", "isDefault": false}], "Table": [{"name": "Table", "module": "@/components/ui/table", "isDefault": false}], "TableBody": [{"name": "TableBody", "module": "@/components/ui/table", "isDefault": false}], "TableCell": [{"name": "TableCell", "module": "@/components/ui/table", "isDefault": false}], "TableHead": [{"name": "TableHead", "module": "@/components/ui/table", "isDefault": false}], "TableHeader": [{"name": "TableHeader", "module": "@/components/ui/table", "isDefault": false}], "TableRow": [{"name": "TableRow", "module": "@/components/ui/table", "isDefault": false}], "ExchangeRates": [{"name": "ExchangeRates", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/services/currency-conversion.ts", "type": "interface", "isDefault": false}], "CurrencyData": [{"name": "CurrencyData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/services/currency-conversion.ts", "type": "interface", "isDefault": false}], "ConversionResult": [{"name": "ConversionResult", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/services/currency-conversion.ts", "type": "interface", "isDefault": false}], "currencyConversionService": [{"name": "currencyConversionService", "module": "@/lib/services/currency-conversion", "isDefault": false}, {"name": "currencyConversionService", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/services/currency-conversion.ts", "type": "const", "isDefault": false}], "Commission": [{"name": "Commission", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "CommissionRule": [{"name": "CommissionRule", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "CommissionPayment": [{"name": "CommissionPayment", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "CommissionStats": [{"name": "CommissionStats", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "CreateCommissionData": [{"name": "CreateCommissionData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "CreateCommissionPaymentData": [{"name": "CreateCommissionPaymentData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "BulkApprovalData": [{"name": "BulkApprovalData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "interface", "isDefault": false}], "commissionsAPI": [{"name": "commissionsAPI", "module": "@/lib/api", "isDefault": false}, {"name": "commissionsAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api/commissions.ts", "type": "const", "isDefault": false}], "axios": [{"name": "axios", "module": "axios", "isDefault": true}], "taskKeys": [{"name": "task<PERSON>eys", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "const", "isDefault": false}], "useTasks": [{"name": "useTasks", "module": "@/lib/hooks/use-tasks", "isDefault": false}, {"name": "useTasks", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useTask": [{"name": "useTask", "module": "@/lib/hooks/use-tasks", "isDefault": false}, {"name": "useTask", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useTasksByProject": [{"name": "useTasksByProject", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useTasksByAssignee": [{"name": "useTasksByAssignee", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useTaskStats": [{"name": "useTaskStats", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useCreateTask": [{"name": "useCreateTask", "module": "@/lib/hooks/use-tasks", "isDefault": false}, {"name": "useCreateTask", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useUpdateTask": [{"name": "useUpdateTask", "module": "@/lib/hooks/use-tasks", "isDefault": false}, {"name": "useUpdateTask", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useDeleteTask": [{"name": "useDeleteTask", "module": "@/lib/hooks/use-tasks", "isDefault": false}, {"name": "useDeleteTask", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "useTaskManagement": [{"name": "useTaskManagement", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-tasks.ts", "type": "function", "isDefault": false}], "React": [{"name": "React", "module": "react", "isDefault": true, "asDefinition": "* as React"}], "useQuery": [{"name": "useQuery", "module": "@tanstack/react-query", "isDefault": false}], "useMutation": [{"name": "useMutation", "module": "@tanstack/react-query", "isDefault": false}], "useQueryClient": [{"name": "useQueryClient", "module": "@tanstack/react-query", "isDefault": false}], "tasksAPI": [{"name": "tasksAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/api.ts", "type": "const", "isDefault": false}, {"name": "tasksAPI", "module": "@/lib/api", "isDefault": false}], "useTaskStore": [{"name": "useTaskStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "const", "isDefault": false}, {"name": "useTaskStore", "module": "@/lib/stores/app-store", "isDefault": false}], "toast": [{"name": "toast", "module": "sonner", "isDefault": false}], "type { Task": [{"name": "type { Task", "module": "@mtbrmg/shared", "isDefault": true}], "clientKeys": [{"name": "clientKeys", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "const", "isDefault": false}], "useClients": [{"name": "useClients", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useClient": [{"name": "useClient", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useClientStats": [{"name": "useClientStats", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useCreateClient": [{"name": "useCreateClient", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useUpdateClient": [{"name": "useUpdateClient", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useDeleteClient": [{"name": "useDeleteClient", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useClientCommunications": [{"name": "useClientCommunications", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "useClientManagement": [{"name": "useClientManagement", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-clients.ts", "type": "function", "isDefault": false}], "clientsAPI": [{"name": "clientsAPI", "module": "@/lib/api", "isDefault": false}, {"name": "clientsAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/api.ts", "type": "const", "isDefault": false}], "useClientStore": [{"name": "useClientStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "const", "isDefault": false}, {"name": "useClientStore", "module": "@/lib/stores/app-store", "isDefault": false}], "type { Client": [{"name": "type { Client", "module": "@mtbrmg/shared", "isDefault": true}], "projectKeys": [{"name": "projectKeys", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "const", "isDefault": false}], "useProjects": [{"name": "useProjects", "module": "@/lib/hooks/use-projects", "isDefault": false}, {"name": "useProjects", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useProject": [{"name": "useProject", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useProjectsByClient": [{"name": "useProjectsByClient", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useProjectStats": [{"name": "useProjectStats", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useCreateProject": [{"name": "useCreateProject", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useUpdateProject": [{"name": "useUpdateProject", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useDeleteProject": [{"name": "useDeleteProject", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "useProjectManagement": [{"name": "useProjectManagement", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/hooks/use-projects.ts", "type": "function", "isDefault": false}], "projectsAPI": [{"name": "projectsAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/api.ts", "type": "const", "isDefault": false}, {"name": "projectsAPI", "module": "@/lib/api", "isDefault": false}], "useProjectStore": [{"name": "useProjectStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "const", "isDefault": false}, {"name": "useProjectStore", "module": "@/lib/stores/app-store", "isDefault": false}], "type { Project": [{"name": "type { Project", "module": "@mtbrmg/shared", "isDefault": true}], "authAPI": [{"name": "authAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/api.ts", "type": "const", "isDefault": false}], "usersAPI": [{"name": "usersAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/api.ts", "type": "const", "isDefault": false}, {"name": "usersAPI", "module": "@/lib/api", "isDefault": false}], "teamAPI": [{"name": "teamAPI", "module": "@/lib/api", "isDefault": false}, {"name": "teamAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api.ts", "type": "const", "isDefault": false}], "financeAPI": [{"name": "financeAPI", "module": "@/lib/api", "isDefault": false}, {"name": "financeAPI", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/api.ts", "type": "const", "isDefault": false}], "api": [{"name": "api", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/api.ts", "isDefault": true}], "LoginData": [{"name": "LoginData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "type", "isDefault": false}, {"name": "LoginData", "module": "@mtbrmg/shared", "isDefault": false}], "RegisterData": [{"name": "RegisterData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "type", "isDefault": false}, {"name": "RegisterData", "module": "@mtbrmg/shared", "isDefault": false}], "TokenResponse": [{"name": "TokenResponse", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "type", "isDefault": false}, {"name": "TokenResponse", "module": "@mtbrmg/shared", "isDefault": false}], "User": [{"name": "User", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "type", "isDefault": false}, {"name": "User", "module": "@mtbrmg/shared", "isDefault": false}], "useAuthStore": [{"name": "useAuthStore", "module": "@/lib/stores/auth-store", "isDefault": false}, {"name": "useAuthStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/stores/auth-store.ts", "type": "const", "isDefault": false}], "useUserRole": [{"name": "useUserRole", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/stores/auth-store.ts", "type": "const", "isDefault": false}], "initializeAuth": [{"name": "initializeAuth", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/lib/stores/auth-store.ts", "type": "const", "isDefault": false}, {"name": "initializeAuth", "module": "@/lib/stores/auth-store", "isDefault": false}], "create": [{"name": "create", "module": "zustand", "isDefault": false}], "persist": [{"name": "persist", "module": "zustand/middleware", "isDefault": false}], "AppState": [{"name": "AppState", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "interface", "isDefault": false}], "useAppStore": [{"name": "useAppStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "const", "isDefault": false}], "useUIStore": [{"name": "useUIStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "const", "isDefault": false}], "initializeAppStore": [{"name": "initializeAppStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/app-store.ts", "type": "const", "isDefault": false}], "devtools": [{"name": "devtools", "module": "zustand/middleware", "isDefault": false}], "ClientFilters": [{"name": "ClientFilters", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/client-slice.ts", "type": "interface", "isDefault": false}], "ClientState": [{"name": "ClientState", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/client-slice.ts", "type": "interface", "isDefault": false}], "createClientSlice": [{"name": "createClientSlice", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/client-slice.ts", "type": "const", "isDefault": false}], "StateCreator": [{"name": "StateCreator", "module": "zustand", "isDefault": false}], "Client": [{"name": "Client", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "type", "isDefault": false}, {"name": "Client", "module": "@mtbrmg/shared", "isDefault": false}], "UIState": [{"name": "UIState", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/ui-slice.ts", "type": "interface", "isDefault": false}], "createUISlice": [{"name": "createUISlice", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/ui-slice.ts", "type": "const", "isDefault": false}], "ProjectFilters": [{"name": "ProjectFilters", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/project-slice.ts", "type": "interface", "isDefault": false}], "ProjectState": [{"name": "ProjectState", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/project-slice.ts", "type": "interface", "isDefault": false}], "createProjectSlice": [{"name": "createProjectSlice", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/project-slice.ts", "type": "const", "isDefault": false}], "Project": [{"name": "Project", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "type", "isDefault": false}, {"name": "Project", "module": "@mtbrmg/shared", "isDefault": false}], "TaskFilters": [{"name": "TaskFilters", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/task-slice.ts", "type": "interface", "isDefault": false}], "TaskState": [{"name": "TaskState", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/task-slice.ts", "type": "interface", "isDefault": false}], "createTaskSlice": [{"name": "createTaskSlice", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/slices/task-slice.ts", "type": "const", "isDefault": false}], "Task": [{"name": "Task", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "type", "isDefault": false}, {"name": "Task", "module": "@mtbrmg/shared", "isDefault": false}], "useCurrencyStore": [{"name": "useCurrencyStore", "module": "@/lib/stores/currency-store", "isDefault": false}, {"name": "useCurrencyStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/currency-store.ts", "type": "const", "isDefault": false}], "initializeCurrencyStore": [{"name": "initializeCurrencyStore", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/currency-store.ts", "type": "const", "isDefault": false}], "useCurrency": [{"name": "useCurrency", "module": "@/lib/stores/currency-store", "isDefault": false}, {"name": "useCurrency", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/stores/currency-store.ts", "type": "const", "isDefault": false}], "CURRENCY_CONFIG": [{"name": "CURRENCY_CONFIG", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}, {"name": "CURRENCY_CONFIG", "module": "@mtbrmg/shared", "isDefault": false}], "cn": [{"name": "cn", "module": "@/lib/utils", "isDefault": false}, {"name": "cn", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/utils.ts", "type": "function", "isDefault": false}], "clsx": [{"name": "clsx", "module": "clsx", "isDefault": false}], "type ClassValue": [{"name": "type ClassValue", "module": "clsx", "isDefault": false}], "twMerge": [{"name": "twMerge", "module": "tailwind-merge", "isDefault": false}], "ExportOptions": [{"name": "ExportOptions", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/dashboard-export.ts", "type": "interface", "isDefault": false}], "DashboardExporter": [{"name": "DashboardExporter", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/dashboard-export.ts", "type": "class", "isDefault": false}], "async": [{"name": "async", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/dashboard-export.ts", "isDefault": false}], "getAvailableExportFormats": [{"name": "getAvailableExportFormats", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/dashboard-export.ts", "type": "function", "isDefault": false}], "getAvailableExportSections": [{"name": "getAvailableExportSections", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/dashboard-export.ts", "type": "function", "isDefault": false}], "DashboardData": [{"name": "DashboardData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/hooks/use-dashboard-data.ts", "type": "interface", "isDefault": false}, {"name": "DashboardData", "module": "@/hooks/use-dashboard-data", "isDefault": false}], "showToast": [{"name": "showToast", "module": "@/lib/toast", "isDefault": false}, {"name": "showToast", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/toast.ts", "type": "const", "isDefault": false}], "DEMO_FOUNDER_CREDS": [{"name": "DEMO_FOUNDER_CREDS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/demo-data.ts", "type": "const", "isDefault": false}, {"name": "DEMO_FOUNDER_CREDS", "module": "@/lib/demo-data", "isDefault": false}], "DEMO_CLIENTS": [{"name": "DEMO_CLIENTS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/demo-data.ts", "type": "const", "isDefault": false}, {"name": "DEMO_CLIENTS", "module": "@/lib/demo-data", "isDefault": false}], "DEMO_PROJECTS": [{"name": "DEMO_PROJECTS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/demo-data.ts", "type": "const", "isDefault": false}, {"name": "DEMO_PROJECTS", "module": "@/lib/demo-data", "isDefault": false}], "DEMO_TASKS": [{"name": "DEMO_TASKS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/demo-data.ts", "type": "const", "isDefault": false}, {"name": "DEMO_TASKS", "module": "@/lib/demo-data", "isDefault": false}], "DEMO_TEAM": [{"name": "DEMO_TEAM", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/demo-data.ts", "type": "const", "isDefault": false}, {"name": "DEMO_TEAM", "module": "@/lib/demo-data", "isDefault": false}], "DEMO_ANALYTICS": [{"name": "DEMO_ANALYTICS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/lib/demo-data.ts", "type": "const", "isDefault": false}, {"name": "DEMO_ANALYTICS", "module": "@/lib/demo-data", "isDefault": false}], "useNavigation": [{"name": "useNavigation", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/hooks/use-navigation.ts", "type": "function", "isDefault": false}], "UserRole": [{"name": "UserRole", "module": "@mtbrmg/shared", "isDefault": false}, {"name": "UserRole", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "enum", "isDefault": false}], "ROLE_PERMISSIONS": [{"name": "ROLE_PERMISSIONS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}, {"name": "ROLE_PERMISSIONS", "module": "@mtbrmg/shared", "isDefault": false}], "DashboardMetrics": [{"name": "DashboardMetrics", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/hooks/use-dashboard-data.ts", "type": "interface", "isDefault": false}], "useDashboardData": [{"name": "useDashboardData", "module": "@/hooks/use-dashboard-data", "isDefault": false}, {"name": "useDashboardData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/hooks/use-dashboard-data.ts", "type": "function", "isDefault": false}], "useEffect": [{"name": "useEffect", "module": "react", "isDefault": false}], "useCallback": [{"name": "useCallback", "module": "react", "isDefault": false}], "reducer": [{"name": "reducer", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/use-toast.ts", "type": "const", "isDefault": false}], "useIsMobile": [{"name": "useIsMobile", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/use-mobile.tsx", "type": "function", "isDefault": false}, {"name": "useIsMobile", "module": "@/hooks/use-mobile", "isDefault": false}], "render": [{"name": "render", "module": "@testing-library/react", "isDefault": false}], "screen": [{"name": "screen", "module": "@testing-library/react", "isDefault": false}], "waitFor": [{"name": "waitFor", "module": "@testing-library/react", "isDefault": false}], "QueryClient": [{"name": "QueryClient", "module": "@tanstack/react-query", "isDefault": false}], "QueryClientProvider": [{"name": "QueryClientProvider", "module": "@tanstack/react-query", "isDefault": false}], "QueryProvider": [{"name": "<PERSON>ry<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/components/query-provider.tsx", "type": "function", "isDefault": false}, {"name": "<PERSON>ry<PERSON><PERSON><PERSON>", "module": "@/components/query-provider", "isDefault": false}], "ReactQueryDevtools": [{"name": "ReactQueryDevtools", "module": "@tanstack/react-query-devtools", "isDefault": false}], "AuthProvider": [{"name": "<PERSON>th<PERSON><PERSON><PERSON>", "module": "@/components/auth-provider", "isDefault": false}, {"name": "<PERSON>th<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/auth-provider.tsx", "type": "function", "isDefault": false}], "DeleteClientDialog": [{"name": "DeleteClientDialog", "module": "@/components/dialogs/delete-client-dialog", "isDefault": false}, {"name": "DeleteClientDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/delete-client-dialog.tsx", "type": "function", "isDefault": false}], "ClientDetailsDialog": [{"name": "ClientDetailsDialog", "module": "@/components/dialogs/client-details-dialog", "isDefault": false}, {"name": "ClientDetailsDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/client-details-dialog.tsx", "type": "function", "isDefault": false}], "CardContent": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/card", "isDefault": false}], "CardDescription": [{"name": "CardDescription", "module": "@/components/ui/card", "isDefault": false}], "CardHeader": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/card", "isDefault": false}], "CardTitle": [{"name": "CardTitle", "module": "@/components/ui/card", "isDefault": false}], "DeleteTaskDialog": [{"name": "DeleteTaskDialog", "module": "@/components/dialogs/delete-task-dialog", "isDefault": false}, {"name": "DeleteTaskDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/delete-task-dialog.tsx", "type": "function", "isDefault": false}], "AlertTriangle": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "lucide-react", "isDefault": false}], "Trash2": [{"name": "Trash2", "module": "lucide-react", "isDefault": false}], "X": [{"name": "X", "module": "lucide-react", "isDefault": false}], "Loader2": [{"name": "Loader2", "module": "lucide-react", "isDefault": false}], "ProjectDetailsDialog": [{"name": "ProjectDetailsDialog", "module": "@/components/dialogs/project-details-dialog", "isDefault": false}, {"name": "ProjectDetailsDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/project-details-dialog.tsx", "type": "function", "isDefault": false}], "Progress": [{"name": "Progress", "module": "@/components/ui/progress", "isDefault": false}], "DeleteProjectDialog": [{"name": "DeleteProjectDialog", "module": "@/components/dialogs/delete-project-dialog", "isDefault": false}, {"name": "DeleteProjectDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/delete-project-dialog.tsx", "type": "function", "isDefault": false}], "TaskDetailsDialog": [{"name": "TaskDetailsDialog", "module": "@/components/dialogs/task-details-dialog", "isDefault": false}, {"name": "TaskDetailsDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/task-details-dialog.tsx", "type": "function", "isDefault": false}], "TeamMemberDetailsDialog": [{"name": "TeamMemberDetailsDialog", "module": "@/components/dialogs/team-member-details-dialog", "isDefault": false}, {"name": "TeamMemberDetailsDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/team-member-details-dialog.tsx", "type": "function", "isDefault": false}], "DeleteTeamMemberDialog": [{"name": "DeleteTeamMemberDialog", "module": "@/components/dialogs/delete-team-member-dialog", "isDefault": false}, {"name": "DeleteTeamMemberDialog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dialogs/delete-team-member-dialog.tsx", "type": "function", "isDefault": false}], "QuickActions": [{"name": "QuickActions", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/quick-actions.tsx", "type": "function", "isDefault": false}], "QuickStats": [{"name": "QuickStats", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/quick-actions.tsx", "type": "function", "isDefault": false}], "RecentActivities": [{"name": "RecentActivities", "module": "@/components/dashboard/quick-actions", "isDefault": false}, {"name": "RecentActivities", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/quick-actions.tsx", "type": "function", "isDefault": false}], "useRouter": [{"name": "useRouter", "module": "next/navigation", "isDefault": false}], "ClickableCard": [{"name": "ClickableCard", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/clickable-card.tsx", "type": "function", "isDefault": false}], "MetricCard": [{"name": "MetricCard", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/clickable-card.tsx", "type": "function", "isDefault": false}], "ProjectCard": [{"name": "ProjectCard", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/clickable-card.tsx", "type": "function", "isDefault": false}], "TaskCard": [{"name": "TaskCard", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/clickable-card.tsx", "type": "function", "isDefault": false}], "TeamMemberCard": [{"name": "TeamMemberCard", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/dashboard/clickable-card.tsx", "type": "function", "isDefault": false}], "ReactNode": [{"name": "ReactNode", "module": "react", "isDefault": false}], "ToastProvider": [{"name": "ToastProvider", "module": "@/components/providers/toast-provider", "isDefault": false}, {"name": "ToastProvider", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/providers/toast-provider.tsx", "type": "function", "isDefault": false}], "Toaster": [{"name": "Toaster", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/toaster.tsx", "type": "function", "isDefault": false}, {"name": "Toaster", "module": "sonner", "isDefault": false}], "UnifiedFooter": [{"name": "UnifiedFooter", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "type": "function", "isDefault": false}], "Link": [{"name": "Link", "module": "next/link", "isDefault": true}], "Image": [{"name": "Image", "module": "next/image", "isDefault": true}], "Heart": [{"name": "Heart", "module": "lucide-react", "isDefault": false}], "ExternalLink": [{"name": "ExternalLink", "module": "lucide-react", "isDefault": false}], "UnifiedHeader": [{"name": "UnifiedHeader", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-header.tsx", "type": "function", "isDefault": false}], "Sheet": [{"name": "Sheet", "module": "@/components/ui/sheet", "isDefault": false}], "SheetContent": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/sheet", "isDefault": false}], "SheetTrigger": [{"name": "Sheet<PERSON><PERSON>ger", "module": "@/components/ui/sheet", "isDefault": false}], "SheetTitle": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/sheet", "isDefault": false}], "UnifiedLayout": [{"name": "UnifiedLayout", "module": "@/components/layout", "isDefault": false}, {"name": "UnifiedLayout", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-layout.tsx", "type": "function", "isDefault": false}], "usePathname": [{"name": "usePathname", "module": "next/navigation", "isDefault": false}], "Collapsible": [{"name": "Collapsible", "module": "@/components/ui/collapsible", "isDefault": false}], "CollapsibleContent": [{"name": "Collapsible<PERSON><PERSON>nt", "module": "@/components/ui/collapsible", "isDefault": false}], "CollapsibleTrigger": [{"name": "CollapsibleTrigger", "module": "@/components/ui/collapsible", "isDefault": false}], "RoleGuard": [{"name": "<PERSON><PERSON><PERSON>", "module": "@/components/auth/role-guard", "isDefault": false}, {"name": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/auth/role-guard.tsx", "type": "function", "isDefault": false}], "EditTeamMemberForm": [{"name": "EditTeamMemberForm", "module": "@/components/forms/edit-team-member-form", "isDefault": false}, {"name": "EditTeamMemberForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/edit-team-member-form.tsx", "type": "function", "isDefault": false}], "Label": [{"name": "Label", "module": "@/components/ui/label", "isDefault": false}], "Textarea": [{"name": "Textarea", "module": "@/components/ui/textarea", "isDefault": false}], "Select": [{"name": "Select", "module": "@/components/ui/select", "isDefault": false}], "SelectContent": [{"name": "SelectContent", "module": "@/components/ui/select", "isDefault": false}], "SelectItem": [{"name": "SelectItem", "module": "@/components/ui/select", "isDefault": false}], "SelectTrigger": [{"name": "SelectTrigger", "module": "@/components/ui/select", "isDefault": false}], "SelectValue": [{"name": "SelectValue", "module": "@/components/ui/select", "isDefault": false}], "TaskFormData": [{"name": "TaskFormData", "module": "@/components/forms/add-task-form", "isDefault": false}, {"name": "TaskFormData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-task-form.tsx", "type": "interface", "isDefault": false}], "AddTaskForm": [{"name": "AddTaskForm", "module": "@/components/forms/add-task-form", "isDefault": false}, {"name": "AddTaskForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-task-form.tsx", "type": "function", "isDefault": false}], "EditClientForm": [{"name": "EditClientForm", "module": "@/components/forms/edit-client-form", "isDefault": false}, {"name": "EditClientForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/edit-client-form.tsx", "type": "function", "isDefault": false}], "ProjectFormData": [{"name": "ProjectFormData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-project-form.tsx", "type": "interface", "isDefault": false}], "AddProjectForm": [{"name": "AddProjectForm", "module": "@/components/forms/add-project-form", "isDefault": false}, {"name": "AddProjectForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-project-form.tsx", "type": "function", "isDefault": false}], "EditTaskForm": [{"name": "EditTaskForm", "module": "@/components/forms/edit-task-form", "isDefault": false}, {"name": "EditTaskForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/edit-task-form.tsx", "type": "function", "isDefault": false}], "UnifiedProjectCreationForm": [{"name": "UnifiedProjectCreationForm", "module": "@/components/forms/unified-project-creation-form", "isDefault": false}, {"name": "UnifiedProjectCreationForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation-form.tsx", "type": "function", "isDefault": false}], "AddTeamMemberForm": [{"name": "AddTeamMemberForm", "module": "@/components/forms/add-team-member-form", "isDefault": false}, {"name": "AddTeamMemberForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-team-member-form.tsx", "type": "function", "isDefault": false}], "TimelineBudgetSection": [{"name": "TimelineBudgetSection", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation/timeline-budget-section.tsx", "type": "function", "isDefault": false}], "Calendar": [{"name": "Calendar", "module": "lucide-react", "isDefault": false}], "Clock": [{"name": "Clock", "module": "lucide-react", "isDefault": false}], "CurrencyIcon": [{"name": "CurrencyIcon", "module": "@/components/ui/currency-icon", "isDefault": false}, {"name": "CurrencyIcon", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-icon.tsx", "type": "function", "isDefault": false}], "TechnicalSpecsSection": [{"name": "TechnicalSpecsSection", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation/technical-specs-section.tsx", "type": "function", "isDefault": false}], "Code": [{"name": "Code", "module": "lucide-react", "isDefault": false}], "Users": [{"name": "Users", "module": "lucide-react", "isDefault": false}], "CredentialsSection": [{"name": "CredentialsSection", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation/credentials-section.tsx", "type": "function", "isDefault": false}], "Lock": [{"name": "Lock", "module": "lucide-react", "isDefault": false}], "Mail": [{"name": "Mail", "module": "lucide-react", "isDefault": false}], "Server": [{"name": "Server", "module": "lucide-react", "isDefault": false}], "Eye": [{"name": "Eye", "module": "lucide-react", "isDefault": false}], "EyeOff": [{"name": "Eye<PERSON>ff", "module": "lucide-react", "isDefault": false}], "ClientSection": [{"name": "ClientSection", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation/client-section.tsx", "type": "function", "isDefault": false}], "Phone": [{"name": "Phone", "module": "lucide-react", "isDefault": false}], "Building": [{"name": "Building", "module": "lucide-react", "isDefault": false}], "Globe": [{"name": "Globe", "module": "lucide-react", "isDefault": false}], "MapPin": [{"name": "MapPin", "module": "lucide-react", "isDefault": false}], "FileText": [{"name": "FileText", "module": "lucide-react", "isDefault": false}], "CreationModeSelector": [{"name": "CreationModeSelector", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation/creation-mode-selector.tsx", "type": "function", "isDefault": false}], "UserPlus": [{"name": "UserPlus", "module": "lucide-react", "isDefault": false}], "ProjectInformationSection": [{"name": "ProjectInformationSection", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/unified-project-creation/project-information-section.tsx", "type": "function", "isDefault": false}], "FolderOpen": [{"name": "FolderOpen", "module": "lucide-react", "isDefault": false}], "ClientFormData": [{"name": "ClientFormData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-client-form.tsx", "type": "interface", "isDefault": false}], "AddClientForm": [{"name": "AddClientForm", "module": "@/components/forms/add-client-form", "isDefault": false}, {"name": "AddClientForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/add-client-form.tsx", "type": "function", "isDefault": false}], "EditProjectForm": [{"name": "EditProjectForm", "module": "@/components/forms/edit-project-form", "isDefault": false}, {"name": "EditProjectForm", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/forms/edit-project-form.tsx", "type": "function", "isDefault": false}], "ArrowLeft": [{"name": "ArrowLeft", "module": "lucide-react", "isDefault": false}], "ArrowRight": [{"name": "ArrowRight", "module": "lucide-react", "isDefault": false}], "LabelPrimitive": [{"name": "LabelPrimitive", "module": "@radix-ui/react-label", "isDefault": true, "asDefinition": "* as LabelPrimitive"}], "Slot": [{"name": "Slot", "module": "@radix-ui/react-slot", "isDefault": false}], "ContextMenuPrimitive": [{"name": "ContextMenuPrimitive", "module": "@radix-ui/react-context-menu", "isDefault": true, "asDefinition": "* as ContextMenuPrimitive"}], "Check": [{"name": "Check", "module": "lucide-react", "isDefault": false}], "Circle": [{"name": "Circle", "module": "lucide-react", "isDefault": false}], "SelectPrimitive": [{"name": "SelectPrimitive", "module": "@radix-ui/react-select", "isDefault": true, "asDefinition": "* as SelectPrimitive"}], "ChevronUp": [{"name": "ChevronUp", "module": "lucide-react", "isDefault": false}], "DropdownMenuPrimitive": [{"name": "DropdownMenuPrimitive", "module": "@radix-ui/react-dropdown-menu", "isDefault": true, "asDefinition": "* as DropdownMenuPrimitive"}], "CurrencyDisplay": [{"name": "CurrencyDisplay", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-display.tsx", "type": "function", "isDefault": false}], "CurrencyInput": [{"name": "CurrencyInput", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-display.tsx", "type": "function", "isDefault": false}], "CurrencySelector": [{"name": "CurrencySelector", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-display.tsx", "type": "function", "isDefault": false}], "CollapsiblePrimitive": [{"name": "CollapsiblePrimitive", "module": "@radix-ui/react-collapsible", "isDefault": true, "asDefinition": "* as CollapsiblePrimitive"}], "CheckboxPrimitive": [{"name": "CheckboxPrimitive", "module": "@radix-ui/react-checkbox", "isDefault": true, "asDefinition": "* as CheckboxPrimitive"}], "ToastPrimitives": [{"name": "ToastPrimitives", "module": "@radix-ui/react-toast", "isDefault": true, "asDefinition": "* as ToastPrimitives"}], "cva": [{"name": "cva", "module": "class-variance-authority", "isDefault": false}], "type VariantProps": [{"name": "type VariantProps", "module": "class-variance-authority", "isDefault": false}], "EgyptianPoundIcon": [{"name": "EgyptianPoundIcon", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-icon.tsx", "type": "function", "isDefault": false}], "USDollarIcon": [{"name": "USDollarIcon", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-icon.tsx", "type": "function", "isDefault": false}], "SaudiRiyalIcon": [{"name": "SaudiRiyalIcon", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-icon.tsx", "type": "function", "isDefault": false}], "UAEDirhamIcon": [{"name": "UAEDirhamIcon", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-icon.tsx", "type": "function", "isDefault": false}], "DollarSign": [{"name": "DollarSign", "module": "lucide-react", "isDefault": false}], "PoundSterling": [{"name": "PoundSterling", "module": "lucide-react", "isDefault": false}], "TogglePrimitive": [{"name": "TogglePrimitive", "module": "@radix-ui/react-toggle", "isDefault": true, "asDefinition": "* as TogglePrimitive"}], "ButtonProps": [{"name": "ButtonProps", "module": "@/components/ui/button", "isDefault": false}, {"name": "ButtonProps", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/button.tsx", "type": "interface", "isDefault": false}], "SeparatorPrimitive": [{"name": "SeparatorPrimitive", "module": "@radix-ui/react-separator", "isDefault": true, "asDefinition": "* as SeparatorPrimitive"}], "VariantProps": [{"name": "VariantProps", "module": "class-variance-authority", "isDefault": false}], "PanelLeft": [{"name": "PanelLeft", "module": "lucide-react", "isDefault": false}], "Separator": [{"name": "Separator", "module": "@/components/ui/separator", "isDefault": false}], "Skeleton": [{"name": "Skeleton", "module": "@/components/ui/skeleton", "isDefault": false}], "BadgeProps": [{"name": "BadgeProps", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/badge.tsx", "type": "interface", "isDefault": false}], "DialogPrimitive": [{"name": "DialogPrimitive", "module": "@radix-ui/react-dialog", "isDefault": true, "asDefinition": "* as DialogPrimitive"}], "ResponsiveBreadcrumb": [{"name": "ResponsiveBreadcrumb", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/responsive-breadcrumb.tsx", "type": "function", "isDefault": false}], "all": [{"name": "all", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/responsive-breadcrumb.tsx", "isDefault": false}], "QuickBreadcrumb": [{"name": "QuickBreadcrumb", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/responsive-breadcrumb.tsx", "type": "function", "isDefault": false}], "BREADCRUMB_PATTERNS": [{"name": "BREADCRUMB_PATTERNS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/responsive-breadcrumb.tsx", "type": "const", "isDefault": false}], "createBreadcrumbPath": [{"name": "createBreadcrumbPath", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/responsive-breadcrumb.tsx", "type": "function", "isDefault": false}], "FounderBreadcrumb": [{"name": "FounderBreadcrumb", "module": "@/components/ui/responsive-breadcrumb", "isDefault": false}, {"name": "FounderBreadcrumb", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/responsive-breadcrumb.tsx", "type": "function", "isDefault": false}], "MenubarPrimitive": [{"name": "MenubarPrimitive", "module": "@radix-ui/react-menubar", "isDefault": true, "asDefinition": "* as MenubarPrimitive"}], "AvatarPrimitive": [{"name": "AvatarPrimitive", "module": "@radix-ui/react-avatar", "isDefault": true, "asDefinition": "* as AvatarPrimitive"}], "ToggleGroupPrimitive": [{"name": "ToggleGroupPrimitive", "module": "@radix-ui/react-toggle-group", "isDefault": true, "asDefinition": "* as ToggleGroupPrimitive"}], "toggleVariants": [{"name": "toggleVariants", "module": "@/components/ui/toggle", "isDefault": false}], "type DialogProps": [{"name": "type DialogProps", "module": "@radix-ui/react-dialog", "isDefault": false}], "Command as CommandPrimitive": [{"name": "Command as CommandPrimitive", "module": "cmdk", "isDefault": false}], "Dialog": [{"name": "Dialog", "module": "@/components/ui/dialog", "isDefault": false}], "DialogContent": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/dialog", "isDefault": false}], "RadioGroupPrimitive": [{"name": "RadioGroupPrimitive", "module": "@radix-ui/react-radio-group", "isDefault": true, "asDefinition": "* as RadioGroupPrimitive"}], "CalendarProps": [{"name": "CalendarProps", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/calendar.tsx", "type": "type", "isDefault": false}], "DayPicker": [{"name": "DayPicker", "module": "react-day-picker", "isDefault": false}], "buttonVariants": [{"name": "buttonVariants", "module": "@/components/ui/button", "isDefault": false}], "SwitchPrimitives": [{"name": "SwitchPrimitives", "module": "@radix-ui/react-switch", "isDefault": true, "asDefinition": "* as SwitchPrimitives"}], "TooltipPrimitive": [{"name": "TooltipPrimitive", "module": "@radix-ui/react-tooltip", "isDefault": true, "asDefinition": "* as TooltipPrimitive"}], "Drawer as DrawerPrimitive": [{"name": "Drawer as DrawerPrimitive", "module": "vaul", "isDefault": false}], "AccordionPrimitive": [{"name": "AccordionPrimitive", "module": "@radix-ui/react-accordion", "isDefault": true, "asDefinition": "* as AccordionPrimitive"}], "NavigationMenuPrimitive": [{"name": "NavigationMenuPrimitive", "module": "@radix-ui/react-navigation-menu", "isDefault": true, "asDefinition": "* as NavigationMenuPrimitive"}], "useTheme": [{"name": "useTheme", "module": "next-themes", "isDefault": false}], "Toaster as Sonner": [{"name": "<PERSON><PERSON> as <PERSON><PERSON>", "module": "sonner", "isDefault": false}], "GripVertical": [{"name": "GripVertical", "module": "lucide-react", "isDefault": false}], "ResizablePrimitive": [{"name": "ResizablePrimitive", "module": "react-resizable-panels", "isDefault": true, "asDefinition": "* as ResizablePrimitive"}], "ScrollAreaPrimitive": [{"name": "ScrollAreaPrimitive", "module": "@radix-ui/react-scroll-area", "isDefault": true, "asDefinition": "* as ScrollAreaPrimitive"}], "CurrencyConversionIndicator": [{"name": "CurrencyConversionIndicator", "module": "@/components/ui/currency-conversion-indicator", "isDefault": false}, {"name": "CurrencyConversionIndicator", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-conversion-indicator.tsx", "type": "function", "isDefault": false}], "ConvertedAmountDisplay": [{"name": "ConvertedAmountDisplay", "module": "@/components/ui/currency-conversion-indicator", "isDefault": false}, {"name": "ConvertedAmountDisplay", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/currency-conversion-indicator.tsx", "type": "function", "isDefault": false}], "RefreshCw": [{"name": "RefreshCw", "module": "lucide-react", "isDefault": false}], "CheckCircle": [{"name": "CheckCircle", "module": "lucide-react", "isDefault": false}], "Wifi": [{"name": "Wifi", "module": "lucide-react", "isDefault": false}], "WifiOff": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "lucide-react", "isDefault": false}], "Tooltip": [{"name": "<PERSON><PERSON><PERSON>", "module": "@/components/ui/tooltip", "isDefault": false}], "TooltipContent": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/tooltip", "isDefault": false}], "TooltipProvider": [{"name": "TooltipProvider", "module": "@/components/ui/tooltip", "isDefault": false}], "TooltipTrigger": [{"name": "TooltipTrigger", "module": "@/components/ui/tooltip", "isDefault": false}], "SheetPrimitive": [{"name": "SheetPrimitive", "module": "@radix-ui/react-dialog", "isDefault": true, "asDefinition": "* as SheetPrimitive"}], "HoverCardPrimitive": [{"name": "HoverCardPrimitive", "module": "@radix-ui/react-hover-card", "isDefault": true, "asDefinition": "* as HoverCardPrimitive"}], "ChartConfig": [{"name": "ChartConfig", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/ui/chart.tsx", "type": "type", "isDefault": false}], "RechartsPrimitive": [{"name": "RechartsPrimitive", "module": "recharts", "isDefault": true, "asDefinition": "* as RechartsPrimitive"}], "OTPInput": [{"name": "OTPInput", "module": "input-otp", "isDefault": false}], "OTPInputContext": [{"name": "OTPInputContext", "module": "input-otp", "isDefault": false}], "Dot": [{"name": "Dot", "module": "lucide-react", "isDefault": false}], "useToast": [{"name": "useToast", "module": "@/hooks/use-toast", "isDefault": false}], "ProgressPrimitive": [{"name": "ProgressPrimitive", "module": "@radix-ui/react-progress", "isDefault": true, "asDefinition": "* as ProgressPrimitive"}], "PopoverPrimitive": [{"name": "PopoverPrimitive", "module": "@radix-ui/react-popover", "isDefault": true, "asDefinition": "* as PopoverPrimitive"}], "SliderPrimitive": [{"name": "SliderPrimitive", "module": "@radix-ui/react-slider", "isDefault": true, "asDefinition": "* as SliderPrimitive"}], "TabsPrimitive": [{"name": "TabsPrimitive", "module": "@radix-ui/react-tabs", "isDefault": true, "asDefinition": "* as TabsPrimitive"}], "AlertDialogPrimitive": [{"name": "AlertDialogPrimitive", "module": "@radix-ui/react-alert-dialog", "isDefault": true, "asDefinition": "* as AlertDialogPrimitive"}], "AspectRatioPrimitive": [{"name": "AspectRatioPrimitive", "module": "@radix-ui/react-aspect-ratio", "isDefault": true, "asDefinition": "* as AspectRatioPrimitive"}], "ThemeProvider": [{"name": "ThemeProvider", "module": "@/components/theme-provider", "isDefault": false}, {"name": "ThemeProvider", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/theme-provider.tsx", "type": "function", "isDefault": false}], "config": [{"name": "config", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/tailwind.config.ts", "isDefault": true}], "type { Config": [{"name": "type { Config", "module": "tailwindcss", "isDefault": true}], "PageProps": [{"name": "PageProps", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/.next/types/app/founder-dashboard/clients/page.ts", "type": "interface", "isDefault": false}], "LayoutProps": [{"name": "LayoutProps", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/.next/types/app/founder-dashboard/clients/page.ts", "type": "interface", "isDefault": false}], "type { ResolvingMetadata": [{"name": "type { ResolvingMetadata", "module": "next/dist/lib/metadata/types/metadata-interface.js", "isDefault": true}], "ResolvingViewport": [{"name": "ResolvingViewport", "module": "next/dist/lib/metadata/types/metadata-interface.js", "isDefault": true}], "TablePage": [{"name": "TablePage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/table/page.tsx", "type": "function", "isDefault": true}], "HelpPage": [{"name": "HelpPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/help/page.tsx", "type": "function", "isDefault": true}], "Home": [{"name": "Home", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/page.tsx", "type": "function", "isDefault": true}], "LoginPage": [{"name": "LoginPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/login/page.tsx", "type": "function", "isDefault": true}], "useForm": [{"name": "useForm", "module": "react-hook-form", "isDefault": false}], "zodResolver": [{"name": "zodResolver", "module": "@hookform/resolvers/zod", "isDefault": false}], "LoginSchema": [{"name": "LoginSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "const", "isDefault": false}, {"name": "LoginSchema", "module": "@mtbrmg/shared", "isDefault": false}], "Alert": [{"name": "<PERSON><PERSON>", "module": "@/components/ui/alert", "isDefault": false}], "AlertDescription": [{"name": "AlertDescription", "module": "@/components/ui/alert", "isDefault": false}], "CurrencyTestPage": [{"name": "CurrencyTestPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/currency-test/page.tsx", "type": "function", "isDefault": true}], "metadata": [{"name": "metadata", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/layout.tsx", "type": "const", "isDefault": false}], "RootLayout": [{"name": "RootLayout", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/layout.tsx", "type": "function", "isDefault": true}], "type React": [{"name": "type React", "module": "react", "isDefault": true}], "type { Metadata": [{"name": "type { Metadata", "module": "next", "isDefault": true}], "IBM_Plex_Sans_Arabic": [{"name": "IBM_Plex_Sans_Arabic", "module": "next/font/google", "isDefault": false}], "AuthTestPage": [{"name": "AuthTestPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/auth-test/page.tsx", "type": "function", "isDefault": true}], "FounderDashboardPage": [{"name": "FounderDashboardPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/page.tsx", "type": "function", "isDefault": true}], "functionality": [{"name": "functionality", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/cash-flow/page.tsx", "isDefault": false}], "await": [{"name": "await", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/page.tsx", "isDefault": false}], "AnalyticsPage": [{"name": "AnalyticsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/analytics/page.tsx", "type": "function", "isDefault": true}], "BudgetPlanningPage": [{"name": "BudgetPlanningPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/budget/page.tsx", "type": "function", "isDefault": true}], "TeamPage": [{"name": "TeamPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/team/page.tsx", "type": "function", "isDefault": true}], "DevelopersTeamPage": [{"name": "DevelopersTeamPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/team/developers/page.tsx", "type": "function", "isDefault": true}], "DesignersTeamPage": [{"name": "DesignersTeamPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/team/designers/page.tsx", "type": "function", "isDefault": true}], "FinanceDashboardPage": [{"name": "FinanceDashboardPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/page.tsx", "type": "function", "isDefault": true}], "SalesTeamPage": [{"name": "SalesTeamPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/team/sales/page.tsx", "type": "function", "isDefault": true}], "FinancialReportsPage": [{"name": "FinancialReportsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/reports/page.tsx", "type": "function", "isDefault": true}], "CommissionsPage": [{"name": "CommissionsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/team/sales/commissions/page.tsx", "type": "function", "isDefault": true}], "CashFlowAnalysisPage": [{"name": "CashFlowAnalysisPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/cash-flow/page.tsx", "type": "function", "isDefault": true}], "RevenueManagementPage": [{"name": "RevenueManagementPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/revenue/page.tsx", "type": "function", "isDefault": true}], "ExpenseTrackingPage": [{"name": "ExpenseTrackingPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/expenses/page.tsx", "type": "function", "isDefault": true}], "AddCashFlowProjectionPage": [{"name": "AddCashFlowProjectionPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/cash-flow/add/page.tsx", "type": "function", "isDefault": true}], "AddRevenuePage": [{"name": "AddRevenuePage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/revenue/add/page.tsx", "type": "function", "isDefault": true}], "ProjectsPage": [{"name": "ProjectsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/projects/page.tsx", "type": "function", "isDefault": true}], "type ProjectFormData": [{"name": "type ProjectFormData", "module": "@/components/forms/add-project-form", "isDefault": false}], "AddExpensePage": [{"name": "AddExpensePage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/finance/expenses/add/page.tsx", "type": "function", "isDefault": true}], "TasksPage": [{"name": "TasksPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/tasks/page.tsx", "type": "function", "isDefault": true}], "SettingsPage": [{"name": "SettingsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/settings/page.tsx", "type": "function", "isDefault": true}], "Switch": [{"name": "Switch", "module": "@/components/ui/switch", "isDefault": false}], "Tabs": [{"name": "Tabs", "module": "@/components/ui/tabs", "isDefault": false}], "TabsContent": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module": "@/components/ui/tabs", "isDefault": false}], "TabsList": [{"name": "TabsList", "module": "@/components/ui/tabs", "isDefault": false}], "TabsTrigger": [{"name": "TabsTrigger", "module": "@/components/ui/tabs", "isDefault": false}], "NewProjectPage": [{"name": "NewProjectPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/projects/new/page.tsx", "type": "function", "isDefault": true}], "ProjectDetailPage": [{"name": "ProjectDetailPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/projects/[id]/page.tsx", "type": "function", "isDefault": true}], "useParams": [{"name": "useParams", "module": "next/navigation", "isDefault": false}], "TaskDetailPage": [{"name": "TaskDetailPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/tasks/[id]/page.tsx", "type": "function", "isDefault": true}], "DocsPage": [{"name": "DocsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/docs/page.tsx", "type": "function", "isDefault": true}], "ClientsPage": [{"name": "ClientsPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/clients/page.tsx", "type": "function", "isDefault": true}], "type ClientFormData": [{"name": "type ClientFormData", "module": "@/components/forms/add-client-form", "isDefault": false}], "RateVerificationPage": [{"name": "RateVerificationPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/rate-verification/page.tsx", "type": "function", "isDefault": true}], "TestPage": [{"name": "TestPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/test/page.tsx", "type": "function", "isDefault": true}], "PrivacyPage": [{"name": "PrivacyPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/privacy/page.tsx", "type": "function", "isDefault": true}], "ContactPage": [{"name": "ContactPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/contact/page.tsx", "type": "function", "isDefault": true}], "ClientDetailPage": [{"name": "ClientDetailPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/clients/[id]/page.tsx", "type": "function", "isDefault": true}], "EditClientPage": [{"name": "EditClientPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/clients/[id]/edit/page.tsx", "type": "function", "isDefault": true}], "declare": [{"name": "declare", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/dist/types/tasks.d.ts", "isDefault": false}], "z": [{"name": "z", "module": "zod", "isDefault": false}], "CreateClientData": [{"name": "CreateClientData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "type", "isDefault": false}], "UpdateClientData": [{"name": "UpdateClientData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "type", "isDefault": false}], "ClientCommunication": [{"name": "ClientCommunication", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "type", "isDefault": false}], "CreateProjectData": [{"name": "CreateProjectData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "type", "isDefault": false}], "UpdateProjectData": [{"name": "UpdateProjectData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "type", "isDefault": false}], "ApiResponse": [{"name": "ApiResponse", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/index.ts", "type": "interface", "isDefault": false}], "PaginatedResponse": [{"name": "PaginatedResponse", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/index.ts", "type": "interface", "isDefault": false}], "BaseFilter": [{"name": "BaseFilter", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/index.ts", "type": "interface", "isDefault": false}], "DateRangeFilter": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/index.ts", "type": "interface", "isDefault": false}], "Money": [{"name": "Money", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/index.ts", "type": "interface", "isDefault": false}], "FileUpload": [{"name": "FileUpload", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/index.ts", "type": "interface", "isDefault": false}], "CreateTaskData": [{"name": "CreateTaskData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "type", "isDefault": false}], "UpdateTaskData": [{"name": "UpdateTaskData", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "type", "isDefault": false}], "TaskTimeLog": [{"name": "TaskTimeLog", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "type", "isDefault": false}], "TaskComment": [{"name": "TaskComment", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "type", "isDefault": false}], "API_ENDPOINTS": [{"name": "API_ENDPOINTS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}], "GOVERNORATE_LABELS": [{"name": "GOVERNORATE_LABELS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}], "TASK_CATEGORY_HOURS": [{"name": "TASK_CATEGORY_HOURS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}], "DEFAULT_PAGINATION": [{"name": "DEFAULT_PAGINATION", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}], "FILE_UPLOAD_LIMITS": [{"name": "FILE_UPLOAD_LIMITS", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/constants/index.ts", "type": "const", "isDefault": false}], "UserStatus": [{"name": "UserStatus", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "enum", "isDefault": false}], "UserSchema": [{"name": "UserSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "const", "isDefault": false}], "RegisterSchema": [{"name": "RegisterSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "const", "isDefault": false}], "TokenResponseSchema": [{"name": "TokenResponseSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/auth.ts", "type": "const", "isDefault": false}], "TaskPriority": [{"name": "TaskPriority", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "enum", "isDefault": false}], "TaskStatus": [{"name": "TaskStatus", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "enum", "isDefault": false}], "TaskCategory": [{"name": "TaskCategory", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "enum", "isDefault": false}], "TaskSchema": [{"name": "TaskSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "const", "isDefault": false}], "CreateTaskSchema": [{"name": "CreateTaskSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "const", "isDefault": false}], "UpdateTaskSchema": [{"name": "UpdateTaskSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "const", "isDefault": false}], "TaskTimeLogSchema": [{"name": "TaskTimeLogSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "const", "isDefault": false}], "TaskCommentSchema": [{"name": "TaskCommentSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/tasks.ts", "type": "const", "isDefault": false}], "ClientMood": [{"name": "Client<PERSON>ood", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "enum", "isDefault": false}], "EgyptianGovernorate": [{"name": "EgyptianGovernorate", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "enum", "isDefault": false}], "ClientSchema": [{"name": "ClientSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "const", "isDefault": false}], "CreateClientSchema": [{"name": "CreateClientSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "const", "isDefault": false}], "UpdateClientSchema": [{"name": "UpdateClientSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "const", "isDefault": false}], "ClientCommunicationSchema": [{"name": "ClientCommunicationSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/clients.ts", "type": "const", "isDefault": false}], "ProjectStatus": [{"name": "ProjectStatus", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "enum", "isDefault": false}], "ProjectPriority": [{"name": "ProjectPriority", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "enum", "isDefault": false}], "ProjectType": [{"name": "ProjectType", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "enum", "isDefault": false}], "ProjectSchema": [{"name": "ProjectSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "const", "isDefault": false}], "CreateProjectSchema": [{"name": "CreateProjectSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "const", "isDefault": false}], "UpdateProjectSchema": [{"name": "UpdateProjectSchema", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/packages/shared/src/types/projects.ts", "type": "const", "isDefault": false}], "TeamAvatarGrid": [{"name": "TeamAvatarGrid", "module": "@/components/ui/enhanced-avatar", "isDefault": false}], "Suspense": [{"name": "Suspense", "module": "react", "isDefault": false}], "useRef": [{"name": "useRef", "module": "react", "isDefault": false}], "Canvas": [{"name": "<PERSON><PERSON>", "module": "@react-three/fiber", "isDefault": false}], "useFrame": [{"name": "useFrame", "module": "@react-three/fiber", "isDefault": false}], "OrbitControls": [{"name": "OrbitControls", "module": "@react-three/drei", "isDefault": false}], "Text3D": [{"name": "Text3D", "module": "@react-three/drei", "isDefault": false}], "Center": [{"name": "Center", "module": "@react-three/drei", "isDefault": false}], "Float": [{"name": "Float", "module": "@react-three/drei", "isDefault": false}], "THREE": [{"name": "THREE", "module": "three", "isDefault": true, "asDefinition": "* as THREE"}], "DEMO_FINANCIAL_DATA": [{"name": "DEMO_FINANCIAL_DATA", "module": "@/lib/demo-data", "isDefault": false}], "DEMO_TEAM_PERFORMANCE": [{"name": "DEMO_TEAM_PERFORMANCE", "module": "@/lib/demo-data", "isDefault": false}], "EnhancedAvatar": [{"name": "EnhancedAvat<PERSON>", "module": "@/components/ui/enhanced-avatar", "isDefault": false}], "getDepartmentColor": [{"name": "getDepartmentColor", "module": "@/components/ui/enhanced-avatar", "isDefault": false}], "getRoleBadgeText": [{"name": "getRoleBadgeText", "module": "@/components/ui/enhanced-avatar", "isDefault": false}], "Avatar": [{"name": "Avatar", "module": "@/components/ui/avatar", "isDefault": false}], "AvatarFallback": [{"name": "AvatarFallback", "module": "@/components/ui/avatar", "isDefault": false}], "AvatarImage": [{"name": "AvatarImage", "module": "@/components/ui/avatar", "isDefault": false}], "MediaBuyersTeamPage": [{"name": "MediaBuyersTeamPage", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/founder-dashboard/team/media-buyers/page.tsx", "type": "function", "isDefault": true}], "UnifiedSidebar": [{"name": "UnifiedSidebar", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "type": "function", "isDefault": false}], "unstable_cacheLife": [{"name": "unstable_cacheLife", "path": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/.next/types/cache-life.d.ts", "type": "function", "isDefault": false}]}}