{"version": 3, "file": "CurveExtras.js", "sources": ["../../src/curves/CurveExtras.js"], "sourcesContent": ["import { Curve, Vector3 } from 'three'\n\n/**\n * A bunch of parametric curves\n *\n * Formulas collected from various sources\n * http://mathworld.wolfram.com/HeartCurve.html\n * http://en.wikipedia.org/wiki/Viviani%27s_curve\n * http://www.mi.sanu.ac.rs/vismath/taylorapril2011/Taylor.pdf\n * https://prideout.net/blog/old/blog/index.html@p=44.html\n */\n\n// GrannyKnot\n\nclass GrannyKnot extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = 2 * Math.PI * t\n\n    const x = -0.22 * Math.cos(t) - 1.28 * Math.sin(t) - 0.44 * Math.cos(3 * t) - 0.78 * Math.sin(3 * t)\n    const y = -0.1 * Math.cos(2 * t) - 0.27 * Math.sin(2 * t) + 0.38 * Math.cos(4 * t) + 0.46 * Math.sin(4 * t)\n    const z = 0.7 * Math.cos(3 * t) - 0.4 * Math.sin(3 * t)\n\n    return point.set(x, y, z).multiplyScalar(20)\n  }\n}\n\n// HeartCurve\n\nclass HeartCurve extends Curve {\n  constructor(scale = 5) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= 2 * Math.PI\n\n    const x = 16 * Math.pow(Math.sin(t), 3)\n    const y = 13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t)\n    const z = 0\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// Viviani's Curve\n\nclass VivianiCurve extends Curve {\n  constructor(scale = 70) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = t * 4 * Math.PI // normalized to 0..1\n    const a = this.scale / 2\n\n    const x = a * (1 + Math.cos(t))\n    const y = a * Math.sin(t)\n    const z = 2 * a * Math.sin(t / 2)\n\n    return point.set(x, y, z)\n  }\n}\n\n// KnotCurve\n\nclass KnotCurve extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= 2 * Math.PI\n\n    const R = 10\n    const s = 50\n\n    const x = s * Math.sin(t)\n    const y = Math.cos(t) * (R + s * Math.cos(t))\n    const z = Math.sin(t) * (R + s * Math.cos(t))\n\n    return point.set(x, y, z)\n  }\n}\n\n// HelixCurve\n\nclass HelixCurve extends Curve {\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const a = 30 // radius\n    const b = 150 // height\n\n    const t2 = (2 * Math.PI * t * b) / 30\n\n    const x = Math.cos(t2) * a\n    const y = Math.sin(t2) * a\n    const z = b * t\n\n    return point.set(x, y, z)\n  }\n}\n\n// TrefoilKnot\n\nclass TrefoilKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= Math.PI * 2\n\n    const x = (2 + Math.cos(3 * t)) * Math.cos(2 * t)\n    const y = (2 + Math.cos(3 * t)) * Math.sin(2 * t)\n    const z = Math.sin(3 * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// TorusKnot\n\nclass TorusKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const p = 3\n    const q = 4\n\n    t *= Math.PI * 2\n\n    const x = (2 + Math.cos(q * t)) * Math.cos(p * t)\n    const y = (2 + Math.cos(q * t)) * Math.sin(p * t)\n    const z = Math.sin(q * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// CinquefoilKnot\n\nclass CinquefoilKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const p = 2\n    const q = 5\n\n    t *= Math.PI * 2\n\n    const x = (2 + Math.cos(q * t)) * Math.cos(p * t)\n    const y = (2 + Math.cos(q * t)) * Math.sin(p * t)\n    const z = Math.sin(q * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// TrefoilPolynomialKnot\n\nclass TrefoilPolynomialKnot extends Curve {\n  constructor(scale = 10) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = t * 4 - 2\n\n    const x = Math.pow(t, 3) - 3 * t\n    const y = Math.pow(t, 4) - 4 * t * t\n    const z = (1 / 5) * Math.pow(t, 5) - 2 * t\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\nfunction scaleTo(x, y, t) {\n  const r = y - x\n  return t * r + x\n}\n\n// FigureEightPolynomialKnot\n\nclass FigureEightPolynomialKnot extends Curve {\n  constructor(scale = 1) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t = scaleTo(-4, 4, t)\n\n    const x = (2 / 5) * t * (t * t - 7) * (t * t - 10)\n    const y = Math.pow(t, 4) - 13 * t * t\n    const z = (1 / 10) * t * (t * t - 4) * (t * t - 9) * (t * t - 12)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot4a\n\nclass DecoratedTorusKnot4a extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    t *= Math.PI * 2\n\n    const x = Math.cos(2 * t) * (1 + 0.6 * (Math.cos(5 * t) + 0.75 * Math.cos(10 * t)))\n    const y = Math.sin(2 * t) * (1 + 0.6 * (Math.cos(5 * t) + 0.75 * Math.cos(10 * t)))\n    const z = 0.35 * Math.sin(5 * t)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot4b\n\nclass DecoratedTorusKnot4b extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const fi = t * Math.PI * 2\n\n    const x = Math.cos(2 * fi) * (1 + 0.45 * Math.cos(3 * fi) + 0.4 * Math.cos(9 * fi))\n    const y = Math.sin(2 * fi) * (1 + 0.45 * Math.cos(3 * fi) + 0.4 * Math.cos(9 * fi))\n    const z = 0.2 * Math.sin(9 * fi)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot5a\n\nclass DecoratedTorusKnot5a extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const fi = t * Math.PI * 2\n\n    const x = Math.cos(3 * fi) * (1 + 0.3 * Math.cos(5 * fi) + 0.5 * Math.cos(10 * fi))\n    const y = Math.sin(3 * fi) * (1 + 0.3 * Math.cos(5 * fi) + 0.5 * Math.cos(10 * fi))\n    const z = 0.2 * Math.sin(20 * fi)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\n// DecoratedTorusKnot5c\n\nclass DecoratedTorusKnot5c extends Curve {\n  constructor(scale = 40) {\n    super()\n\n    this.scale = scale\n  }\n\n  getPoint(t, optionalTarget = new Vector3()) {\n    const point = optionalTarget\n\n    const fi = t * Math.PI * 2\n\n    const x = Math.cos(4 * fi) * (1 + 0.5 * (Math.cos(5 * fi) + 0.4 * Math.cos(20 * fi)))\n    const y = Math.sin(4 * fi) * (1 + 0.5 * (Math.cos(5 * fi) + 0.4 * Math.cos(20 * fi)))\n    const z = 0.35 * Math.sin(15 * fi)\n\n    return point.set(x, y, z).multiplyScalar(this.scale)\n  }\n}\n\nexport {\n  GrannyKnot,\n  HeartCurve,\n  VivianiCurve,\n  KnotCurve,\n  HelixCurve,\n  TrefoilKnot,\n  TorusKnot,\n  CinquefoilKnot,\n  TrefoilPolynomialKnot,\n  FigureEightPolynomialKnot,\n  DecoratedTorusKnot4a,\n  DecoratedTorusKnot4b,\n  DecoratedTorusKnot5a,\n  DecoratedTorusKnot5c,\n}\n"], "names": [], "mappings": ";AAcA,MAAM,mBAAmB,MAAM;AAAA,EAC7B,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,QAAI,IAAI,KAAK,KAAK;AAElB,UAAM,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AACnG,UAAM,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AAC1G,UAAM,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC;AAEtD,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,EAAE;AAAA,EAC5C;AACH;AAIA,MAAM,mBAAmB,MAAM;AAAA,EAC7B,YAAY,QAAQ,GAAG;AACrB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,SAAK,IAAI,KAAK;AAEd,UAAM,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC;AACtC,UAAM,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;AACvF,UAAM,IAAI;AAEV,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,qBAAqB,MAAM;AAAA,EAC/B,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,QAAI,IAAI,IAAI,KAAK;AACjB,UAAM,IAAI,KAAK,QAAQ;AAEvB,UAAM,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC;AAC7B,UAAM,IAAI,IAAI,KAAK,IAAI,CAAC;AACxB,UAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAEhC,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC;AAAA,EACzB;AACH;AAIA,MAAM,kBAAkB,MAAM;AAAA,EAC5B,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,SAAK,IAAI,KAAK;AAEd,UAAM,IAAI;AACV,UAAM,IAAI;AAEV,UAAM,IAAI,IAAI,KAAK,IAAI,CAAC;AACxB,UAAM,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAC3C,UAAM,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAE3C,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC;AAAA,EACzB;AACH;AAIA,MAAM,mBAAmB,MAAM;AAAA,EAC7B,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,UAAM,IAAI;AACV,UAAM,IAAI;AAEV,UAAM,KAAM,IAAI,KAAK,KAAK,IAAI,IAAK;AAEnC,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI;AACzB,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI;AACzB,UAAM,IAAI,IAAI;AAEd,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC;AAAA,EACzB;AACH;AAIA,MAAM,oBAAoB,MAAM;AAAA,EAC9B,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,SAAK,KAAK,KAAK;AAEf,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AAChD,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AAChD,UAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAExB,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,kBAAkB,MAAM;AAAA,EAC5B,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,UAAM,IAAI;AACV,UAAM,IAAI;AAEV,SAAK,KAAK,KAAK;AAEf,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AAChD,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AAChD,UAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAExB,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,uBAAuB,MAAM;AAAA,EACjC,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,UAAM,IAAI;AACV,UAAM,IAAI;AAEV,SAAK,KAAK,KAAK;AAEf,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AAChD,UAAM,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC;AAChD,UAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAExB,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,8BAA8B,MAAM;AAAA,EACxC,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,QAAI,IAAI,IAAI;AAEZ,UAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI;AAC/B,UAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;AACnC,UAAM,IAAK,IAAI,IAAK,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI;AAEzC,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAEA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACxB,QAAM,IAAI,IAAI;AACd,SAAO,IAAI,IAAI;AACjB;AAIA,MAAM,kCAAkC,MAAM;AAAA,EAC5C,YAAY,QAAQ,GAAG;AACrB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,QAAI,QAAQ,IAAI,GAAG,CAAC;AAEpB,UAAM,IAAK,IAAI,IAAK,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AAC/C,UAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI;AACpC,UAAM,IAAK,IAAI,KAAM,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI;AAE9D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,6BAA6B,MAAM;AAAA,EACvC,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,SAAK,KAAK,KAAK;AAEf,UAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,KAAK,CAAC;AAChF,UAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,KAAK,CAAC;AAChF,UAAM,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AAE/B,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,6BAA6B,MAAM;AAAA,EACvC,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,UAAM,KAAK,IAAI,KAAK,KAAK;AAEzB,UAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,EAAE,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE;AACjF,UAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,EAAE,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE;AACjF,UAAM,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE;AAE/B,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,6BAA6B,MAAM;AAAA,EACvC,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,UAAM,KAAK,IAAI,KAAK,KAAK;AAEzB,UAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AACjF,UAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AACjF,UAAM,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AAEhC,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;AAIA,MAAM,6BAA6B,MAAM;AAAA,EACvC,YAAY,QAAQ,IAAI;AACtB,UAAO;AAEP,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS,GAAG,iBAAiB,IAAI,QAAO,GAAI;AAC1C,UAAM,QAAQ;AAEd,UAAM,KAAK,IAAI,KAAK,KAAK;AAEzB,UAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,EAAE,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AAClF,UAAM,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,IAAI,IAAI,EAAE,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AAClF,UAAM,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE;AAEjC,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,KAAK;AAAA,EACpD;AACH;"}