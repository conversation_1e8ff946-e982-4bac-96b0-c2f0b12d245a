{"version": 3, "file": "ConvexObjectBreaker.js", "sources": ["../../src/misc/ConvexObjectBreaker.js"], "sourcesContent": ["import { Line3, <PERSON>sh, Plane, Vector3 } from 'three'\nimport { ConvexGeometry } from '../geometries/ConvexGeometry'\n\n/**\n * @fileoverview This class can be used to subdivide a convex Geometry object into pieces.\n *\n * Usage:\n *\n * Use the function prepareBreakableObject to prepare a Mesh object to be broken.\n *\n * Then, call the various functions to subdivide the object (subdivideByImpact, cutByPlane)\n *\n * Sub-objects that are product of subdivision don't need prepareBreakableObject to be called on them.\n *\n * Requisites for the object:\n *\n *  - Mesh object must have a buffer geometry and a material\n *\n *  - Vertex normals must be planar (not smoothed)\n *\n *  - The geometry must be convex (this is not checked in the library). You can create convex\n *  geometries with ConvexGeometry. The BoxGeometry, SphereGeometry and other convex primitives\n *  can also be used.\n *\n * Note: This lib adds member variables to object's userData member (see prepareBreakableObject function)\n * Use with caution and read the code when using with other libs.\n *\n * @param {double} minSizeForBreak Min size a debris can have to break.\n * @param {double} smallDelta Max distance to consider that a point belongs to a plane.\n *\n */\n\nconst _v1 = /* @__PURE__ */ new Vector3()\n\nconst ConvexObjectBreaker = /* @__PURE__ */ (() => {\n  class ConvexObjectBreaker {\n    constructor(minSizeForBreak = 1.4, smallDelta = 0.0001) {\n      this.minSizeForBreak = minSizeForBreak\n      this.smallDelta = smallDelta\n\n      this.tempLine1 = new Line3()\n      this.tempPlane1 = new Plane()\n      this.tempPlane2 = new Plane()\n      this.tempPlane_Cut = new Plane()\n      this.tempCM1 = new Vector3()\n      this.tempCM2 = new Vector3()\n      this.tempVector3 = new Vector3()\n      this.tempVector3_2 = new Vector3()\n      this.tempVector3_3 = new Vector3()\n      this.tempVector3_P0 = new Vector3()\n      this.tempVector3_P1 = new Vector3()\n      this.tempVector3_P2 = new Vector3()\n      this.tempVector3_N0 = new Vector3()\n      this.tempVector3_N1 = new Vector3()\n      this.tempVector3_AB = new Vector3()\n      this.tempVector3_CB = new Vector3()\n      this.tempResultObjects = { object1: null, object2: null }\n\n      this.segments = []\n      const n = 30 * 30\n      for (let i = 0; i < n; i++) this.segments[i] = false\n    }\n\n    prepareBreakableObject(object, mass, velocity, angularVelocity, breakable) {\n      // object is a Object3d (normally a Mesh), must have a buffer geometry, and it must be convex.\n      // Its material property is propagated to its children (sub-pieces)\n      // mass must be > 0\n\n      const userData = object.userData\n      userData.mass = mass\n      userData.velocity = velocity.clone()\n      userData.angularVelocity = angularVelocity.clone()\n      userData.breakable = breakable\n    }\n\n    /*\n     * @param {int} maxRadialIterations Iterations for radial cuts.\n     * @param {int} maxRandomIterations Max random iterations for not-radial cuts\n     *\n     * Returns the array of pieces\n     */\n    subdivideByImpact(object, pointOfImpact, normal, maxRadialIterations, maxRandomIterations) {\n      const debris = []\n\n      const tempPlane1 = this.tempPlane1\n      const tempPlane2 = this.tempPlane2\n\n      this.tempVector3.addVectors(pointOfImpact, normal)\n      tempPlane1.setFromCoplanarPoints(pointOfImpact, object.position, this.tempVector3)\n\n      const maxTotalIterations = maxRandomIterations + maxRadialIterations\n\n      const scope = this\n\n      function subdivideRadial(subObject, startAngle, endAngle, numIterations) {\n        if (Math.random() < numIterations * 0.05 || numIterations > maxTotalIterations) {\n          debris.push(subObject)\n\n          return\n        }\n\n        let angle = Math.PI\n\n        if (numIterations === 0) {\n          tempPlane2.normal.copy(tempPlane1.normal)\n          tempPlane2.constant = tempPlane1.constant\n        } else {\n          if (numIterations <= maxRadialIterations) {\n            angle = (endAngle - startAngle) * (0.2 + 0.6 * Math.random()) + startAngle\n\n            // Rotate tempPlane2 at impact point around normal axis and the angle\n            scope.tempVector3_2\n              .copy(object.position)\n              .sub(pointOfImpact)\n              .applyAxisAngle(normal, angle)\n              .add(pointOfImpact)\n            tempPlane2.setFromCoplanarPoints(pointOfImpact, scope.tempVector3, scope.tempVector3_2)\n          } else {\n            angle = (0.5 * (numIterations & 1) + 0.2 * (2 - Math.random())) * Math.PI\n\n            // Rotate tempPlane2 at object position around normal axis and the angle\n            scope.tempVector3_2\n              .copy(pointOfImpact)\n              .sub(subObject.position)\n              .applyAxisAngle(normal, angle)\n              .add(subObject.position)\n            scope.tempVector3_3.copy(normal).add(subObject.position)\n            tempPlane2.setFromCoplanarPoints(subObject.position, scope.tempVector3_3, scope.tempVector3_2)\n          }\n        }\n\n        // Perform the cut\n        scope.cutByPlane(subObject, tempPlane2, scope.tempResultObjects)\n\n        const obj1 = scope.tempResultObjects.object1\n        const obj2 = scope.tempResultObjects.object2\n\n        if (obj1) {\n          subdivideRadial(obj1, startAngle, angle, numIterations + 1)\n        }\n\n        if (obj2) {\n          subdivideRadial(obj2, angle, endAngle, numIterations + 1)\n        }\n      }\n\n      subdivideRadial(object, 0, 2 * Math.PI, 0)\n\n      return debris\n    }\n\n    cutByPlane(object, plane, output) {\n      // Returns breakable objects in output.object1 and output.object2 members, the resulting 2 pieces of the cut.\n      // object2 can be null if the plane doesn't cut the object.\n      // object1 can be null only in case of internal error\n      // Returned value is number of pieces, 0 for error.\n\n      const geometry = object.geometry\n      const coords = geometry.attributes.position.array\n      const normals = geometry.attributes.normal.array\n\n      const numPoints = coords.length / 3\n      let numFaces = numPoints / 3\n\n      let indices = geometry.getIndex()\n\n      if (indices) {\n        indices = indices.array\n        numFaces = indices.length / 3\n      }\n\n      function getVertexIndex(faceIdx, vert) {\n        // vert = 0, 1 or 2.\n\n        const idx = faceIdx * 3 + vert\n\n        return indices ? indices[idx] : idx\n      }\n\n      const points1 = []\n      const points2 = []\n\n      const delta = this.smallDelta\n\n      // Reset segments mark\n      const numPointPairs = numPoints * numPoints\n      for (let i = 0; i < numPointPairs; i++) this.segments[i] = false\n\n      const p0 = this.tempVector3_P0\n      const p1 = this.tempVector3_P1\n      const n0 = this.tempVector3_N0\n      const n1 = this.tempVector3_N1\n\n      // Iterate through the faces to mark edges shared by coplanar faces\n      for (let i = 0; i < numFaces - 1; i++) {\n        const a1 = getVertexIndex(i, 0)\n        const b1 = getVertexIndex(i, 1)\n        const c1 = getVertexIndex(i, 2)\n\n        // Assuming all 3 vertices have the same normal\n        n0.set(normals[a1], normals[a1] + 1, normals[a1] + 2)\n\n        for (let j = i + 1; j < numFaces; j++) {\n          const a2 = getVertexIndex(j, 0)\n          const b2 = getVertexIndex(j, 1)\n          const c2 = getVertexIndex(j, 2)\n\n          // Assuming all 3 vertices have the same normal\n          n1.set(normals[a2], normals[a2] + 1, normals[a2] + 2)\n\n          const coplanar = 1 - n0.dot(n1) < delta\n\n          if (coplanar) {\n            if (a1 === a2 || a1 === b2 || a1 === c2) {\n              if (b1 === a2 || b1 === b2 || b1 === c2) {\n                this.segments[a1 * numPoints + b1] = true\n                this.segments[b1 * numPoints + a1] = true\n              } else {\n                this.segments[c1 * numPoints + a1] = true\n                this.segments[a1 * numPoints + c1] = true\n              }\n            } else if (b1 === a2 || b1 === b2 || b1 === c2) {\n              this.segments[c1 * numPoints + b1] = true\n              this.segments[b1 * numPoints + c1] = true\n            }\n          }\n        }\n      }\n\n      // Transform the plane to object local space\n      const localPlane = this.tempPlane_Cut\n      object.updateMatrix()\n      ConvexObjectBreaker.transformPlaneToLocalSpace(plane, object.matrix, localPlane)\n\n      // Iterate through the faces adding points to both pieces\n      for (let i = 0; i < numFaces; i++) {\n        const va = getVertexIndex(i, 0)\n        const vb = getVertexIndex(i, 1)\n        const vc = getVertexIndex(i, 2)\n\n        for (let segment = 0; segment < 3; segment++) {\n          const i0 = segment === 0 ? va : segment === 1 ? vb : vc\n          const i1 = segment === 0 ? vb : segment === 1 ? vc : va\n\n          const segmentState = this.segments[i0 * numPoints + i1]\n\n          if (segmentState) continue // The segment already has been processed in another face\n\n          // Mark segment as processed (also inverted segment)\n          this.segments[i0 * numPoints + i1] = true\n          this.segments[i1 * numPoints + i0] = true\n\n          p0.set(coords[3 * i0], coords[3 * i0 + 1], coords[3 * i0 + 2])\n          p1.set(coords[3 * i1], coords[3 * i1 + 1], coords[3 * i1 + 2])\n\n          // mark: 1 for negative side, 2 for positive side, 3 for coplanar point\n          let mark0 = 0\n\n          let d = localPlane.distanceToPoint(p0)\n\n          if (d > delta) {\n            mark0 = 2\n            points2.push(p0.clone())\n          } else if (d < -delta) {\n            mark0 = 1\n            points1.push(p0.clone())\n          } else {\n            mark0 = 3\n            points1.push(p0.clone())\n            points2.push(p0.clone())\n          }\n\n          // mark: 1 for negative side, 2 for positive side, 3 for coplanar point\n          let mark1 = 0\n\n          d = localPlane.distanceToPoint(p1)\n\n          if (d > delta) {\n            mark1 = 2\n            points2.push(p1.clone())\n          } else if (d < -delta) {\n            mark1 = 1\n            points1.push(p1.clone())\n          } else {\n            mark1 = 3\n            points1.push(p1.clone())\n            points2.push(p1.clone())\n          }\n\n          if ((mark0 === 1 && mark1 === 2) || (mark0 === 2 && mark1 === 1)) {\n            // Intersection of segment with the plane\n\n            this.tempLine1.start.copy(p0)\n            this.tempLine1.end.copy(p1)\n\n            let intersection = new Vector3()\n            intersection = localPlane.intersectLine(this.tempLine1, intersection)\n\n            if (intersection === null) {\n              // Shouldn't happen\n              console.error('Internal error: segment does not intersect plane.')\n              output.segmentedObject1 = null\n              output.segmentedObject2 = null\n              return 0\n            }\n\n            points1.push(intersection)\n            points2.push(intersection.clone())\n          }\n        }\n      }\n\n      // Calculate debris mass (very fast and imprecise):\n      const newMass = object.userData.mass * 0.5\n\n      // Calculate debris Center of Mass (again fast and imprecise)\n      this.tempCM1.set(0, 0, 0)\n      let radius1 = 0\n      const numPoints1 = points1.length\n\n      if (numPoints1 > 0) {\n        for (let i = 0; i < numPoints1; i++) this.tempCM1.add(points1[i])\n\n        this.tempCM1.divideScalar(numPoints1)\n        for (let i = 0; i < numPoints1; i++) {\n          const p = points1[i]\n          p.sub(this.tempCM1)\n          radius1 = Math.max(radius1, p.x, p.y, p.z)\n        }\n\n        this.tempCM1.add(object.position)\n      }\n\n      this.tempCM2.set(0, 0, 0)\n      let radius2 = 0\n      const numPoints2 = points2.length\n      if (numPoints2 > 0) {\n        for (let i = 0; i < numPoints2; i++) this.tempCM2.add(points2[i])\n\n        this.tempCM2.divideScalar(numPoints2)\n        for (let i = 0; i < numPoints2; i++) {\n          const p = points2[i]\n          p.sub(this.tempCM2)\n          radius2 = Math.max(radius2, p.x, p.y, p.z)\n        }\n\n        this.tempCM2.add(object.position)\n      }\n\n      let object1 = null\n      let object2 = null\n\n      let numObjects = 0\n\n      if (numPoints1 > 4) {\n        object1 = new Mesh(new ConvexGeometry(points1), object.material)\n        object1.position.copy(this.tempCM1)\n        object1.quaternion.copy(object.quaternion)\n\n        this.prepareBreakableObject(\n          object1,\n          newMass,\n          object.userData.velocity,\n          object.userData.angularVelocity,\n          2 * radius1 > this.minSizeForBreak,\n        )\n\n        numObjects++\n      }\n\n      if (numPoints2 > 4) {\n        object2 = new Mesh(new ConvexGeometry(points2), object.material)\n        object2.position.copy(this.tempCM2)\n        object2.quaternion.copy(object.quaternion)\n\n        this.prepareBreakableObject(\n          object2,\n          newMass,\n          object.userData.velocity,\n          object.userData.angularVelocity,\n          2 * radius2 > this.minSizeForBreak,\n        )\n\n        numObjects++\n      }\n\n      output.object1 = object1\n      output.object2 = object2\n\n      return numObjects\n    }\n\n    static transformFreeVector(v, m) {\n      // input:\n      // vector interpreted as a free vector\n      // THREE.Matrix4 orthogonal matrix (matrix without scale)\n\n      const x = v.x,\n        y = v.y,\n        z = v.z\n      const e = m.elements\n\n      v.x = e[0] * x + e[4] * y + e[8] * z\n      v.y = e[1] * x + e[5] * y + e[9] * z\n      v.z = e[2] * x + e[6] * y + e[10] * z\n\n      return v\n    }\n\n    static transformFreeVectorInverse(v, m) {\n      // input:\n      // vector interpreted as a free vector\n      // THREE.Matrix4 orthogonal matrix (matrix without scale)\n\n      const x = v.x,\n        y = v.y,\n        z = v.z\n      const e = m.elements\n\n      v.x = e[0] * x + e[1] * y + e[2] * z\n      v.y = e[4] * x + e[5] * y + e[6] * z\n      v.z = e[8] * x + e[9] * y + e[10] * z\n\n      return v\n    }\n\n    static transformTiedVectorInverse(v, m) {\n      // input:\n      // vector interpreted as a tied (ordinary) vector\n      // THREE.Matrix4 orthogonal matrix (matrix without scale)\n\n      const x = v.x,\n        y = v.y,\n        z = v.z\n      const e = m.elements\n\n      v.x = e[0] * x + e[1] * y + e[2] * z - e[12]\n      v.y = e[4] * x + e[5] * y + e[6] * z - e[13]\n      v.z = e[8] * x + e[9] * y + e[10] * z - e[14]\n\n      return v\n    }\n\n    static transformPlaneToLocalSpace(plane, m, resultPlane) {\n      resultPlane.normal.copy(plane.normal)\n      resultPlane.constant = plane.constant\n\n      const referencePoint = ConvexObjectBreaker.transformTiedVectorInverse(plane.coplanarPoint(_v1), m)\n\n      ConvexObjectBreaker.transformFreeVectorInverse(resultPlane.normal, m)\n\n      // recalculate constant (like in setFromNormalAndCoplanarPoint)\n      resultPlane.constant = -referencePoint.dot(resultPlane.normal)\n    }\n  }\n\n  return ConvexObjectBreaker\n})()\n\nexport { ConvexObjectBreaker }\n"], "names": ["ConvexObjectBreaker"], "mappings": ";;AAgCA,MAAM,MAAsB,oBAAI,QAAS;AAEpC,MAAC,sBAAuC,uBAAM;AACjD,QAAMA,qBAAoB;AAAA,IACxB,YAAY,kBAAkB,KAAK,aAAa,MAAQ;AACtD,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAElB,WAAK,YAAY,IAAI,MAAO;AAC5B,WAAK,aAAa,IAAI,MAAO;AAC7B,WAAK,aAAa,IAAI,MAAO;AAC7B,WAAK,gBAAgB,IAAI,MAAO;AAChC,WAAK,UAAU,IAAI,QAAS;AAC5B,WAAK,UAAU,IAAI,QAAS;AAC5B,WAAK,cAAc,IAAI,QAAS;AAChC,WAAK,gBAAgB,IAAI,QAAS;AAClC,WAAK,gBAAgB,IAAI,QAAS;AAClC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,iBAAiB,IAAI,QAAS;AACnC,WAAK,oBAAoB,EAAE,SAAS,MAAM,SAAS,KAAM;AAEzD,WAAK,WAAW,CAAE;AAClB,YAAM,IAAI,KAAK;AACf,eAAS,IAAI,GAAG,IAAI,GAAG;AAAK,aAAK,SAAS,CAAC,IAAI;AAAA,IAChD;AAAA,IAED,uBAAuB,QAAQ,MAAM,UAAU,iBAAiB,WAAW;AAKzE,YAAM,WAAW,OAAO;AACxB,eAAS,OAAO;AAChB,eAAS,WAAW,SAAS,MAAO;AACpC,eAAS,kBAAkB,gBAAgB,MAAO;AAClD,eAAS,YAAY;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQD,kBAAkB,QAAQ,eAAe,QAAQ,qBAAqB,qBAAqB;AACzF,YAAM,SAAS,CAAE;AAEjB,YAAM,aAAa,KAAK;AACxB,YAAM,aAAa,KAAK;AAExB,WAAK,YAAY,WAAW,eAAe,MAAM;AACjD,iBAAW,sBAAsB,eAAe,OAAO,UAAU,KAAK,WAAW;AAEjF,YAAM,qBAAqB,sBAAsB;AAEjD,YAAM,QAAQ;AAEd,eAAS,gBAAgB,WAAW,YAAY,UAAU,eAAe;AACvE,YAAI,KAAK,OAAQ,IAAG,gBAAgB,QAAQ,gBAAgB,oBAAoB;AAC9E,iBAAO,KAAK,SAAS;AAErB;AAAA,QACD;AAED,YAAI,QAAQ,KAAK;AAEjB,YAAI,kBAAkB,GAAG;AACvB,qBAAW,OAAO,KAAK,WAAW,MAAM;AACxC,qBAAW,WAAW,WAAW;AAAA,QAC3C,OAAe;AACL,cAAI,iBAAiB,qBAAqB;AACxC,qBAAS,WAAW,eAAe,MAAM,MAAM,KAAK,OAAQ,KAAI;AAGhE,kBAAM,cACH,KAAK,OAAO,QAAQ,EACpB,IAAI,aAAa,EACjB,eAAe,QAAQ,KAAK,EAC5B,IAAI,aAAa;AACpB,uBAAW,sBAAsB,eAAe,MAAM,aAAa,MAAM,aAAa;AAAA,UAClG,OAAiB;AACL,qBAAS,OAAO,gBAAgB,KAAK,OAAO,IAAI,KAAK,aAAa,KAAK;AAGvE,kBAAM,cACH,KAAK,aAAa,EAClB,IAAI,UAAU,QAAQ,EACtB,eAAe,QAAQ,KAAK,EAC5B,IAAI,UAAU,QAAQ;AACzB,kBAAM,cAAc,KAAK,MAAM,EAAE,IAAI,UAAU,QAAQ;AACvD,uBAAW,sBAAsB,UAAU,UAAU,MAAM,eAAe,MAAM,aAAa;AAAA,UAC9F;AAAA,QACF;AAGD,cAAM,WAAW,WAAW,YAAY,MAAM,iBAAiB;AAE/D,cAAM,OAAO,MAAM,kBAAkB;AACrC,cAAM,OAAO,MAAM,kBAAkB;AAErC,YAAI,MAAM;AACR,0BAAgB,MAAM,YAAY,OAAO,gBAAgB,CAAC;AAAA,QAC3D;AAED,YAAI,MAAM;AACR,0BAAgB,MAAM,OAAO,UAAU,gBAAgB,CAAC;AAAA,QACzD;AAAA,MACF;AAED,sBAAgB,QAAQ,GAAG,IAAI,KAAK,IAAI,CAAC;AAEzC,aAAO;AAAA,IACR;AAAA,IAED,WAAW,QAAQ,OAAO,QAAQ;AAMhC,YAAM,WAAW,OAAO;AACxB,YAAM,SAAS,SAAS,WAAW,SAAS;AAC5C,YAAM,UAAU,SAAS,WAAW,OAAO;AAE3C,YAAM,YAAY,OAAO,SAAS;AAClC,UAAI,WAAW,YAAY;AAE3B,UAAI,UAAU,SAAS,SAAU;AAEjC,UAAI,SAAS;AACX,kBAAU,QAAQ;AAClB,mBAAW,QAAQ,SAAS;AAAA,MAC7B;AAED,eAAS,eAAe,SAAS,MAAM;AAGrC,cAAM,MAAM,UAAU,IAAI;AAE1B,eAAO,UAAU,QAAQ,GAAG,IAAI;AAAA,MACjC;AAED,YAAM,UAAU,CAAE;AAClB,YAAM,UAAU,CAAE;AAElB,YAAM,QAAQ,KAAK;AAGnB,YAAM,gBAAgB,YAAY;AAClC,eAAS,IAAI,GAAG,IAAI,eAAe;AAAK,aAAK,SAAS,CAAC,IAAI;AAE3D,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAGhB,eAAS,IAAI,GAAG,IAAI,WAAW,GAAG,KAAK;AACrC,cAAM,KAAK,eAAe,GAAG,CAAC;AAC9B,cAAM,KAAK,eAAe,GAAG,CAAC;AAC9B,cAAM,KAAK,eAAe,GAAG,CAAC;AAG9B,WAAG,IAAI,QAAQ,EAAE,GAAG,QAAQ,EAAE,IAAI,GAAG,QAAQ,EAAE,IAAI,CAAC;AAEpD,iBAAS,IAAI,IAAI,GAAG,IAAI,UAAU,KAAK;AACrC,gBAAM,KAAK,eAAe,GAAG,CAAC;AAC9B,gBAAM,KAAK,eAAe,GAAG,CAAC;AAC9B,gBAAM,KAAK,eAAe,GAAG,CAAC;AAG9B,aAAG,IAAI,QAAQ,EAAE,GAAG,QAAQ,EAAE,IAAI,GAAG,QAAQ,EAAE,IAAI,CAAC;AAEpD,gBAAM,WAAW,IAAI,GAAG,IAAI,EAAE,IAAI;AAElC,cAAI,UAAU;AACZ,gBAAI,OAAO,MAAM,OAAO,MAAM,OAAO,IAAI;AACvC,kBAAI,OAAO,MAAM,OAAO,MAAM,OAAO,IAAI;AACvC,qBAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AACrC,qBAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AAAA,cACrD,OAAqB;AACL,qBAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AACrC,qBAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AAAA,cACtC;AAAA,YACf,WAAuB,OAAO,MAAM,OAAO,MAAM,OAAO,IAAI;AAC9C,mBAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AACrC,mBAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGD,YAAM,aAAa,KAAK;AACxB,aAAO,aAAc;AACrB,MAAAA,qBAAoB,2BAA2B,OAAO,OAAO,QAAQ,UAAU;AAG/E,eAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,cAAM,KAAK,eAAe,GAAG,CAAC;AAC9B,cAAM,KAAK,eAAe,GAAG,CAAC;AAC9B,cAAM,KAAK,eAAe,GAAG,CAAC;AAE9B,iBAAS,UAAU,GAAG,UAAU,GAAG,WAAW;AAC5C,gBAAM,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,KAAK;AACrD,gBAAM,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,KAAK;AAErD,gBAAM,eAAe,KAAK,SAAS,KAAK,YAAY,EAAE;AAEtD,cAAI;AAAc;AAGlB,eAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AACrC,eAAK,SAAS,KAAK,YAAY,EAAE,IAAI;AAErC,aAAG,IAAI,OAAO,IAAI,EAAE,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,CAAC;AAC7D,aAAG,IAAI,OAAO,IAAI,EAAE,GAAG,OAAO,IAAI,KAAK,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,CAAC;AAG7D,cAAI,QAAQ;AAEZ,cAAI,IAAI,WAAW,gBAAgB,EAAE;AAErC,cAAI,IAAI,OAAO;AACb,oBAAQ;AACR,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACnC,WAAqB,IAAI,CAAC,OAAO;AACrB,oBAAQ;AACR,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACnC,OAAiB;AACL,oBAAQ;AACR,oBAAQ,KAAK,GAAG,OAAO;AACvB,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACxB;AAGD,cAAI,QAAQ;AAEZ,cAAI,WAAW,gBAAgB,EAAE;AAEjC,cAAI,IAAI,OAAO;AACb,oBAAQ;AACR,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACnC,WAAqB,IAAI,CAAC,OAAO;AACrB,oBAAQ;AACR,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACnC,OAAiB;AACL,oBAAQ;AACR,oBAAQ,KAAK,GAAG,OAAO;AACvB,oBAAQ,KAAK,GAAG,OAAO;AAAA,UACxB;AAED,cAAK,UAAU,KAAK,UAAU,KAAO,UAAU,KAAK,UAAU,GAAI;AAGhE,iBAAK,UAAU,MAAM,KAAK,EAAE;AAC5B,iBAAK,UAAU,IAAI,KAAK,EAAE;AAE1B,gBAAI,eAAe,IAAI,QAAS;AAChC,2BAAe,WAAW,cAAc,KAAK,WAAW,YAAY;AAEpE,gBAAI,iBAAiB,MAAM;AAEzB,sBAAQ,MAAM,mDAAmD;AACjE,qBAAO,mBAAmB;AAC1B,qBAAO,mBAAmB;AAC1B,qBAAO;AAAA,YACR;AAED,oBAAQ,KAAK,YAAY;AACzB,oBAAQ,KAAK,aAAa,OAAO;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAGD,YAAM,UAAU,OAAO,SAAS,OAAO;AAGvC,WAAK,QAAQ,IAAI,GAAG,GAAG,CAAC;AACxB,UAAI,UAAU;AACd,YAAM,aAAa,QAAQ;AAE3B,UAAI,aAAa,GAAG;AAClB,iBAAS,IAAI,GAAG,IAAI,YAAY;AAAK,eAAK,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAEhE,aAAK,QAAQ,aAAa,UAAU;AACpC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,gBAAM,IAAI,QAAQ,CAAC;AACnB,YAAE,IAAI,KAAK,OAAO;AAClB,oBAAU,KAAK,IAAI,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,QAC1C;AAED,aAAK,QAAQ,IAAI,OAAO,QAAQ;AAAA,MACjC;AAED,WAAK,QAAQ,IAAI,GAAG,GAAG,CAAC;AACxB,UAAI,UAAU;AACd,YAAM,aAAa,QAAQ;AAC3B,UAAI,aAAa,GAAG;AAClB,iBAAS,IAAI,GAAG,IAAI,YAAY;AAAK,eAAK,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAEhE,aAAK,QAAQ,aAAa,UAAU;AACpC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,gBAAM,IAAI,QAAQ,CAAC;AACnB,YAAE,IAAI,KAAK,OAAO;AAClB,oBAAU,KAAK,IAAI,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,QAC1C;AAED,aAAK,QAAQ,IAAI,OAAO,QAAQ;AAAA,MACjC;AAED,UAAI,UAAU;AACd,UAAI,UAAU;AAEd,UAAI,aAAa;AAEjB,UAAI,aAAa,GAAG;AAClB,kBAAU,IAAI,KAAK,IAAI,eAAe,OAAO,GAAG,OAAO,QAAQ;AAC/D,gBAAQ,SAAS,KAAK,KAAK,OAAO;AAClC,gBAAQ,WAAW,KAAK,OAAO,UAAU;AAEzC,aAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA,OAAO,SAAS;AAAA,UAChB,OAAO,SAAS;AAAA,UAChB,IAAI,UAAU,KAAK;AAAA,QACpB;AAED;AAAA,MACD;AAED,UAAI,aAAa,GAAG;AAClB,kBAAU,IAAI,KAAK,IAAI,eAAe,OAAO,GAAG,OAAO,QAAQ;AAC/D,gBAAQ,SAAS,KAAK,KAAK,OAAO;AAClC,gBAAQ,WAAW,KAAK,OAAO,UAAU;AAEzC,aAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA,OAAO,SAAS;AAAA,UAChB,OAAO,SAAS;AAAA,UAChB,IAAI,UAAU,KAAK;AAAA,QACpB;AAED;AAAA,MACD;AAED,aAAO,UAAU;AACjB,aAAO,UAAU;AAEjB,aAAO;AAAA,IACR;AAAA,IAED,OAAO,oBAAoB,GAAG,GAAG;AAK/B,YAAM,IAAI,EAAE,GACV,IAAI,EAAE,GACN,IAAI,EAAE;AACR,YAAM,IAAI,EAAE;AAEZ,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AACnC,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AACnC,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI;AAEpC,aAAO;AAAA,IACR;AAAA,IAED,OAAO,2BAA2B,GAAG,GAAG;AAKtC,YAAM,IAAI,EAAE,GACV,IAAI,EAAE,GACN,IAAI,EAAE;AACR,YAAM,IAAI,EAAE;AAEZ,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AACnC,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI;AACnC,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI;AAEpC,aAAO;AAAA,IACR;AAAA,IAED,OAAO,2BAA2B,GAAG,GAAG;AAKtC,YAAM,IAAI,EAAE,GACV,IAAI,EAAE,GACN,IAAI,EAAE;AACR,YAAM,IAAI,EAAE;AAEZ,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE;AAC3C,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE;AAC3C,QAAE,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE;AAE5C,aAAO;AAAA,IACR;AAAA,IAED,OAAO,2BAA2B,OAAO,GAAG,aAAa;AACvD,kBAAY,OAAO,KAAK,MAAM,MAAM;AACpC,kBAAY,WAAW,MAAM;AAE7B,YAAM,iBAAiBA,qBAAoB,2BAA2B,MAAM,cAAc,GAAG,GAAG,CAAC;AAEjG,MAAAA,qBAAoB,2BAA2B,YAAY,QAAQ,CAAC;AAGpE,kBAAY,WAAW,CAAC,eAAe,IAAI,YAAY,MAAM;AAAA,IAC9D;AAAA,EACF;AAED,SAAOA;AACT,GAAC;"}