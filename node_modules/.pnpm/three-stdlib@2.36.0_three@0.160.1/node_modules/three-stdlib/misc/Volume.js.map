{"version": 3, "file": "Volume.js", "sources": ["../../src/misc/Volume.js"], "sourcesContent": ["import { Matrix3, Matrix4, Vector3 } from 'three'\nimport { VolumeSlice } from '../misc/VolumeSlice'\n\n/**\n * This class had been written to handle the output of the NRRD loader.\n * It contains a volume of data and informations about it.\n * For now it only handles 3 dimensional data.\n * See the webgl_loader_nrrd.html example and the loaderNRRD.js file to see how to use this class.\n * @class\n * @param   {number}        xLength         Width of the volume\n * @param   {number}        yLength         Length of the volume\n * @param   {number}        zLength         Depth of the volume\n * @param   {string}        type            The type of data (uint8, uint16, ...)\n * @param   {ArrayBuffer}   arrayBuffer     The buffer with volume data\n */\nclass Volume {\n  constructor(xLength, yLength, zLength, type, arrayBuffer) {\n    if (xLength !== undefined) {\n      /**\n       * @member {number} xLength Width of the volume in the IJK coordinate system\n       */\n      this.xLength = Number(xLength) || 1\n      /**\n       * @member {number} yLength Height of the volume in the IJK coordinate system\n       */\n      this.yLength = Number(yLength) || 1\n      /**\n       * @member {number} zLength Depth of the volume in the IJK coordinate system\n       */\n      this.zLength = Number(zLength) || 1\n      /**\n       * @member {Array<string>} The order of the Axis dictated by the NRRD header\n       */\n      this.axisOrder = ['x', 'y', 'z']\n      /**\n       * @member {TypedArray} data Data of the volume\n       */\n\n      switch (type) {\n        case 'Uint8':\n        case 'uint8':\n        case 'uchar':\n        case 'unsigned char':\n        case 'uint8_t':\n          this.data = new Uint8Array(arrayBuffer)\n          break\n        case 'Int8':\n        case 'int8':\n        case 'signed char':\n        case 'int8_t':\n          this.data = new Int8Array(arrayBuffer)\n          break\n        case 'Int16':\n        case 'int16':\n        case 'short':\n        case 'short int':\n        case 'signed short':\n        case 'signed short int':\n        case 'int16_t':\n          this.data = new Int16Array(arrayBuffer)\n          break\n        case 'Uint16':\n        case 'uint16':\n        case 'ushort':\n        case 'unsigned short':\n        case 'unsigned short int':\n        case 'uint16_t':\n          this.data = new Uint16Array(arrayBuffer)\n          break\n        case 'Int32':\n        case 'int32':\n        case 'int':\n        case 'signed int':\n        case 'int32_t':\n          this.data = new Int32Array(arrayBuffer)\n          break\n        case 'Uint32':\n        case 'uint32':\n        case 'uint':\n        case 'unsigned int':\n        case 'uint32_t':\n          this.data = new Uint32Array(arrayBuffer)\n          break\n        case 'longlong':\n        case 'long long':\n        case 'long long int':\n        case 'signed long long':\n        case 'signed long long int':\n        case 'int64':\n        case 'int64_t':\n        case 'ulonglong':\n        case 'unsigned long long':\n        case 'unsigned long long int':\n        case 'uint64':\n        case 'uint64_t':\n          throw new Error('Error in Volume constructor : this type is not supported in JavaScript')\n          break\n        case 'Float32':\n        case 'float32':\n        case 'float':\n          this.data = new Float32Array(arrayBuffer)\n          break\n        case 'Float64':\n        case 'float64':\n        case 'double':\n          this.data = new Float64Array(arrayBuffer)\n          break\n        default:\n          this.data = new Uint8Array(arrayBuffer)\n      }\n\n      if (this.data.length !== this.xLength * this.yLength * this.zLength) {\n        throw new Error('Error in Volume constructor, lengths are not matching arrayBuffer size')\n      }\n    }\n\n    /**\n     * @member {Array}  spacing Spacing to apply to the volume from IJK to RAS coordinate system\n     */\n    this.spacing = [1, 1, 1]\n    /**\n     * @member {Array}  offset Offset of the volume in the RAS coordinate system\n     */\n    this.offset = [0, 0, 0]\n    /**\n     * @member {Martrix3} matrix The IJK to RAS matrix\n     */\n    this.matrix = new Matrix3()\n    this.matrix.identity()\n    /**\n     * @member {Martrix3} inverseMatrix The RAS to IJK matrix\n     */\n    /**\n     * @member {number} lowerThreshold The voxels with values under this threshold won't appear in the slices.\n     *                      If changed, geometryNeedsUpdate is automatically set to true on all the slices associated to this volume\n     */\n    let lowerThreshold = -Infinity\n    Object.defineProperty(this, 'lowerThreshold', {\n      get: function () {\n        return lowerThreshold\n      },\n      set: function (value) {\n        lowerThreshold = value\n        this.sliceList.forEach(function (slice) {\n          slice.geometryNeedsUpdate = true\n        })\n      },\n    })\n    /**\n     * @member {number} upperThreshold The voxels with values over this threshold won't appear in the slices.\n     *                      If changed, geometryNeedsUpdate is automatically set to true on all the slices associated to this volume\n     */\n    let upperThreshold = Infinity\n    Object.defineProperty(this, 'upperThreshold', {\n      get: function () {\n        return upperThreshold\n      },\n      set: function (value) {\n        upperThreshold = value\n        this.sliceList.forEach(function (slice) {\n          slice.geometryNeedsUpdate = true\n        })\n      },\n    })\n\n    /**\n     * @member {Array} sliceList The list of all the slices associated to this volume\n     */\n    this.sliceList = []\n\n    /**\n     * @member {boolean} segmentation in segmentation mode, it can load 16-bits nrrds correctly\n     */\n    this.segmentation = false\n\n    /**\n     * @member {Array} RASDimensions This array holds the dimensions of the volume in the RAS space\n     */\n  }\n\n  /**\n   * @member {Function} getData Shortcut for data[access(i,j,k)]\n   * @memberof Volume\n   * @param {number} i    First coordinate\n   * @param {number} j    Second coordinate\n   * @param {number} k    Third coordinate\n   * @returns {number}  value in the data array\n   */\n  getData(i, j, k) {\n    return this.data[k * this.xLength * this.yLength + j * this.xLength + i]\n  }\n\n  /**\n   * @member {Function} access compute the index in the data array corresponding to the given coordinates in IJK system\n   * @memberof Volume\n   * @param {number} i    First coordinate\n   * @param {number} j    Second coordinate\n   * @param {number} k    Third coordinate\n   * @returns {number}  index\n   */\n  access(i, j, k) {\n    return k * this.xLength * this.yLength + j * this.xLength + i\n  }\n\n  /**\n   * @member {Function} reverseAccess Retrieve the IJK coordinates of the voxel corresponding of the given index in the data\n   * @memberof Volume\n   * @param {number} index index of the voxel\n   * @returns {Array}  [x,y,z]\n   */\n  reverseAccess(index) {\n    const z = Math.floor(index / (this.yLength * this.xLength))\n    const y = Math.floor((index - z * this.yLength * this.xLength) / this.xLength)\n    const x = index - z * this.yLength * this.xLength - y * this.xLength\n    return [x, y, z]\n  }\n\n  /**\n   * @member {Function} map Apply a function to all the voxels, be careful, the value will be replaced\n   * @memberof Volume\n   * @param {Function} functionToMap A function to apply to every voxel, will be called with the following parameters :\n   *                                 value of the voxel\n   *                                 index of the voxel\n   *                                 the data (TypedArray)\n   * @param {Object}   context    You can specify a context in which call the function, default if this Volume\n   * @returns {Volume}   this\n   */\n  map(functionToMap, context) {\n    const length = this.data.length\n    context = context || this\n\n    for (let i = 0; i < length; i++) {\n      this.data[i] = functionToMap.call(context, this.data[i], i, this.data)\n    }\n\n    return this\n  }\n\n  /**\n   * @member {Function} extractPerpendicularPlane Compute the orientation of the slice and returns all the information relative to the geometry such as sliceAccess, the plane matrix (orientation and position in RAS coordinate) and the dimensions of the plane in both coordinate system.\n   * @memberof Volume\n   * @param {string}            axis  the normal axis to the slice 'x' 'y' or 'z'\n   * @param {number}            index the index of the slice\n   * @returns {Object} an object containing all the usefull information on the geometry of the slice\n   */\n  extractPerpendicularPlane(axis, RASIndex) {\n    let firstSpacing, secondSpacing, positionOffset, IJKIndex\n\n    const axisInIJK = new Vector3(),\n      firstDirection = new Vector3(),\n      secondDirection = new Vector3(),\n      planeMatrix = new Matrix4().identity(),\n      volume = this\n\n    const dimensions = new Vector3(this.xLength, this.yLength, this.zLength)\n\n    switch (axis) {\n      case 'x':\n        axisInIJK.set(1, 0, 0)\n        firstDirection.set(0, 0, -1)\n        secondDirection.set(0, -1, 0)\n        firstSpacing = this.spacing[this.axisOrder.indexOf('z')]\n        secondSpacing = this.spacing[this.axisOrder.indexOf('y')]\n        IJKIndex = new Vector3(RASIndex, 0, 0)\n\n        planeMatrix.multiply(new Matrix4().makeRotationY(Math.PI / 2))\n        positionOffset = (volume.RASDimensions[0] - 1) / 2\n        planeMatrix.setPosition(new Vector3(RASIndex - positionOffset, 0, 0))\n        break\n      case 'y':\n        axisInIJK.set(0, 1, 0)\n        firstDirection.set(1, 0, 0)\n        secondDirection.set(0, 0, 1)\n        firstSpacing = this.spacing[this.axisOrder.indexOf('x')]\n        secondSpacing = this.spacing[this.axisOrder.indexOf('z')]\n        IJKIndex = new Vector3(0, RASIndex, 0)\n\n        planeMatrix.multiply(new Matrix4().makeRotationX(-Math.PI / 2))\n        positionOffset = (volume.RASDimensions[1] - 1) / 2\n        planeMatrix.setPosition(new Vector3(0, RASIndex - positionOffset, 0))\n        break\n      case 'z':\n      default:\n        axisInIJK.set(0, 0, 1)\n        firstDirection.set(1, 0, 0)\n        secondDirection.set(0, -1, 0)\n        firstSpacing = this.spacing[this.axisOrder.indexOf('x')]\n        secondSpacing = this.spacing[this.axisOrder.indexOf('y')]\n        IJKIndex = new Vector3(0, 0, RASIndex)\n\n        positionOffset = (volume.RASDimensions[2] - 1) / 2\n        planeMatrix.setPosition(new Vector3(0, 0, RASIndex - positionOffset))\n        break\n    }\n\n    let iLength, jLength\n\n    if (!this.segmentation) {\n      firstDirection.applyMatrix4(volume.inverseMatrix).normalize()\n      secondDirection.applyMatrix4(volume.inverseMatrix).normalize()\n      axisInIJK.applyMatrix4(volume.inverseMatrix).normalize()\n    }\n    firstDirection.arglet = 'i'\n    secondDirection.arglet = 'j'\n    iLength = Math.floor(Math.abs(firstDirection.dot(dimensions)))\n    jLength = Math.floor(Math.abs(secondDirection.dot(dimensions)))\n    const planeWidth = Math.abs(iLength * firstSpacing)\n    const planeHeight = Math.abs(jLength * secondSpacing)\n\n    IJKIndex = Math.abs(Math.round(IJKIndex.applyMatrix4(volume.inverseMatrix).dot(axisInIJK)))\n    const base = [new Vector3(1, 0, 0), new Vector3(0, 1, 0), new Vector3(0, 0, 1)]\n    const iDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[0])) > 0.9\n    })\n    const jDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[1])) > 0.9\n    })\n    const kDirection = [firstDirection, secondDirection, axisInIJK].find(function (x) {\n      return Math.abs(x.dot(base[2])) > 0.9\n    })\n\n    function sliceAccess(i, j) {\n      const si = iDirection === axisInIJK ? IJKIndex : iDirection.arglet === 'i' ? i : j\n      const sj = jDirection === axisInIJK ? IJKIndex : jDirection.arglet === 'i' ? i : j\n      const sk = kDirection === axisInIJK ? IJKIndex : kDirection.arglet === 'i' ? i : j\n\n      // invert indices if necessary\n\n      const accessI = iDirection.dot(base[0]) > 0 ? si : volume.xLength - 1 - si\n      const accessJ = jDirection.dot(base[1]) > 0 ? sj : volume.yLength - 1 - sj\n      const accessK = kDirection.dot(base[2]) > 0 ? sk : volume.zLength - 1 - sk\n\n      return volume.access(accessI, accessJ, accessK)\n    }\n\n    return {\n      iLength: iLength,\n      jLength: jLength,\n      sliceAccess: sliceAccess,\n      matrix: planeMatrix,\n      planeWidth: planeWidth,\n      planeHeight: planeHeight,\n    }\n  }\n\n  /**\n   * @member {Function} extractSlice Returns a slice corresponding to the given axis and index\n   *                        The coordinate are given in the Right Anterior Superior coordinate format\n   * @memberof Volume\n   * @param {string}            axis  the normal axis to the slice 'x' 'y' or 'z'\n   * @param {number}            index the index of the slice\n   * @returns {VolumeSlice} the extracted slice\n   */\n  extractSlice(axis, index) {\n    const slice = new VolumeSlice(this, index, axis)\n    this.sliceList.push(slice)\n    return slice\n  }\n\n  /**\n   * @member {Function} repaintAllSlices Call repaint on all the slices extracted from this volume\n   * @see VolumeSlice.repaint\n   * @memberof Volume\n   * @returns {Volume} this\n   */\n  repaintAllSlices() {\n    this.sliceList.forEach(function (slice) {\n      slice.repaint()\n    })\n\n    return this\n  }\n\n  /**\n   * @member {Function} computeMinMax Compute the minimum and the maximum of the data in the volume\n   * @memberof Volume\n   * @returns {Array} [min,max]\n   */\n  computeMinMax() {\n    let min = Infinity\n    let max = -Infinity\n\n    // buffer the length\n    const datasize = this.data.length\n\n    let i = 0\n\n    for (i = 0; i < datasize; i++) {\n      if (!isNaN(this.data[i])) {\n        const value = this.data[i]\n        min = Math.min(min, value)\n        max = Math.max(max, value)\n      }\n    }\n\n    this.min = min\n    this.max = max\n\n    return [min, max]\n  }\n}\n\nexport { Volume }\n"], "names": [], "mappings": ";;AAeA,MAAM,OAAO;AAAA,EACX,YAAY,SAAS,SAAS,SAAS,MAAM,aAAa;AACxD,QAAI,YAAY,QAAW;AAIzB,WAAK,UAAU,OAAO,OAAO,KAAK;AAIlC,WAAK,UAAU,OAAO,OAAO,KAAK;AAIlC,WAAK,UAAU,OAAO,OAAO,KAAK;AAIlC,WAAK,YAAY,CAAC,KAAK,KAAK,GAAG;AAK/B,cAAQ,MAAI;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,WAAW,WAAW;AACtC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,UAAU,WAAW;AACrC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,WAAW,WAAW;AACtC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,YAAY,WAAW;AACvC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,WAAW,WAAW;AACtC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,YAAY,WAAW;AACvC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,gBAAM,IAAI,MAAM,wEAAwE;AAAA,QAE1F,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,aAAa,WAAW;AACxC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,OAAO,IAAI,aAAa,WAAW;AACxC;AAAA,QACF;AACE,eAAK,OAAO,IAAI,WAAW,WAAW;AAAA,MACzC;AAED,UAAI,KAAK,KAAK,WAAW,KAAK,UAAU,KAAK,UAAU,KAAK,SAAS;AACnE,cAAM,IAAI,MAAM,wEAAwE;AAAA,MACzF;AAAA,IACF;AAKD,SAAK,UAAU,CAAC,GAAG,GAAG,CAAC;AAIvB,SAAK,SAAS,CAAC,GAAG,GAAG,CAAC;AAItB,SAAK,SAAS,IAAI,QAAS;AAC3B,SAAK,OAAO,SAAU;AAQtB,QAAI,iBAAiB;AACrB,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC5C,KAAK,WAAY;AACf,eAAO;AAAA,MACR;AAAA,MACD,KAAK,SAAU,OAAO;AACpB,yBAAiB;AACjB,aAAK,UAAU,QAAQ,SAAU,OAAO;AACtC,gBAAM,sBAAsB;AAAA,QACtC,CAAS;AAAA,MACF;AAAA,IACP,CAAK;AAKD,QAAI,iBAAiB;AACrB,WAAO,eAAe,MAAM,kBAAkB;AAAA,MAC5C,KAAK,WAAY;AACf,eAAO;AAAA,MACR;AAAA,MACD,KAAK,SAAU,OAAO;AACpB,yBAAiB;AACjB,aAAK,UAAU,QAAQ,SAAU,OAAO;AACtC,gBAAM,sBAAsB;AAAA,QACtC,CAAS;AAAA,MACF;AAAA,IACP,CAAK;AAKD,SAAK,YAAY,CAAE;AAKnB,SAAK,eAAe;AAAA,EAKrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,QAAQ,GAAG,GAAG,GAAG;AACf,WAAO,KAAK,KAAK,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,OAAO,GAAG,GAAG,GAAG;AACd,WAAO,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI,KAAK,UAAU;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,cAAc,OAAO;AACnB,UAAM,IAAI,KAAK,MAAM,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1D,UAAM,IAAI,KAAK,OAAO,QAAQ,IAAI,KAAK,UAAU,KAAK,WAAW,KAAK,OAAO;AAC7E,UAAM,IAAI,QAAQ,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI,KAAK;AAC7D,WAAO,CAAC,GAAG,GAAG,CAAC;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYD,IAAI,eAAe,SAAS;AAC1B,UAAM,SAAS,KAAK,KAAK;AACzB,cAAU,WAAW;AAErB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,WAAK,KAAK,CAAC,IAAI,cAAc,KAAK,SAAS,KAAK,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI;AAAA,IACtE;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,0BAA0B,MAAM,UAAU;AACxC,QAAI,cAAc,eAAe,gBAAgB;AAEjD,UAAM,YAAY,IAAI,QAAS,GAC7B,iBAAiB,IAAI,QAAS,GAC9B,kBAAkB,IAAI,QAAS,GAC/B,cAAc,IAAI,QAAS,EAAC,SAAU,GACtC,SAAS;AAEX,UAAM,aAAa,IAAI,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AAEvE,YAAQ,MAAI;AAAA,MACV,KAAK;AACH,kBAAU,IAAI,GAAG,GAAG,CAAC;AACrB,uBAAe,IAAI,GAAG,GAAG,EAAE;AAC3B,wBAAgB,IAAI,GAAG,IAAI,CAAC;AAC5B,uBAAe,KAAK,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC;AACvD,wBAAgB,KAAK,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC;AACxD,mBAAW,IAAI,QAAQ,UAAU,GAAG,CAAC;AAErC,oBAAY,SAAS,IAAI,QAAS,EAAC,cAAc,KAAK,KAAK,CAAC,CAAC;AAC7D,0BAAkB,OAAO,cAAc,CAAC,IAAI,KAAK;AACjD,oBAAY,YAAY,IAAI,QAAQ,WAAW,gBAAgB,GAAG,CAAC,CAAC;AACpE;AAAA,MACF,KAAK;AACH,kBAAU,IAAI,GAAG,GAAG,CAAC;AACrB,uBAAe,IAAI,GAAG,GAAG,CAAC;AAC1B,wBAAgB,IAAI,GAAG,GAAG,CAAC;AAC3B,uBAAe,KAAK,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC;AACvD,wBAAgB,KAAK,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC;AACxD,mBAAW,IAAI,QAAQ,GAAG,UAAU,CAAC;AAErC,oBAAY,SAAS,IAAI,QAAS,EAAC,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC;AAC9D,0BAAkB,OAAO,cAAc,CAAC,IAAI,KAAK;AACjD,oBAAY,YAAY,IAAI,QAAQ,GAAG,WAAW,gBAAgB,CAAC,CAAC;AACpE;AAAA,MACF,KAAK;AAAA,MACL;AACE,kBAAU,IAAI,GAAG,GAAG,CAAC;AACrB,uBAAe,IAAI,GAAG,GAAG,CAAC;AAC1B,wBAAgB,IAAI,GAAG,IAAI,CAAC;AAC5B,uBAAe,KAAK,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC;AACvD,wBAAgB,KAAK,QAAQ,KAAK,UAAU,QAAQ,GAAG,CAAC;AACxD,mBAAW,IAAI,QAAQ,GAAG,GAAG,QAAQ;AAErC,0BAAkB,OAAO,cAAc,CAAC,IAAI,KAAK;AACjD,oBAAY,YAAY,IAAI,QAAQ,GAAG,GAAG,WAAW,cAAc,CAAC;AACpE;AAAA,IACH;AAED,QAAI,SAAS;AAEb,QAAI,CAAC,KAAK,cAAc;AACtB,qBAAe,aAAa,OAAO,aAAa,EAAE,UAAW;AAC7D,sBAAgB,aAAa,OAAO,aAAa,EAAE,UAAW;AAC9D,gBAAU,aAAa,OAAO,aAAa,EAAE,UAAW;AAAA,IACzD;AACD,mBAAe,SAAS;AACxB,oBAAgB,SAAS;AACzB,cAAU,KAAK,MAAM,KAAK,IAAI,eAAe,IAAI,UAAU,CAAC,CAAC;AAC7D,cAAU,KAAK,MAAM,KAAK,IAAI,gBAAgB,IAAI,UAAU,CAAC,CAAC;AAC9D,UAAM,aAAa,KAAK,IAAI,UAAU,YAAY;AAClD,UAAM,cAAc,KAAK,IAAI,UAAU,aAAa;AAEpD,eAAW,KAAK,IAAI,KAAK,MAAM,SAAS,aAAa,OAAO,aAAa,EAAE,IAAI,SAAS,CAAC,CAAC;AAC1F,UAAM,OAAO,CAAC,IAAI,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC;AAC9E,UAAM,aAAa,CAAC,gBAAgB,iBAAiB,SAAS,EAAE,KAAK,SAAU,GAAG;AAChF,aAAO,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI;AAAA,IACxC,CAAK;AACD,UAAM,aAAa,CAAC,gBAAgB,iBAAiB,SAAS,EAAE,KAAK,SAAU,GAAG;AAChF,aAAO,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI;AAAA,IACxC,CAAK;AACD,UAAM,aAAa,CAAC,gBAAgB,iBAAiB,SAAS,EAAE,KAAK,SAAU,GAAG;AAChF,aAAO,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI;AAAA,IACxC,CAAK;AAED,aAAS,YAAY,GAAG,GAAG;AACzB,YAAM,KAAK,eAAe,YAAY,WAAW,WAAW,WAAW,MAAM,IAAI;AACjF,YAAM,KAAK,eAAe,YAAY,WAAW,WAAW,WAAW,MAAM,IAAI;AACjF,YAAM,KAAK,eAAe,YAAY,WAAW,WAAW,WAAW,MAAM,IAAI;AAIjF,YAAM,UAAU,WAAW,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,OAAO,UAAU,IAAI;AACxE,YAAM,UAAU,WAAW,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,OAAO,UAAU,IAAI;AACxE,YAAM,UAAU,WAAW,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,OAAO,UAAU,IAAI;AAExE,aAAO,OAAO,OAAO,SAAS,SAAS,OAAO;AAAA,IAC/C;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,aAAa,MAAM,OAAO;AACxB,UAAM,QAAQ,IAAI,YAAY,MAAM,OAAO,IAAI;AAC/C,SAAK,UAAU,KAAK,KAAK;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,mBAAmB;AACjB,SAAK,UAAU,QAAQ,SAAU,OAAO;AACtC,YAAM,QAAS;AAAA,IACrB,CAAK;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,gBAAgB;AACd,QAAI,MAAM;AACV,QAAI,MAAM;AAGV,UAAM,WAAW,KAAK,KAAK;AAE3B,QAAI,IAAI;AAER,SAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC7B,UAAI,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,GAAG;AACxB,cAAM,QAAQ,KAAK,KAAK,CAAC;AACzB,cAAM,KAAK,IAAI,KAAK,KAAK;AACzB,cAAM,KAAK,IAAI,KAAK,KAAK;AAAA,MAC1B;AAAA,IACF;AAED,SAAK,MAAM;AACX,SAAK,MAAM;AAEX,WAAO,CAAC,KAAK,GAAG;AAAA,EACjB;AACH;"}