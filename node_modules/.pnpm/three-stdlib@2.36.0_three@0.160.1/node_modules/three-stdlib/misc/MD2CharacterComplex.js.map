{"version": 3, "file": "MD2CharacterComplex.js", "sources": ["../../src/misc/MD2CharacterComplex.js"], "sourcesContent": ["import { Box3, <PERSON><PERSON><PERSON>s, MeshLambertMaterial, Object3D, TextureLoader, UVMapping } from 'three'\nimport { MD2Loader } from '../loaders/MD2Loader'\nimport { MorphBlendMesh } from '../misc/MorphBlendMesh'\n\nclass MD2CharacterComplex {\n  constructor() {\n    this.scale = 1\n\n    // animation parameters\n\n    this.animationFPS = 6\n    this.transitionFrames = 15\n\n    // movement model parameters\n\n    this.maxSpeed = 275\n    this.maxReverseSpeed = -275\n\n    this.frontAcceleration = 600\n    this.backAcceleration = 600\n\n    this.frontDecceleration = 600\n\n    this.angularSpeed = 2.5\n\n    // rig\n\n    this.root = new Object3D()\n\n    this.meshBody = null\n    this.meshWeapon = null\n\n    this.controls = null\n\n    // skins\n\n    this.skinsBody = []\n    this.skinsWeapon = []\n\n    this.weapons = []\n\n    this.currentSkin = undefined\n\n    //\n\n    this.onLoadComplete = function () {}\n\n    // internals\n\n    this.meshes = []\n    this.animations = {}\n\n    this.loadCounter = 0\n\n    // internal movement control variables\n\n    this.speed = 0\n    this.bodyOrientation = 0\n\n    this.walkSpeed = this.maxSpeed\n    this.crouchSpeed = this.maxSpeed * 0.5\n\n    // internal animation parameters\n\n    this.activeAnimation = null\n    this.oldAnimation = null\n\n    // API\n  }\n\n  enableShadows(enable) {\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.meshes[i].castShadow = enable\n      this.meshes[i].receiveShadow = enable\n    }\n  }\n\n  setVisible(enable) {\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.meshes[i].visible = enable\n      this.meshes[i].visible = enable\n    }\n  }\n\n  shareParts(original) {\n    this.animations = original.animations\n    this.walkSpeed = original.walkSpeed\n    this.crouchSpeed = original.crouchSpeed\n\n    this.skinsBody = original.skinsBody\n    this.skinsWeapon = original.skinsWeapon\n\n    // BODY\n\n    const mesh = this._createPart(original.meshBody.geometry, this.skinsBody[0])\n    mesh.scale.set(this.scale, this.scale, this.scale)\n\n    this.root.position.y = original.root.position.y\n    this.root.add(mesh)\n\n    this.meshBody = mesh\n\n    this.meshes.push(mesh)\n\n    // WEAPONS\n\n    for (let i = 0; i < original.weapons.length; i++) {\n      const meshWeapon = this._createPart(original.weapons[i].geometry, this.skinsWeapon[i])\n      meshWeapon.scale.set(this.scale, this.scale, this.scale)\n      meshWeapon.visible = false\n\n      meshWeapon.name = original.weapons[i].name\n\n      this.root.add(meshWeapon)\n\n      this.weapons[i] = meshWeapon\n      this.meshWeapon = meshWeapon\n\n      this.meshes.push(meshWeapon)\n    }\n  }\n\n  loadParts(config) {\n    const scope = this\n\n    function loadTextures(baseUrl, textureUrls) {\n      const textureLoader = new TextureLoader()\n      const textures = []\n\n      for (let i = 0; i < textureUrls.length; i++) {\n        textures[i] = textureLoader.load(baseUrl + textureUrls[i], checkLoadingComplete)\n        textures[i].mapping = UVMapping\n        textures[i].name = textureUrls[i]\n        if ('colorSpace' in textures[i]) textures[i].colorSpace = 'srgb'\n        else textures[i].encoding = 3001 // sRGBEncoding\n      }\n\n      return textures\n    }\n\n    function checkLoadingComplete() {\n      scope.loadCounter -= 1\n      if (scope.loadCounter === 0) scope.onLoadComplete()\n    }\n\n    this.animations = config.animations\n    this.walkSpeed = config.walkSpeed\n    this.crouchSpeed = config.crouchSpeed\n\n    this.loadCounter = config.weapons.length * 2 + config.skins.length + 1\n\n    const weaponsTextures = []\n    for (let i = 0; i < config.weapons.length; i++) weaponsTextures[i] = config.weapons[i][1]\n\n    // SKINS\n\n    this.skinsBody = loadTextures(config.baseUrl + 'skins/', config.skins)\n    this.skinsWeapon = loadTextures(config.baseUrl + 'skins/', weaponsTextures)\n\n    // BODY\n\n    const loader = new MD2Loader()\n\n    loader.load(config.baseUrl + config.body, function (geo) {\n      const boundingBox = new Box3()\n      boundingBox.setFromBufferAttribute(geo.attributes.position)\n\n      scope.root.position.y = -scope.scale * boundingBox.min.y\n\n      const mesh = scope._createPart(geo, scope.skinsBody[0])\n      mesh.scale.set(scope.scale, scope.scale, scope.scale)\n\n      scope.root.add(mesh)\n\n      scope.meshBody = mesh\n      scope.meshes.push(mesh)\n\n      checkLoadingComplete()\n    })\n\n    // WEAPONS\n\n    const generateCallback = function (index, name) {\n      return function (geo) {\n        const mesh = scope._createPart(geo, scope.skinsWeapon[index])\n        mesh.scale.set(scope.scale, scope.scale, scope.scale)\n        mesh.visible = false\n\n        mesh.name = name\n\n        scope.root.add(mesh)\n\n        scope.weapons[index] = mesh\n        scope.meshWeapon = mesh\n        scope.meshes.push(mesh)\n\n        checkLoadingComplete()\n      }\n    }\n\n    for (let i = 0; i < config.weapons.length; i++) {\n      loader.load(config.baseUrl + config.weapons[i][0], generateCallback(i, config.weapons[i][0]))\n    }\n  }\n\n  setPlaybackRate(rate) {\n    if (this.meshBody) this.meshBody.duration = this.meshBody.baseDuration / rate\n    if (this.meshWeapon) this.meshWeapon.duration = this.meshWeapon.baseDuration / rate\n  }\n\n  setWireframe(wireframeEnabled) {\n    if (wireframeEnabled) {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialWireframe\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialWireframe\n    } else {\n      if (this.meshBody) this.meshBody.material = this.meshBody.materialTexture\n      if (this.meshWeapon) this.meshWeapon.material = this.meshWeapon.materialTexture\n    }\n  }\n\n  setSkin(index) {\n    if (this.meshBody && this.meshBody.material.wireframe === false) {\n      this.meshBody.material.map = this.skinsBody[index]\n      this.currentSkin = index\n    }\n  }\n\n  setWeapon(index) {\n    for (let i = 0; i < this.weapons.length; i++) this.weapons[i].visible = false\n\n    const activeWeapon = this.weapons[index]\n\n    if (activeWeapon) {\n      activeWeapon.visible = true\n      this.meshWeapon = activeWeapon\n\n      if (this.activeAnimation) {\n        activeWeapon.playAnimation(this.activeAnimation)\n        this.meshWeapon.setAnimationTime(this.activeAnimation, this.meshBody.getAnimationTime(this.activeAnimation))\n      }\n    }\n  }\n\n  setAnimation(animationName) {\n    if (animationName === this.activeAnimation || !animationName) return\n\n    if (this.meshBody) {\n      this.meshBody.setAnimationWeight(animationName, 0)\n      this.meshBody.playAnimation(animationName)\n\n      this.oldAnimation = this.activeAnimation\n      this.activeAnimation = animationName\n\n      this.blendCounter = this.transitionFrames\n    }\n\n    if (this.meshWeapon) {\n      this.meshWeapon.setAnimationWeight(animationName, 0)\n      this.meshWeapon.playAnimation(animationName)\n    }\n  }\n\n  update(delta) {\n    if (this.controls) this.updateMovementModel(delta)\n\n    if (this.animations) {\n      this.updateBehaviors()\n      this.updateAnimations(delta)\n    }\n  }\n\n  updateAnimations(delta) {\n    let mix = 1\n\n    if (this.blendCounter > 0) {\n      mix = (this.transitionFrames - this.blendCounter) / this.transitionFrames\n      this.blendCounter -= 1\n    }\n\n    if (this.meshBody) {\n      this.meshBody.update(delta)\n\n      this.meshBody.setAnimationWeight(this.activeAnimation, mix)\n      this.meshBody.setAnimationWeight(this.oldAnimation, 1 - mix)\n    }\n\n    if (this.meshWeapon) {\n      this.meshWeapon.update(delta)\n\n      this.meshWeapon.setAnimationWeight(this.activeAnimation, mix)\n      this.meshWeapon.setAnimationWeight(this.oldAnimation, 1 - mix)\n    }\n  }\n\n  updateBehaviors() {\n    const controls = this.controls\n    const animations = this.animations\n\n    let moveAnimation, idleAnimation\n\n    // crouch vs stand\n\n    if (controls.crouch) {\n      moveAnimation = animations['crouchMove']\n      idleAnimation = animations['crouchIdle']\n    } else {\n      moveAnimation = animations['move']\n      idleAnimation = animations['idle']\n    }\n\n    // actions\n\n    if (controls.jump) {\n      moveAnimation = animations['jump']\n      idleAnimation = animations['jump']\n    }\n\n    if (controls.attack) {\n      if (controls.crouch) {\n        moveAnimation = animations['crouchAttack']\n        idleAnimation = animations['crouchAttack']\n      } else {\n        moveAnimation = animations['attack']\n        idleAnimation = animations['attack']\n      }\n    }\n\n    // set animations\n\n    if (controls.moveForward || controls.moveBackward || controls.moveLeft || controls.moveRight) {\n      if (this.activeAnimation !== moveAnimation) {\n        this.setAnimation(moveAnimation)\n      }\n    }\n\n    if (\n      Math.abs(this.speed) < 0.2 * this.maxSpeed &&\n      !(controls.moveLeft || controls.moveRight || controls.moveForward || controls.moveBackward)\n    ) {\n      if (this.activeAnimation !== idleAnimation) {\n        this.setAnimation(idleAnimation)\n      }\n    }\n\n    // set animation direction\n\n    if (controls.moveForward) {\n      if (this.meshBody) {\n        this.meshBody.setAnimationDirectionForward(this.activeAnimation)\n        this.meshBody.setAnimationDirectionForward(this.oldAnimation)\n      }\n\n      if (this.meshWeapon) {\n        this.meshWeapon.setAnimationDirectionForward(this.activeAnimation)\n        this.meshWeapon.setAnimationDirectionForward(this.oldAnimation)\n      }\n    }\n\n    if (controls.moveBackward) {\n      if (this.meshBody) {\n        this.meshBody.setAnimationDirectionBackward(this.activeAnimation)\n        this.meshBody.setAnimationDirectionBackward(this.oldAnimation)\n      }\n\n      if (this.meshWeapon) {\n        this.meshWeapon.setAnimationDirectionBackward(this.activeAnimation)\n        this.meshWeapon.setAnimationDirectionBackward(this.oldAnimation)\n      }\n    }\n  }\n\n  updateMovementModel(delta) {\n    function exponentialEaseOut(k) {\n      return k === 1 ? 1 : -Math.pow(2, -10 * k) + 1\n    }\n\n    const controls = this.controls\n\n    // speed based on controls\n\n    if (controls.crouch) this.maxSpeed = this.crouchSpeed\n    else this.maxSpeed = this.walkSpeed\n\n    this.maxReverseSpeed = -this.maxSpeed\n\n    if (controls.moveForward)\n      this.speed = MathUtils.clamp(this.speed + delta * this.frontAcceleration, this.maxReverseSpeed, this.maxSpeed)\n    if (controls.moveBackward)\n      this.speed = MathUtils.clamp(this.speed - delta * this.backAcceleration, this.maxReverseSpeed, this.maxSpeed)\n\n    // orientation based on controls\n    // (don't just stand while turning)\n\n    const dir = 1\n\n    if (controls.moveLeft) {\n      this.bodyOrientation += delta * this.angularSpeed\n      this.speed = MathUtils.clamp(\n        this.speed + dir * delta * this.frontAcceleration,\n        this.maxReverseSpeed,\n        this.maxSpeed,\n      )\n    }\n\n    if (controls.moveRight) {\n      this.bodyOrientation -= delta * this.angularSpeed\n      this.speed = MathUtils.clamp(\n        this.speed + dir * delta * this.frontAcceleration,\n        this.maxReverseSpeed,\n        this.maxSpeed,\n      )\n    }\n\n    // speed decay\n\n    if (!(controls.moveForward || controls.moveBackward)) {\n      if (this.speed > 0) {\n        const k = exponentialEaseOut(this.speed / this.maxSpeed)\n        this.speed = MathUtils.clamp(this.speed - k * delta * this.frontDecceleration, 0, this.maxSpeed)\n      } else {\n        const k = exponentialEaseOut(this.speed / this.maxReverseSpeed)\n        this.speed = MathUtils.clamp(this.speed + k * delta * this.backAcceleration, this.maxReverseSpeed, 0)\n      }\n    }\n\n    // displacement\n\n    const forwardDelta = this.speed * delta\n\n    this.root.position.x += Math.sin(this.bodyOrientation) * forwardDelta\n    this.root.position.z += Math.cos(this.bodyOrientation) * forwardDelta\n\n    // steering\n\n    this.root.rotation.y = this.bodyOrientation\n  }\n\n  // internal\n\n  _createPart(geometry, skinMap) {\n    const materialWireframe = new MeshLambertMaterial({\n      color: 0xffaa00,\n      wireframe: true,\n      morphTargets: true,\n      morphNormals: true,\n    })\n    const materialTexture = new MeshLambertMaterial({\n      color: 0xffffff,\n      wireframe: false,\n      map: skinMap,\n      morphTargets: true,\n      morphNormals: true,\n    })\n\n    //\n\n    const mesh = new MorphBlendMesh(geometry, materialTexture)\n    mesh.rotation.y = -Math.PI / 2\n\n    //\n\n    mesh.materialTexture = materialTexture\n    mesh.materialWireframe = materialWireframe\n\n    //\n\n    mesh.autoCreateAnimations(this.animationFPS)\n\n    return mesh\n  }\n}\n\nexport { MD2CharacterComplex }\n"], "names": [], "mappings": ";;;AAIA,MAAM,oBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,QAAQ;AAIb,SAAK,eAAe;AACpB,SAAK,mBAAmB;AAIxB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AAEvB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AAExB,SAAK,qBAAqB;AAE1B,SAAK,eAAe;AAIpB,SAAK,OAAO,IAAI,SAAU;AAE1B,SAAK,WAAW;AAChB,SAAK,aAAa;AAElB,SAAK,WAAW;AAIhB,SAAK,YAAY,CAAE;AACnB,SAAK,cAAc,CAAE;AAErB,SAAK,UAAU,CAAE;AAEjB,SAAK,cAAc;AAInB,SAAK,iBAAiB,WAAY;AAAA,IAAE;AAIpC,SAAK,SAAS,CAAE;AAChB,SAAK,aAAa,CAAE;AAEpB,SAAK,cAAc;AAInB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AAEvB,SAAK,YAAY,KAAK;AACtB,SAAK,cAAc,KAAK,WAAW;AAInC,SAAK,kBAAkB;AACvB,SAAK,eAAe;AAAA,EAGrB;AAAA,EAED,cAAc,QAAQ;AACpB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,WAAK,OAAO,CAAC,EAAE,aAAa;AAC5B,WAAK,OAAO,CAAC,EAAE,gBAAgB;AAAA,IAChC;AAAA,EACF;AAAA,EAED,WAAW,QAAQ;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,WAAK,OAAO,CAAC,EAAE,UAAU;AACzB,WAAK,OAAO,CAAC,EAAE,UAAU;AAAA,IAC1B;AAAA,EACF;AAAA,EAED,WAAW,UAAU;AACnB,SAAK,aAAa,SAAS;AAC3B,SAAK,YAAY,SAAS;AAC1B,SAAK,cAAc,SAAS;AAE5B,SAAK,YAAY,SAAS;AAC1B,SAAK,cAAc,SAAS;AAI5B,UAAM,OAAO,KAAK,YAAY,SAAS,SAAS,UAAU,KAAK,UAAU,CAAC,CAAC;AAC3E,SAAK,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK;AAEjD,SAAK,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS;AAC9C,SAAK,KAAK,IAAI,IAAI;AAElB,SAAK,WAAW;AAEhB,SAAK,OAAO,KAAK,IAAI;AAIrB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,QAAQ,KAAK;AAChD,YAAM,aAAa,KAAK,YAAY,SAAS,QAAQ,CAAC,EAAE,UAAU,KAAK,YAAY,CAAC,CAAC;AACrF,iBAAW,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK,KAAK;AACvD,iBAAW,UAAU;AAErB,iBAAW,OAAO,SAAS,QAAQ,CAAC,EAAE;AAEtC,WAAK,KAAK,IAAI,UAAU;AAExB,WAAK,QAAQ,CAAC,IAAI;AAClB,WAAK,aAAa;AAElB,WAAK,OAAO,KAAK,UAAU;AAAA,IAC5B;AAAA,EACF;AAAA,EAED,UAAU,QAAQ;AAChB,UAAM,QAAQ;AAEd,aAAS,aAAa,SAAS,aAAa;AAC1C,YAAM,gBAAgB,IAAI,cAAe;AACzC,YAAM,WAAW,CAAE;AAEnB,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,iBAAS,CAAC,IAAI,cAAc,KAAK,UAAU,YAAY,CAAC,GAAG,oBAAoB;AAC/E,iBAAS,CAAC,EAAE,UAAU;AACtB,iBAAS,CAAC,EAAE,OAAO,YAAY,CAAC;AAChC,YAAI,gBAAgB,SAAS,CAAC;AAAG,mBAAS,CAAC,EAAE,aAAa;AAAA;AACrD,mBAAS,CAAC,EAAE,WAAW;AAAA,MAC7B;AAED,aAAO;AAAA,IACR;AAED,aAAS,uBAAuB;AAC9B,YAAM,eAAe;AACrB,UAAI,MAAM,gBAAgB;AAAG,cAAM,eAAgB;AAAA,IACpD;AAED,SAAK,aAAa,OAAO;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,cAAc,OAAO;AAE1B,SAAK,cAAc,OAAO,QAAQ,SAAS,IAAI,OAAO,MAAM,SAAS;AAErE,UAAM,kBAAkB,CAAE;AAC1B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ;AAAK,sBAAgB,CAAC,IAAI,OAAO,QAAQ,CAAC,EAAE,CAAC;AAIxF,SAAK,YAAY,aAAa,OAAO,UAAU,UAAU,OAAO,KAAK;AACrE,SAAK,cAAc,aAAa,OAAO,UAAU,UAAU,eAAe;AAI1E,UAAM,SAAS,IAAI,UAAW;AAE9B,WAAO,KAAK,OAAO,UAAU,OAAO,MAAM,SAAU,KAAK;AACvD,YAAM,cAAc,IAAI,KAAM;AAC9B,kBAAY,uBAAuB,IAAI,WAAW,QAAQ;AAE1D,YAAM,KAAK,SAAS,IAAI,CAAC,MAAM,QAAQ,YAAY,IAAI;AAEvD,YAAM,OAAO,MAAM,YAAY,KAAK,MAAM,UAAU,CAAC,CAAC;AACtD,WAAK,MAAM,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAEpD,YAAM,KAAK,IAAI,IAAI;AAEnB,YAAM,WAAW;AACjB,YAAM,OAAO,KAAK,IAAI;AAEtB,2BAAsB;AAAA,IAC5B,CAAK;AAID,UAAM,mBAAmB,SAAU,OAAO,MAAM;AAC9C,aAAO,SAAU,KAAK;AACpB,cAAM,OAAO,MAAM,YAAY,KAAK,MAAM,YAAY,KAAK,CAAC;AAC5D,aAAK,MAAM,IAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACpD,aAAK,UAAU;AAEf,aAAK,OAAO;AAEZ,cAAM,KAAK,IAAI,IAAI;AAEnB,cAAM,QAAQ,KAAK,IAAI;AACvB,cAAM,aAAa;AACnB,cAAM,OAAO,KAAK,IAAI;AAEtB,6BAAsB;AAAA,MACvB;AAAA,IACF;AAED,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ,KAAK;AAC9C,aAAO,KAAK,OAAO,UAAU,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,iBAAiB,GAAG,OAAO,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IAC7F;AAAA,EACF;AAAA,EAED,gBAAgB,MAAM;AACpB,QAAI,KAAK;AAAU,WAAK,SAAS,WAAW,KAAK,SAAS,eAAe;AACzE,QAAI,KAAK;AAAY,WAAK,WAAW,WAAW,KAAK,WAAW,eAAe;AAAA,EAChF;AAAA,EAED,aAAa,kBAAkB;AAC7B,QAAI,kBAAkB;AACpB,UAAI,KAAK;AAAU,aAAK,SAAS,WAAW,KAAK,SAAS;AAC1D,UAAI,KAAK;AAAY,aAAK,WAAW,WAAW,KAAK,WAAW;AAAA,IACtE,OAAW;AACL,UAAI,KAAK;AAAU,aAAK,SAAS,WAAW,KAAK,SAAS;AAC1D,UAAI,KAAK;AAAY,aAAK,WAAW,WAAW,KAAK,WAAW;AAAA,IACjE;AAAA,EACF;AAAA,EAED,QAAQ,OAAO;AACb,QAAI,KAAK,YAAY,KAAK,SAAS,SAAS,cAAc,OAAO;AAC/D,WAAK,SAAS,SAAS,MAAM,KAAK,UAAU,KAAK;AACjD,WAAK,cAAc;AAAA,IACpB;AAAA,EACF;AAAA,EAED,UAAU,OAAO;AACf,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ;AAAK,WAAK,QAAQ,CAAC,EAAE,UAAU;AAExE,UAAM,eAAe,KAAK,QAAQ,KAAK;AAEvC,QAAI,cAAc;AAChB,mBAAa,UAAU;AACvB,WAAK,aAAa;AAElB,UAAI,KAAK,iBAAiB;AACxB,qBAAa,cAAc,KAAK,eAAe;AAC/C,aAAK,WAAW,iBAAiB,KAAK,iBAAiB,KAAK,SAAS,iBAAiB,KAAK,eAAe,CAAC;AAAA,MAC5G;AAAA,IACF;AAAA,EACF;AAAA,EAED,aAAa,eAAe;AAC1B,QAAI,kBAAkB,KAAK,mBAAmB,CAAC;AAAe;AAE9D,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,mBAAmB,eAAe,CAAC;AACjD,WAAK,SAAS,cAAc,aAAa;AAEzC,WAAK,eAAe,KAAK;AACzB,WAAK,kBAAkB;AAEvB,WAAK,eAAe,KAAK;AAAA,IAC1B;AAED,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,mBAAmB,eAAe,CAAC;AACnD,WAAK,WAAW,cAAc,aAAa;AAAA,IAC5C;AAAA,EACF;AAAA,EAED,OAAO,OAAO;AACZ,QAAI,KAAK;AAAU,WAAK,oBAAoB,KAAK;AAEjD,QAAI,KAAK,YAAY;AACnB,WAAK,gBAAiB;AACtB,WAAK,iBAAiB,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EAED,iBAAiB,OAAO;AACtB,QAAI,MAAM;AAEV,QAAI,KAAK,eAAe,GAAG;AACzB,aAAO,KAAK,mBAAmB,KAAK,gBAAgB,KAAK;AACzD,WAAK,gBAAgB;AAAA,IACtB;AAED,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,OAAO,KAAK;AAE1B,WAAK,SAAS,mBAAmB,KAAK,iBAAiB,GAAG;AAC1D,WAAK,SAAS,mBAAmB,KAAK,cAAc,IAAI,GAAG;AAAA,IAC5D;AAED,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,OAAO,KAAK;AAE5B,WAAK,WAAW,mBAAmB,KAAK,iBAAiB,GAAG;AAC5D,WAAK,WAAW,mBAAmB,KAAK,cAAc,IAAI,GAAG;AAAA,IAC9D;AAAA,EACF;AAAA,EAED,kBAAkB;AAChB,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK;AAExB,QAAI,eAAe;AAInB,QAAI,SAAS,QAAQ;AACnB,sBAAgB,WAAW,YAAY;AACvC,sBAAgB,WAAW,YAAY;AAAA,IAC7C,OAAW;AACL,sBAAgB,WAAW,MAAM;AACjC,sBAAgB,WAAW,MAAM;AAAA,IAClC;AAID,QAAI,SAAS,MAAM;AACjB,sBAAgB,WAAW,MAAM;AACjC,sBAAgB,WAAW,MAAM;AAAA,IAClC;AAED,QAAI,SAAS,QAAQ;AACnB,UAAI,SAAS,QAAQ;AACnB,wBAAgB,WAAW,cAAc;AACzC,wBAAgB,WAAW,cAAc;AAAA,MACjD,OAAa;AACL,wBAAgB,WAAW,QAAQ;AACnC,wBAAgB,WAAW,QAAQ;AAAA,MACpC;AAAA,IACF;AAID,QAAI,SAAS,eAAe,SAAS,gBAAgB,SAAS,YAAY,SAAS,WAAW;AAC5F,UAAI,KAAK,oBAAoB,eAAe;AAC1C,aAAK,aAAa,aAAa;AAAA,MAChC;AAAA,IACF;AAED,QACE,KAAK,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,YAClC,EAAE,SAAS,YAAY,SAAS,aAAa,SAAS,eAAe,SAAS,eAC9E;AACA,UAAI,KAAK,oBAAoB,eAAe;AAC1C,aAAK,aAAa,aAAa;AAAA,MAChC;AAAA,IACF;AAID,QAAI,SAAS,aAAa;AACxB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,6BAA6B,KAAK,eAAe;AAC/D,aAAK,SAAS,6BAA6B,KAAK,YAAY;AAAA,MAC7D;AAED,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,6BAA6B,KAAK,eAAe;AACjE,aAAK,WAAW,6BAA6B,KAAK,YAAY;AAAA,MAC/D;AAAA,IACF;AAED,QAAI,SAAS,cAAc;AACzB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,8BAA8B,KAAK,eAAe;AAChE,aAAK,SAAS,8BAA8B,KAAK,YAAY;AAAA,MAC9D;AAED,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,8BAA8B,KAAK,eAAe;AAClE,aAAK,WAAW,8BAA8B,KAAK,YAAY;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAAA,EAED,oBAAoB,OAAO;AACzB,aAAS,mBAAmB,GAAG;AAC7B,aAAO,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI;AAAA,IAC9C;AAED,UAAM,WAAW,KAAK;AAItB,QAAI,SAAS;AAAQ,WAAK,WAAW,KAAK;AAAA;AACrC,WAAK,WAAW,KAAK;AAE1B,SAAK,kBAAkB,CAAC,KAAK;AAE7B,QAAI,SAAS;AACX,WAAK,QAAQ,UAAU,MAAM,KAAK,QAAQ,QAAQ,KAAK,mBAAmB,KAAK,iBAAiB,KAAK,QAAQ;AAC/G,QAAI,SAAS;AACX,WAAK,QAAQ,UAAU,MAAM,KAAK,QAAQ,QAAQ,KAAK,kBAAkB,KAAK,iBAAiB,KAAK,QAAQ;AAK9G,UAAM,MAAM;AAEZ,QAAI,SAAS,UAAU;AACrB,WAAK,mBAAmB,QAAQ,KAAK;AACrC,WAAK,QAAQ,UAAU;AAAA,QACrB,KAAK,QAAQ,MAAM,QAAQ,KAAK;AAAA,QAChC,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACF;AAED,QAAI,SAAS,WAAW;AACtB,WAAK,mBAAmB,QAAQ,KAAK;AACrC,WAAK,QAAQ,UAAU;AAAA,QACrB,KAAK,QAAQ,MAAM,QAAQ,KAAK;AAAA,QAChC,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACF;AAID,QAAI,EAAE,SAAS,eAAe,SAAS,eAAe;AACpD,UAAI,KAAK,QAAQ,GAAG;AAClB,cAAM,IAAI,mBAAmB,KAAK,QAAQ,KAAK,QAAQ;AACvD,aAAK,QAAQ,UAAU,MAAM,KAAK,QAAQ,IAAI,QAAQ,KAAK,oBAAoB,GAAG,KAAK,QAAQ;AAAA,MACvG,OAAa;AACL,cAAM,IAAI,mBAAmB,KAAK,QAAQ,KAAK,eAAe;AAC9D,aAAK,QAAQ,UAAU,MAAM,KAAK,QAAQ,IAAI,QAAQ,KAAK,kBAAkB,KAAK,iBAAiB,CAAC;AAAA,MACrG;AAAA,IACF;AAID,UAAM,eAAe,KAAK,QAAQ;AAElC,SAAK,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,eAAe,IAAI;AACzD,SAAK,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,eAAe,IAAI;AAIzD,SAAK,KAAK,SAAS,IAAI,KAAK;AAAA,EAC7B;AAAA;AAAA,EAID,YAAY,UAAU,SAAS;AAC7B,UAAM,oBAAoB,IAAI,oBAAoB;AAAA,MAChD,OAAO;AAAA,MACP,WAAW;AAAA,MACX,cAAc;AAAA,MACd,cAAc;AAAA,IACpB,CAAK;AACD,UAAM,kBAAkB,IAAI,oBAAoB;AAAA,MAC9C,OAAO;AAAA,MACP,WAAW;AAAA,MACX,KAAK;AAAA,MACL,cAAc;AAAA,MACd,cAAc;AAAA,IACpB,CAAK;AAID,UAAM,OAAO,IAAI,eAAe,UAAU,eAAe;AACzD,SAAK,SAAS,IAAI,CAAC,KAAK,KAAK;AAI7B,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AAIzB,SAAK,qBAAqB,KAAK,YAAY;AAE3C,WAAO;AAAA,EACR;AACH;"}