{"version": 3, "file": "CSMFrustum.cjs", "sources": ["../../src/csm/CSMFrustum.js"], "sourcesContent": ["import { Vector3, Matrix4 } from 'three'\n\nconst inverseProjectionMatrix = /* @__PURE__ */ new Matrix4()\n\nclass CSMFrustum {\n  constructor(data) {\n    data = data || {}\n\n    this.vertices = {\n      near: [new Vector3(), new Vector3(), new Vector3(), new Vector3()],\n      far: [new Vector3(), new Vector3(), new Vector3(), new Vector3()],\n    }\n\n    if (data.projectionMatrix !== undefined) {\n      this.setFromProjectionMatrix(data.projectionMatrix, data.maxFar || 10000)\n    }\n  }\n\n  setFromProjectionMatrix(projectionMatrix, maxFar) {\n    const isOrthographic = projectionMatrix.elements[2 * 4 + 3] === 0\n\n    inverseProjectionMatrix.copy(projectionMatrix).invert()\n\n    // 3 --- 0  vertices.near/far order\n    // |     |\n    // 2 --- 1\n    // clip space spans from [-1, 1]\n\n    this.vertices.near[0].set(1, 1, -1)\n    this.vertices.near[1].set(1, -1, -1)\n    this.vertices.near[2].set(-1, -1, -1)\n    this.vertices.near[3].set(-1, 1, -1)\n    this.vertices.near.forEach(function (v) {\n      v.applyMatrix4(inverseProjectionMatrix)\n    })\n\n    this.vertices.far[0].set(1, 1, 1)\n    this.vertices.far[1].set(1, -1, 1)\n    this.vertices.far[2].set(-1, -1, 1)\n    this.vertices.far[3].set(-1, 1, 1)\n    this.vertices.far.forEach(function (v) {\n      v.applyMatrix4(inverseProjectionMatrix)\n\n      const absZ = Math.abs(v.z)\n      if (isOrthographic) {\n        v.z *= Math.min(maxFar / absZ, 1.0)\n      } else {\n        v.multiplyScalar(Math.min(maxFar / absZ, 1.0))\n      }\n    })\n\n    return this.vertices\n  }\n\n  split(breaks, target) {\n    while (breaks.length > target.length) {\n      target.push(new CSMFrustum())\n    }\n\n    target.length = breaks.length\n\n    for (let i = 0; i < breaks.length; i++) {\n      const cascade = target[i]\n\n      if (i === 0) {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.near[j].copy(this.vertices.near[j])\n        }\n      } else {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.near[j].lerpVectors(this.vertices.near[j], this.vertices.far[j], breaks[i - 1])\n        }\n      }\n\n      if (i === breaks.length - 1) {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.far[j].copy(this.vertices.far[j])\n        }\n      } else {\n        for (let j = 0; j < 4; j++) {\n          cascade.vertices.far[j].lerpVectors(this.vertices.near[j], this.vertices.far[j], breaks[i])\n        }\n      }\n    }\n  }\n\n  toSpace(cameraMatrix, target) {\n    for (let i = 0; i < 4; i++) {\n      target.vertices.near[i].copy(this.vertices.near[i]).applyMatrix4(cameraMatrix)\n\n      target.vertices.far[i].copy(this.vertices.far[i]).applyMatrix4(cameraMatrix)\n    }\n  }\n}\n\nexport { CSMFrustum }\n"], "names": ["Matrix4", "Vector3"], "mappings": ";;;AAEA,MAAM,0BAA0C,oBAAIA,MAAAA,QAAS;AAE7D,MAAM,WAAW;AAAA,EACf,YAAY,MAAM;AAChB,WAAO,QAAQ,CAAE;AAEjB,SAAK,WAAW;AAAA,MACd,MAAM,CAAC,IAAIC,MAAO,QAAA,GAAI,IAAIA,MAAAA,QAAS,GAAE,IAAIA,MAAO,QAAA,GAAI,IAAIA,MAAAA,SAAS;AAAA,MACjE,KAAK,CAAC,IAAIA,MAAO,QAAA,GAAI,IAAIA,MAAAA,QAAS,GAAE,IAAIA,MAAO,QAAA,GAAI,IAAIA,MAAAA,SAAS;AAAA,IACjE;AAED,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,wBAAwB,KAAK,kBAAkB,KAAK,UAAU,GAAK;AAAA,IACzE;AAAA,EACF;AAAA,EAED,wBAAwB,kBAAkB,QAAQ;AAChD,UAAM,iBAAiB,iBAAiB,SAAS,IAAI,IAAI,CAAC,MAAM;AAEhE,4BAAwB,KAAK,gBAAgB,EAAE,OAAQ;AAOvD,SAAK,SAAS,KAAK,CAAC,EAAE,IAAI,GAAG,GAAG,EAAE;AAClC,SAAK,SAAS,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE;AACnC,SAAK,SAAS,KAAK,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE;AACpC,SAAK,SAAS,KAAK,CAAC,EAAE,IAAI,IAAI,GAAG,EAAE;AACnC,SAAK,SAAS,KAAK,QAAQ,SAAU,GAAG;AACtC,QAAE,aAAa,uBAAuB;AAAA,IAC5C,CAAK;AAED,SAAK,SAAS,IAAI,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC;AAChC,SAAK,SAAS,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC;AACjC,SAAK,SAAS,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC;AAClC,SAAK,SAAS,IAAI,CAAC,EAAE,IAAI,IAAI,GAAG,CAAC;AACjC,SAAK,SAAS,IAAI,QAAQ,SAAU,GAAG;AACrC,QAAE,aAAa,uBAAuB;AAEtC,YAAM,OAAO,KAAK,IAAI,EAAE,CAAC;AACzB,UAAI,gBAAgB;AAClB,UAAE,KAAK,KAAK,IAAI,SAAS,MAAM,CAAG;AAAA,MAC1C,OAAa;AACL,UAAE,eAAe,KAAK,IAAI,SAAS,MAAM,CAAG,CAAC;AAAA,MAC9C;AAAA,IACP,CAAK;AAED,WAAO,KAAK;AAAA,EACb;AAAA,EAED,MAAM,QAAQ,QAAQ;AACpB,WAAO,OAAO,SAAS,OAAO,QAAQ;AACpC,aAAO,KAAK,IAAI,YAAY;AAAA,IAC7B;AAED,WAAO,SAAS,OAAO;AAEvB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,UAAU,OAAO,CAAC;AAExB,UAAI,MAAM,GAAG;AACX,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAQ,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,SAAS,KAAK,CAAC,CAAC;AAAA,QACpD;AAAA,MACT,OAAa;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAQ,SAAS,KAAK,CAAC,EAAE,YAAY,KAAK,SAAS,KAAK,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,QAChG;AAAA,MACF;AAED,UAAI,MAAM,OAAO,SAAS,GAAG;AAC3B,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAQ,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,QAClD;AAAA,MACT,OAAa;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAQ,SAAS,IAAI,CAAC,EAAE,YAAY,KAAK,SAAS,KAAK,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QAC3F;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAED,QAAQ,cAAc,QAAQ;AAC5B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAO,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,SAAS,KAAK,CAAC,CAAC,EAAE,aAAa,YAAY;AAE7E,aAAO,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,aAAa,YAAY;AAAA,IAC5E;AAAA,EACF;AACH;;"}