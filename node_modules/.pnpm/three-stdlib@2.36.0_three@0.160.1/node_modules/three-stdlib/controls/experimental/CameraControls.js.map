{"version": 3, "file": "CameraControls.js", "sources": ["../../../src/controls/experimental/CameraControls.ts"], "sourcesContent": ["import {\n  MOUSE,\n  Matrix4,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n} from 'three'\nimport { EventDispatcher } from '../EventDispatcher'\n\nexport type CHANGE_EVENT = {\n  type: 'change' | 'start' | 'end'\n}\n\nexport const STATE = {\n  NONE: -1,\n  ROTATE: 0,\n  DOLLY: 1,\n  PAN: 2,\n  TOUCH_ROTATE: 3,\n  TOUCH_PAN: 4,\n  TOUCH_DOLLY_PAN: 5,\n  TOUCH_DOLLY_ROTATE: 6,\n}\n\nclass CameraControls extends EventDispatcher<Record<string, {}>> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement\n\n  /** Set to false to disable this control */\n  enabled = true\n\n  /** \"target\" sets the location of focus, where the object orbits around */\n  target = new Vector3()\n\n  /** Set to true to enable trackball behavior */\n  trackball = false\n\n  /** How far you can dolly in ( PerspectiveCamera only ) */\n  minDistance = 0\n  /** How far you can dolly out ( PerspectiveCamera only ) */\n  maxDistance = Infinity\n\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0\n  maxPolarAngle = Math.PI\n\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, must be a sub-interval of the interval [ - Math.PI, Math.PI ].\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n\n  /**\n   * This option enables dollying in and out; property named as \"zoom\" for backwards compatibility\n   * Set to false to disable zooming\n   */\n  enableZoom = true\n  zoomSpeed = 1.0\n\n  /** Set to false to disable rotating */\n  enableRotate = true\n  rotateSpeed = 1.0\n\n  /** Set to false to disable panning */\n  enablePan = true\n  panSpeed = 1.0\n  /** if true, pan in screen-space */\n  screenSpacePanning = false\n  /** pixels moved per arrow key push */\n  keyPanSpeed = 7.0\n\n  /**\n   * Set to true to automatically rotate around the target\n   * If auto-rotate is enabled, you must call controls.update() in your animation loop\n   * auto-rotate is not supported for trackball behavior\n   */\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per round when fps is 60\n\n  /** Set to false to disable use of the keys */\n  enableKeys = true\n\n  /** The four arrow keys */\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n\n  mouseButtons: {\n    LEFT: MOUSE\n    MIDDLE?: MOUSE\n    RIGHT: MOUSE\n  }\n\n  /** Touch fingers */\n  touches = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n\n  // for reset\n  target0: Vector3\n  position0: Vector3\n  quaternion0: Quaternion\n  zoom0: number\n\n  // current position in spherical coordinates\n  spherical = new Spherical()\n  sphericalDelta = new Spherical()\n\n  private changeEvent = { type: 'change' }\n  private startEvent = { type: 'start' }\n  private endEvent = { type: 'end' }\n  private state = STATE.NONE\n\n  private EPS = 0.000001\n\n  private scale = 1\n  private panOffset = new Vector3()\n  private zoomChanged = false\n\n  private rotateStart = new Vector2()\n  private rotateEnd = new Vector2()\n  private rotateDelta = new Vector2()\n\n  private panStart = new Vector2()\n  private panEnd = new Vector2()\n  private panDelta = new Vector2()\n\n  private dollyStart = new Vector2()\n  private dollyEnd = new Vector2()\n  private dollyDelta = new Vector2()\n\n  private offset = new Vector3()\n\n  private lastPosition = new Vector3()\n  private lastQuaternion = new Quaternion()\n\n  private q = new Quaternion()\n  private v = new Vector3()\n  private vec = new Vector3()\n\n  private quat: Quaternion\n  private quatInverse: Quaternion\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super()\n\n    if (domElement === undefined) {\n      console.warn('THREE.CameraControls: The second parameter \"domElement\" is now mandatory.')\n    }\n    if (domElement instanceof Document) {\n      console.error(\n        'THREE.CameraControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n\n    this.object = object\n    this.domElement = domElement\n\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      MIDDLE: MOUSE.DOLLY,\n      RIGHT: MOUSE.PAN,\n    }\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.quaternion0 = this.object.quaternion.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // internals\n    //\n\n    // so camera.up is the orbit axis\n    this.quat = new Quaternion().setFromUnitVectors(this.object.up, new Vector3(0, 1, 0))\n    this.quatInverse = this.quat.clone().invert()\n\n    this.lastPosition = new Vector3()\n    this.lastQuaternion = new Quaternion()\n\n    this.domElement.addEventListener('contextmenu', this.onContextMenu, false)\n\n    this.domElement.addEventListener('mousedown', this.onMouseDown, false)\n    this.domElement.addEventListener('wheel', this.onMouseWheel, false)\n\n    this.domElement.addEventListener('touchstart', this.onTouchStart, false)\n    this.domElement.addEventListener('touchend', this.onTouchEnd, false)\n    this.domElement.addEventListener('touchmove', this.onTouchMove, false)\n\n    this.domElement.addEventListener('keydown', this.onKeyDown, false)\n\n    // make sure element can receive keys.\n\n    if (this.domElement.tabIndex === -1) {\n      this.domElement.tabIndex = 0\n    }\n\n    // force an update at start\n\n    this.object.lookAt(this.target)\n    this.update()\n    this.saveState()\n  }\n\n  getPolarAngle = (): number => this.spherical.phi\n\n  getAzimuthalAngle = (): number => this.spherical.theta\n\n  saveState = (): void => {\n    this.target0.copy(this.target)\n    this.position0.copy(this.object.position)\n    this.quaternion0.copy(this.object.quaternion)\n    this.zoom0 = this.object.zoom\n  }\n\n  reset = (): void => {\n    this.target.copy(this.target0)\n    this.object.position.copy(this.position0)\n    this.object.quaternion.copy(this.quaternion0)\n    this.object.zoom = this.zoom0\n\n    this.object.updateProjectionMatrix()\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n\n    this.update()\n\n    this.state = STATE.NONE\n  }\n\n  dispose = (): void => {\n    this.domElement.removeEventListener('contextmenu', this.onContextMenu, false)\n    this.domElement.removeEventListener('mousedown', this.onMouseDown, false)\n    this.domElement.removeEventListener('wheel', this.onMouseWheel, false)\n\n    this.domElement.removeEventListener('touchstart', this.onTouchStart, false)\n    this.domElement.removeEventListener('touchend', this.onTouchEnd, false)\n    this.domElement.removeEventListener('touchmove', this.onTouchMove, false)\n\n    document.removeEventListener('mousemove', this.onMouseMove, false)\n    document.removeEventListener('mouseup', this.onMouseUp, false)\n\n    this.domElement.removeEventListener('keydown', this.onKeyDown, false)\n\n    //this.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n  }\n\n  private update = (): boolean => {\n    const position = this.object.position\n\n    this.offset.copy(position).sub(this.target)\n\n    if (this.trackball) {\n      // rotate around screen-space y-axis\n\n      if (this.sphericalDelta.theta) {\n        this.vec.set(0, 1, 0).applyQuaternion(this.object.quaternion)\n\n        const factor = this.enableDamping ? this.dampingFactor : 1\n\n        this.q.setFromAxisAngle(this.vec, this.sphericalDelta.theta * factor)\n\n        this.object.quaternion.premultiply(this.q)\n        this.offset.applyQuaternion(this.q)\n      }\n\n      // rotate around screen-space x-axis\n\n      if (this.sphericalDelta.phi) {\n        this.vec.set(1, 0, 0).applyQuaternion(this.object.quaternion)\n\n        const factor = this.enableDamping ? this.dampingFactor : 1\n\n        this.q.setFromAxisAngle(this.vec, this.sphericalDelta.phi * factor)\n\n        this.object.quaternion.premultiply(this.q)\n        this.offset.applyQuaternion(this.q)\n      }\n\n      this.offset.multiplyScalar(this.scale)\n      this.offset.clampLength(this.minDistance, this.maxDistance)\n    } else {\n      // rotate offset to \"y-axis-is-up\" space\n      this.offset.applyQuaternion(this.quat)\n\n      if (this.autoRotate && this.state === STATE.NONE) {\n        this.rotateLeft(this.getAutoRotationAngle())\n      }\n\n      this.spherical.setFromVector3(this.offset)\n\n      if (this.enableDamping) {\n        this.spherical.theta += this.sphericalDelta.theta * this.dampingFactor\n        this.spherical.phi += this.sphericalDelta.phi * this.dampingFactor\n      } else {\n        this.spherical.theta += this.sphericalDelta.theta\n        this.spherical.phi += this.sphericalDelta.phi\n      }\n\n      // restrict theta to be between desired limits\n      this.spherical.theta = Math.max(this.minAzimuthAngle, Math.min(this.maxAzimuthAngle, this.spherical.theta))\n\n      // restrict phi to be between desired limits\n      this.spherical.phi = Math.max(this.minPolarAngle, Math.min(this.maxPolarAngle, this.spherical.phi))\n\n      this.spherical.makeSafe()\n\n      this.spherical.radius *= this.scale\n\n      // restrict radius to be between desired limits\n      this.spherical.radius = Math.max(this.minDistance, Math.min(this.maxDistance, this.spherical.radius))\n\n      this.offset.setFromSpherical(this.spherical)\n\n      // rotate offset back to \"camera-up-vector-is-up\" space\n      this.offset.applyQuaternion(this.quatInverse)\n    }\n\n    // move target to panned location\n\n    if (this.enableDamping === true) {\n      this.target.addScaledVector(this.panOffset, this.dampingFactor)\n    } else {\n      this.target.add(this.panOffset)\n    }\n\n    position.copy(this.target).add(this.offset)\n\n    if (this.trackball === false) {\n      this.object.lookAt(this.target)\n    }\n\n    if (this.enableDamping === true) {\n      this.sphericalDelta.theta *= 1 - this.dampingFactor\n      this.sphericalDelta.phi *= 1 - this.dampingFactor\n\n      this.panOffset.multiplyScalar(1 - this.dampingFactor)\n    } else {\n      this.sphericalDelta.set(0, 0, 0)\n\n      this.panOffset.set(0, 0, 0)\n    }\n\n    this.scale = 1\n\n    // update condition is:\n    // min(camera displacement, camera rotation in radians)^2 > EPS\n    // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n    if (\n      this.zoomChanged ||\n      this.lastPosition.distanceToSquared(this.object.position) > this.EPS ||\n      8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS\n    ) {\n      // @ts-ignore\n      this.dispatchEvent(this.changeEvent)\n\n      this.lastPosition.copy(this.object.position)\n      this.lastQuaternion.copy(this.object.quaternion)\n      this.zoomChanged = false\n\n      return true\n    }\n\n    return false\n  }\n\n  private getAutoRotationAngle = (): number => ((2 * Math.PI) / 60 / 60) * this.autoRotateSpeed\n\n  private getZoomScale = (): number => Math.pow(0.95, this.zoomSpeed)\n\n  private rotateLeft = (angle: number): void => {\n    this.sphericalDelta.theta -= angle\n  }\n\n  private rotateUp = (angle: number): void => {\n    this.sphericalDelta.phi -= angle\n  }\n\n  private panLeft = (distance: number, objectMatrix: Matrix4): void => {\n    this.v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n    this.v.multiplyScalar(-distance)\n\n    this.panOffset.add(this.v)\n  }\n\n  private panUp = (distance: number, objectMatrix: Matrix4): void => {\n    if (this.screenSpacePanning === true) {\n      this.v.setFromMatrixColumn(objectMatrix, 1)\n    } else {\n      this.v.setFromMatrixColumn(objectMatrix, 0)\n      this.v.crossVectors(this.object.up, this.v)\n    }\n\n    this.v.multiplyScalar(distance)\n\n    this.panOffset.add(this.v)\n  }\n\n  // deltaX and deltaY are in pixels; right and down are positive\n  private pan = (deltaX: number, deltaY: number): void => {\n    const element = this.domElement\n\n    if (this.object instanceof PerspectiveCamera) {\n      // perspective\n      const position = this.object.position\n      this.offset.copy(position).sub(this.target)\n      let targetDistance = this.offset.length()\n\n      // half of the fov is center to top of screen\n      targetDistance *= Math.tan(((this.object.fov / 2) * Math.PI) / 180.0)\n\n      // we use only clientHeight here so aspect ratio does not distort speed\n      this.panLeft((2 * deltaX * targetDistance) / element.clientHeight, this.object.matrix)\n      this.panUp((2 * deltaY * targetDistance) / element.clientHeight, this.object.matrix)\n    } else if (this.object.isOrthographicCamera) {\n      // orthographic\n      this.panLeft(\n        (deltaX * (this.object.right - this.object.left)) / this.object.zoom / element.clientWidth,\n        this.object.matrix,\n      )\n      this.panUp(\n        (deltaY * (this.object.top - this.object.bottom)) / this.object.zoom / element.clientHeight,\n        this.object.matrix,\n      )\n    } else {\n      // camera neither orthographic nor perspective\n      console.warn('WARNING: CameraControls.js encountered an unknown camera type - pan disabled.')\n      this.enablePan = false\n    }\n  }\n\n  private dollyIn = (dollyScale: number): void => {\n    // TODO: replace w/.isPerspectiveCamera ?\n    if (this.object instanceof PerspectiveCamera) {\n      this.scale /= dollyScale\n      // TODO: replace w/.isOrthographicCamera ?\n    } else if (this.object instanceof OrthographicCamera) {\n      this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom * dollyScale))\n      this.object.updateProjectionMatrix()\n      this.zoomChanged = true\n    } else {\n      console.warn('WARNING: CameraControls.js encountered an unknown camera type - dolly/zoom disabled.')\n      this.enableZoom = false\n    }\n  }\n\n  private dollyOut = (dollyScale: number): void => {\n    // TODO: replace w/.isPerspectiveCamera ?\n    if (this.object instanceof PerspectiveCamera) {\n      this.scale *= dollyScale\n      // TODO: replace w/.isOrthographicCamera ?\n    } else if (this.object instanceof OrthographicCamera) {\n      this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / dollyScale))\n      this.object.updateProjectionMatrix()\n      this.zoomChanged = true\n    } else {\n      console.warn('WARNING: CameraControls.js encountered an unknown camera type - dolly/zoom disabled.')\n      this.enableZoom = false\n    }\n  }\n\n  // event callbacks - update the object state\n\n  private handleMouseDownRotate = (event: MouseEvent): void => {\n    this.rotateStart.set(event.clientX, event.clientY)\n  }\n\n  // TODO: confirm if worthwhile to return the Vector2 instead of void\n  private handleMouseDownDolly = (event: MouseEvent): void => {\n    this.dollyStart.set(event.clientX, event.clientY)\n  }\n\n  private handleMouseDownPan = (event: MouseEvent): void => {\n    this.panStart.set(event.clientX, event.clientY)\n  }\n\n  private handleMouseMoveRotate = (event: MouseEvent): void => {\n    this.rotateEnd.set(event.clientX, event.clientY)\n\n    this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(this.rotateSpeed)\n\n    const element = this.domElement\n\n    this.rotateLeft((2 * Math.PI * this.rotateDelta.x) / element.clientHeight) // yes, height\n\n    this.rotateUp((2 * Math.PI * this.rotateDelta.y) / element.clientHeight)\n\n    this.rotateStart.copy(this.rotateEnd)\n\n    this.update()\n  }\n\n  private handleMouseMoveDolly = (event: MouseEvent): void => {\n    this.dollyEnd.set(event.clientX, event.clientY)\n\n    this.dollyDelta.subVectors(this.dollyEnd, this.dollyStart)\n\n    if (this.dollyDelta.y > 0) {\n      this.dollyIn(this.getZoomScale())\n    } else if (this.dollyDelta.y < 0) {\n      this.dollyOut(this.getZoomScale())\n    }\n\n    this.dollyStart.copy(this.dollyEnd)\n\n    this.update()\n  }\n\n  private handleMouseMovePan = (event: MouseEvent): void => {\n    this.panEnd.set(event.clientX, event.clientY)\n\n    this.panDelta.subVectors(this.panEnd, this.panStart).multiplyScalar(this.panSpeed)\n\n    this.pan(this.panDelta.x, this.panDelta.y)\n\n    this.panStart.copy(this.panEnd)\n\n    this.update()\n  }\n\n  private handleMouseUp(/*event*/): void {\n    // no-op\n  }\n\n  private handleMouseWheel = (event: WheelEvent): void => {\n    if (event.deltaY < 0) {\n      this.dollyOut(this.getZoomScale())\n    } else if (event.deltaY > 0) {\n      this.dollyIn(this.getZoomScale())\n    }\n\n    this.update()\n  }\n\n  private handleKeyDown = (event: KeyboardEvent): void => {\n    let needsUpdate = false\n\n    switch (event.code) {\n      case this.keys.UP:\n        this.pan(0, this.keyPanSpeed)\n        needsUpdate = true\n        break\n\n      case this.keys.BOTTOM:\n        this.pan(0, -this.keyPanSpeed)\n        needsUpdate = true\n        break\n\n      case this.keys.LEFT:\n        this.pan(this.keyPanSpeed, 0)\n        needsUpdate = true\n        break\n\n      case this.keys.RIGHT:\n        this.pan(-this.keyPanSpeed, 0)\n        needsUpdate = true\n        break\n    }\n\n    if (needsUpdate) {\n      // prevent the browser from scrolling on cursor keys\n      event.preventDefault()\n\n      this.update()\n    }\n  }\n\n  private handleTouchStartRotate = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.rotateStart.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.rotateStart.set(x, y)\n    }\n  }\n\n  private handleTouchStartPan = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.panStart.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.panStart.set(x, y)\n    }\n  }\n\n  private handleTouchStartDolly = (event: TouchEvent): void => {\n    const dx = event.touches[0].pageX - event.touches[1].pageX\n    const dy = event.touches[0].pageY - event.touches[1].pageY\n\n    const distance = Math.sqrt(dx * dx + dy * dy)\n\n    this.dollyStart.set(0, distance)\n  }\n\n  private handleTouchStartDollyPan = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchStartDolly(event)\n\n    if (this.enablePan) this.handleTouchStartPan(event)\n  }\n\n  private handleTouchStartDollyRotate = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchStartDolly(event)\n\n    if (this.enableRotate) this.handleTouchStartRotate(event)\n  }\n\n  private handleTouchMoveRotate = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.rotateEnd.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.rotateEnd.set(x, y)\n    }\n\n    this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart).multiplyScalar(this.rotateSpeed)\n\n    const element = this.domElement\n\n    this.rotateLeft((2 * Math.PI * this.rotateDelta.x) / element.clientHeight) // yes, height\n\n    this.rotateUp((2 * Math.PI * this.rotateDelta.y) / element.clientHeight)\n\n    this.rotateStart.copy(this.rotateEnd)\n  }\n\n  private handleTouchMovePan = (event: TouchEvent): void => {\n    if (event.touches.length == 1) {\n      this.panEnd.set(event.touches[0].pageX, event.touches[0].pageY)\n    } else {\n      const x = 0.5 * (event.touches[0].pageX + event.touches[1].pageX)\n      const y = 0.5 * (event.touches[0].pageY + event.touches[1].pageY)\n\n      this.panEnd.set(x, y)\n    }\n\n    this.panDelta.subVectors(this.panEnd, this.panStart).multiplyScalar(this.panSpeed)\n\n    this.pan(this.panDelta.x, this.panDelta.y)\n\n    this.panStart.copy(this.panEnd)\n  }\n\n  private handleTouchMoveDolly = (event: TouchEvent): void => {\n    const dx = event.touches[0].pageX - event.touches[1].pageX\n    const dy = event.touches[0].pageY - event.touches[1].pageY\n\n    const distance = Math.sqrt(dx * dx + dy * dy)\n\n    this.dollyEnd.set(0, distance)\n\n    this.dollyDelta.set(0, Math.pow(this.dollyEnd.y / this.dollyStart.y, this.zoomSpeed))\n\n    this.dollyIn(this.dollyDelta.y)\n\n    this.dollyStart.copy(this.dollyEnd)\n  }\n\n  private handleTouchMoveDollyPan = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchMoveDolly(event)\n\n    if (this.enablePan) this.handleTouchMovePan(event)\n  }\n\n  private handleTouchMoveDollyRotate = (event: TouchEvent): void => {\n    if (this.enableZoom) this.handleTouchMoveDolly(event)\n\n    if (this.enableRotate) this.handleTouchMoveRotate(event)\n  }\n\n  private handleTouchEnd(/*event*/): void {\n    // no-op\n  }\n\n  //\n  // event handlers - FSM: listen for events and reset state\n  //\n\n  private onMouseDown = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    // Prevent the browser from scrolling.\n\n    event.preventDefault()\n\n    // Manually set the focus since calling preventDefault above\n    // prevents the browser from setting it automatically.\n\n    this.domElement.focus ? this.domElement.focus() : window.focus()\n\n    let mouseAction\n\n    switch (event.button) {\n      case 0:\n        mouseAction = this.mouseButtons.LEFT\n        break\n\n      case 1:\n        mouseAction = this.mouseButtons.MIDDLE\n        break\n\n      case 2:\n        mouseAction = this.mouseButtons.RIGHT\n        break\n\n      default:\n        mouseAction = -1\n    }\n\n    switch (mouseAction) {\n      case MOUSE.DOLLY:\n        if (this.enableZoom === false) return\n\n        this.handleMouseDownDolly(event)\n\n        this.state = STATE.DOLLY\n\n        break\n\n      case MOUSE.ROTATE:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enablePan === false) return\n\n          this.handleMouseDownPan(event)\n\n          this.state = STATE.PAN\n        } else {\n          if (this.enableRotate === false) return\n\n          this.handleMouseDownRotate(event)\n\n          this.state = STATE.ROTATE\n        }\n\n        break\n\n      case MOUSE.PAN:\n        if (event.ctrlKey || event.metaKey || event.shiftKey) {\n          if (this.enableRotate === false) return\n\n          this.handleMouseDownRotate(event)\n\n          this.state = STATE.ROTATE\n        } else {\n          if (this.enablePan === false) return\n\n          this.handleMouseDownPan(event)\n\n          this.state = STATE.PAN\n        }\n\n        break\n\n      default:\n        this.state = STATE.NONE\n    }\n\n    if (this.state !== STATE.NONE) {\n      document.addEventListener('mousemove', this.onMouseMove, false)\n      document.addEventListener('mouseup', this.onMouseUp, false)\n\n      // @ts-ignore\n      this.dispatchEvent(this.startEvent)\n    }\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (this.state) {\n      case STATE.ROTATE:\n        if (this.enableRotate === false) return\n\n        this.handleMouseMoveRotate(event)\n\n        break\n\n      case STATE.DOLLY:\n        if (this.enableZoom === false) return\n\n        this.handleMouseMoveDolly(event)\n\n        break\n\n      case STATE.PAN:\n        if (this.enablePan === false) return\n\n        this.handleMouseMovePan(event)\n\n        break\n    }\n  }\n\n  private onMouseUp = (): void => {\n    if (this.enabled === false) return\n\n    // this.handleMouseUp()\n\n    document.removeEventListener('mousemove', this.onMouseMove, false)\n    document.removeEventListener('mouseup', this.onMouseUp, false)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n\n    this.state = STATE.NONE\n  }\n\n  private onMouseWheel = (event: WheelEvent): void => {\n    if (\n      this.enabled === false ||\n      this.enableZoom === false ||\n      (this.state !== STATE.NONE && this.state !== STATE.ROTATE)\n    ) {\n      return\n    }\n\n    event.preventDefault()\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n\n    this.handleMouseWheel(event)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private onKeyDown = (event: KeyboardEvent): void => {\n    if (this.enabled === false || this.enableKeys === false || this.enablePan === false) return\n\n    this.handleKeyDown(event)\n  }\n\n  private onTouchStart = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        switch (this.touches.ONE) {\n          case TOUCH.ROTATE:\n            if (this.enableRotate === false) return\n\n            this.handleTouchStartRotate(event)\n\n            this.state = STATE.TOUCH_ROTATE\n\n            break\n\n          case TOUCH.PAN:\n            if (this.enablePan === false) return\n\n            this.handleTouchStartPan(event)\n\n            this.state = STATE.TOUCH_PAN\n\n            break\n\n          default:\n            this.state = STATE.NONE\n        }\n\n        break\n\n      case 2:\n        switch (this.touches.TWO) {\n          case TOUCH.DOLLY_PAN:\n            if (this.enableZoom === false && this.enablePan === false) return\n\n            this.handleTouchStartDollyPan(event)\n\n            this.state = STATE.TOUCH_DOLLY_PAN\n\n            break\n\n          case TOUCH.DOLLY_ROTATE:\n            if (this.enableZoom === false && this.enableRotate === false) return\n\n            this.handleTouchStartDollyRotate(event)\n\n            this.state = STATE.TOUCH_DOLLY_ROTATE\n\n            break\n\n          default:\n            this.state = STATE.NONE\n        }\n\n        break\n\n      default:\n        this.state = STATE.NONE\n    }\n\n    if (this.state !== STATE.NONE) {\n      // @ts-ignore\n      this.dispatchEvent(this.startEvent)\n    }\n  }\n\n  private onTouchMove = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (this.state) {\n      case STATE.TOUCH_ROTATE:\n        if (this.enableRotate === false) return\n\n        this.handleTouchMoveRotate(event)\n\n        this.update()\n\n        break\n\n      case STATE.TOUCH_PAN:\n        if (this.enablePan === false) return\n\n        this.handleTouchMovePan(event)\n\n        this.update()\n\n        break\n\n      case STATE.TOUCH_DOLLY_PAN:\n        if (this.enableZoom === false && this.enablePan === false) return\n\n        this.handleTouchMoveDollyPan(event)\n\n        this.update()\n\n        break\n\n      case STATE.TOUCH_DOLLY_ROTATE:\n        if (this.enableZoom === false && this.enableRotate === false) return\n\n        this.handleTouchMoveDollyRotate(event)\n\n        this.update()\n\n        break\n\n      default:\n        this.state = STATE.NONE\n    }\n  }\n\n  private onTouchEnd = (): void => {\n    if (this.enabled === false) return\n\n    // this.handleTouchEnd()\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n\n    this.state = STATE.NONE\n  }\n\n  private onContextMenu = (event: Event): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n  }\n}\n\n/**\n * OrbitControls maintains the \"up\" direction, camera.up (+Y by default).\n *\n * @event Orbit - left mouse / touch: one-finger move\n * @event Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n * @event Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n */\nclass OrbitControlsExp extends CameraControls {\n  mouseButtons: {\n    LEFT: MOUSE\n    RIGHT: MOUSE\n  }\n  touches: {\n    ONE: TOUCH\n    TWO: TOUCH\n  }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super(object, domElement)\n\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      RIGHT: MOUSE.PAN,\n    }\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN,\n    }\n  }\n}\n\n/**\n * MapControls maintains the \"up\" direction, camera.up (+Y by default)\n *\n * @event Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n * @event Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n * @event Pan - left mouse, or left right + ctrl/meta/shiftKey, or arrow keys / touch: one-finger move\n */\nclass MapControlsExp extends CameraControls {\n  mouseButtons: {\n    LEFT: MOUSE\n    RIGHT: MOUSE\n  }\n  touches: {\n    ONE: TOUCH\n    TWO: TOUCH\n  }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super(object, domElement)\n\n    this.mouseButtons = {\n      LEFT: MOUSE.PAN,\n      RIGHT: MOUSE.ROTATE,\n    }\n    this.touches = {\n      ONE: TOUCH.PAN,\n      TWO: TOUCH.DOLLY_ROTATE,\n    }\n  }\n}\n\n/**\n * TrackballControls allows the camera to rotate over the polls and does not maintain camera.up\n *\n * @event Orbit - left mouse / touch: one-finger move\n * @event Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n * @event Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n */\nclass TrackballControlsExp extends CameraControls {\n  trackball: boolean\n  screenSpacePanning: boolean\n  autoRotate: boolean\n  mouseButtons: {\n    LEFT: MOUSE\n    RIGHT: MOUSE\n  }\n  touches: {\n    ONE: TOUCH\n    TWO: TOUCH\n  }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement: HTMLElement) {\n    super(object, domElement)\n\n    this.trackball = true\n    this.screenSpacePanning = true\n    this.autoRotate = false\n\n    this.mouseButtons = {\n      LEFT: MOUSE.ROTATE,\n      RIGHT: MOUSE.PAN,\n    }\n\n    this.touches = {\n      ONE: TOUCH.ROTATE,\n      TWO: TOUCH.DOLLY_PAN,\n    }\n  }\n}\n\nexport { CameraControls, OrbitControlsExp, MapControlsExp, TrackballControlsExp }\n"], "names": [], "mappings": ";;;;;;;;AAiBO,MAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,oBAAoB;AACtB;AAEA,MAAM,uBAAuB,gBAAoC;AAAA,EA4H/D,YAAY,QAAgD,YAAyB;AAC7E;AA5HR;AACA;AAGA;AAAA,mCAAU;AAGV;AAAA,kCAAS,IAAI;AAGb;AAAA,qCAAY;AAGZ;AAAA,uCAAc;AAEd;AAAA,uCAAc;AAGd;AAAA,mCAAU;AACV,mCAAU;AAIV;AAAA;AAAA,yCAAgB;AAChB,yCAAgB,KAAK;AAIrB;AAAA;AAAA,2CAAkB;AAClB;AAAA,2CAAkB;AAIlB;AAAA;AAAA;AAAA,yCAAgB;AAChB,yCAAgB;AAMhB;AAAA;AAAA;AAAA;AAAA,sCAAa;AACb,qCAAY;AAGZ;AAAA,wCAAe;AACf,uCAAc;AAGd;AAAA,qCAAY;AACZ,oCAAW;AAEX;AAAA,8CAAqB;AAErB;AAAA,uCAAc;AAOd;AAAA;AAAA;AAAA;AAAA;AAAA,sCAAa;AACb,2CAAkB;AAGlB;AAAA;AAAA,sCAAa;AAGb;AAAA,gCAAO,EAAE,MAAM,aAAa,IAAI,WAAW,OAAO,cAAc,QAAQ;AAExE;AAOA;AAAA,mCAAU,EAAE,KAAK,MAAM,QAAQ,KAAK,MAAM;AAG1C;AAAA;AACA;AACA;AACA;AAGA;AAAA,qCAAY,IAAI;AAChB,0CAAiB,IAAI;AAEb,uCAAc,EAAE,MAAM;AACtB,sCAAa,EAAE,MAAM;AACrB,oCAAW,EAAE,MAAM;AACnB,iCAAQ,MAAM;AAEd,+BAAM;AAEN,iCAAQ;AACR,qCAAY,IAAI;AAChB,uCAAc;AAEd,uCAAc,IAAI;AAClB,qCAAY,IAAI;AAChB,uCAAc,IAAI;AAElB,oCAAW,IAAI;AACf,kCAAS,IAAI;AACb,oCAAW,IAAI;AAEf,sCAAa,IAAI;AACjB,oCAAW,IAAI;AACf,sCAAa,IAAI;AAEjB,kCAAS,IAAI;AAEb,wCAAe,IAAI;AACnB,0CAAiB,IAAI;AAErB,6BAAI,IAAI;AACR,6BAAI,IAAI;AACR,+BAAM,IAAI;AAEV;AACA;AAgER,yCAAgB,MAAc,KAAK,UAAU;AAE7C,6CAAoB,MAAc,KAAK,UAAU;AAEjD,qCAAY,MAAY;AACjB,WAAA,QAAQ,KAAK,KAAK,MAAM;AAC7B,WAAK,UAAU,KAAK,KAAK,OAAO,QAAQ;AACxC,WAAK,YAAY,KAAK,KAAK,OAAO,UAAU;AACvC,WAAA,QAAQ,KAAK,OAAO;AAAA,IAAA;AAG3B,iCAAQ,MAAY;AACb,WAAA,OAAO,KAAK,KAAK,OAAO;AAC7B,WAAK,OAAO,SAAS,KAAK,KAAK,SAAS;AACxC,WAAK,OAAO,WAAW,KAAK,KAAK,WAAW;AACvC,WAAA,OAAO,OAAO,KAAK;AAExB,WAAK,OAAO;AAEP,WAAA,cAAc,KAAK,WAAW;AAEnC,WAAK,OAAO;AAEZ,WAAK,QAAQ,MAAM;AAAA,IAAA;AAGrB,mCAAU,MAAY;AACpB,WAAK,WAAW,oBAAoB,eAAe,KAAK,eAAe,KAAK;AAC5E,WAAK,WAAW,oBAAoB,aAAa,KAAK,aAAa,KAAK;AACxE,WAAK,WAAW,oBAAoB,SAAS,KAAK,cAAc,KAAK;AAErE,WAAK,WAAW,oBAAoB,cAAc,KAAK,cAAc,KAAK;AAC1E,WAAK,WAAW,oBAAoB,YAAY,KAAK,YAAY,KAAK;AACtE,WAAK,WAAW,oBAAoB,aAAa,KAAK,aAAa,KAAK;AAExE,eAAS,oBAAoB,aAAa,KAAK,aAAa,KAAK;AACjE,eAAS,oBAAoB,WAAW,KAAK,WAAW,KAAK;AAE7D,WAAK,WAAW,oBAAoB,WAAW,KAAK,WAAW,KAAK;AAAA,IAAA;AAK9D,kCAAS,MAAe;AACxB,YAAA,WAAW,KAAK,OAAO;AAE7B,WAAK,OAAO,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM;AAE1C,UAAI,KAAK,WAAW;AAGd,YAAA,KAAK,eAAe,OAAO;AACxB,eAAA,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,gBAAgB,KAAK,OAAO,UAAU;AAE5D,gBAAM,SAAS,KAAK,gBAAgB,KAAK,gBAAgB;AAEzD,eAAK,EAAE,iBAAiB,KAAK,KAAK,KAAK,eAAe,QAAQ,MAAM;AAEpE,eAAK,OAAO,WAAW,YAAY,KAAK,CAAC;AACpC,eAAA,OAAO,gBAAgB,KAAK,CAAC;AAAA,QACpC;AAII,YAAA,KAAK,eAAe,KAAK;AACtB,eAAA,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,gBAAgB,KAAK,OAAO,UAAU;AAE5D,gBAAM,SAAS,KAAK,gBAAgB,KAAK,gBAAgB;AAEzD,eAAK,EAAE,iBAAiB,KAAK,KAAK,KAAK,eAAe,MAAM,MAAM;AAElE,eAAK,OAAO,WAAW,YAAY,KAAK,CAAC;AACpC,eAAA,OAAO,gBAAgB,KAAK,CAAC;AAAA,QACpC;AAEK,aAAA,OAAO,eAAe,KAAK,KAAK;AACrC,aAAK,OAAO,YAAY,KAAK,aAAa,KAAK,WAAW;AAAA,MAAA,OACrD;AAEA,aAAA,OAAO,gBAAgB,KAAK,IAAI;AAErC,YAAI,KAAK,cAAc,KAAK,UAAU,MAAM,MAAM;AAC3C,eAAA,WAAW,KAAK,qBAAsB,CAAA;AAAA,QAC7C;AAEK,aAAA,UAAU,eAAe,KAAK,MAAM;AAEzC,YAAI,KAAK,eAAe;AACtB,eAAK,UAAU,SAAS,KAAK,eAAe,QAAQ,KAAK;AACzD,eAAK,UAAU,OAAO,KAAK,eAAe,MAAM,KAAK;AAAA,QAAA,OAChD;AACA,eAAA,UAAU,SAAS,KAAK,eAAe;AACvC,eAAA,UAAU,OAAO,KAAK,eAAe;AAAA,QAC5C;AAGA,aAAK,UAAU,QAAQ,KAAK,IAAI,KAAK,iBAAiB,KAAK,IAAI,KAAK,iBAAiB,KAAK,UAAU,KAAK,CAAC;AAG1G,aAAK,UAAU,MAAM,KAAK,IAAI,KAAK,eAAe,KAAK,IAAI,KAAK,eAAe,KAAK,UAAU,GAAG,CAAC;AAElG,aAAK,UAAU;AAEV,aAAA,UAAU,UAAU,KAAK;AAG9B,aAAK,UAAU,SAAS,KAAK,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,aAAa,KAAK,UAAU,MAAM,CAAC;AAE/F,aAAA,OAAO,iBAAiB,KAAK,SAAS;AAGtC,aAAA,OAAO,gBAAgB,KAAK,WAAW;AAAA,MAC9C;AAII,UAAA,KAAK,kBAAkB,MAAM;AAC/B,aAAK,OAAO,gBAAgB,KAAK,WAAW,KAAK,aAAa;AAAA,MAAA,OACzD;AACA,aAAA,OAAO,IAAI,KAAK,SAAS;AAAA,MAChC;AAEA,eAAS,KAAK,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM;AAEtC,UAAA,KAAK,cAAc,OAAO;AACvB,aAAA,OAAO,OAAO,KAAK,MAAM;AAAA,MAChC;AAEI,UAAA,KAAK,kBAAkB,MAAM;AAC1B,aAAA,eAAe,SAAS,IAAI,KAAK;AACjC,aAAA,eAAe,OAAO,IAAI,KAAK;AAEpC,aAAK,UAAU,eAAe,IAAI,KAAK,aAAa;AAAA,MAAA,OAC/C;AACL,aAAK,eAAe,IAAI,GAAG,GAAG,CAAC;AAE/B,aAAK,UAAU,IAAI,GAAG,GAAG,CAAC;AAAA,MAC5B;AAEA,WAAK,QAAQ;AAOX,UAAA,KAAK,eACL,KAAK,aAAa,kBAAkB,KAAK,OAAO,QAAQ,IAAI,KAAK,OACjE,KAAK,IAAI,KAAK,eAAe,IAAI,KAAK,OAAO,UAAU,KAAK,KAAK,KACjE;AAEK,aAAA,cAAc,KAAK,WAAW;AAEnC,aAAK,aAAa,KAAK,KAAK,OAAO,QAAQ;AAC3C,aAAK,eAAe,KAAK,KAAK,OAAO,UAAU;AAC/C,aAAK,cAAc;AAEZ,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IAAA;AAGD,gDAAuB,MAAgB,IAAI,KAAK,KAAM,KAAK,KAAM,KAAK;AAEtE,wCAAe,MAAc,KAAK,IAAI,MAAM,KAAK,SAAS;AAE1D,sCAAa,CAAC,UAAwB;AAC5C,WAAK,eAAe,SAAS;AAAA,IAAA;AAGvB,oCAAW,CAAC,UAAwB;AAC1C,WAAK,eAAe,OAAO;AAAA,IAAA;AAGrB,mCAAU,CAAC,UAAkB,iBAAgC;AAC9D,WAAA,EAAE,oBAAoB,cAAc,CAAC;AACrC,WAAA,EAAE,eAAe,CAAC,QAAQ;AAE1B,WAAA,UAAU,IAAI,KAAK,CAAC;AAAA,IAAA;AAGnB,iCAAQ,CAAC,UAAkB,iBAAgC;AAC7D,UAAA,KAAK,uBAAuB,MAAM;AAC/B,aAAA,EAAE,oBAAoB,cAAc,CAAC;AAAA,MAAA,OACrC;AACA,aAAA,EAAE,oBAAoB,cAAc,CAAC;AAC1C,aAAK,EAAE,aAAa,KAAK,OAAO,IAAI,KAAK,CAAC;AAAA,MAC5C;AAEK,WAAA,EAAE,eAAe,QAAQ;AAEzB,WAAA,UAAU,IAAI,KAAK,CAAC;AAAA,IAAA;AAInB;AAAA,+BAAM,CAAC,QAAgB,WAAyB;AACtD,YAAM,UAAU,KAAK;AAEjB,UAAA,KAAK,kBAAkB,mBAAmB;AAEtC,cAAA,WAAW,KAAK,OAAO;AAC7B,aAAK,OAAO,KAAK,QAAQ,EAAE,IAAI,KAAK,MAAM;AACtC,YAAA,iBAAiB,KAAK,OAAO,OAAO;AAGtB,0BAAA,KAAK,IAAM,KAAK,OAAO,MAAM,IAAK,KAAK,KAAM,GAAK;AAG/D,aAAA,QAAS,IAAI,SAAS,iBAAkB,QAAQ,cAAc,KAAK,OAAO,MAAM;AAChF,aAAA,MAAO,IAAI,SAAS,iBAAkB,QAAQ,cAAc,KAAK,OAAO,MAAM;AAAA,MAAA,WAC1E,KAAK,OAAO,sBAAsB;AAEtC,aAAA;AAAA,UACF,UAAU,KAAK,OAAO,QAAQ,KAAK,OAAO,QAAS,KAAK,OAAO,OAAO,QAAQ;AAAA,UAC/E,KAAK,OAAO;AAAA,QAAA;AAET,aAAA;AAAA,UACF,UAAU,KAAK,OAAO,MAAM,KAAK,OAAO,UAAW,KAAK,OAAO,OAAO,QAAQ;AAAA,UAC/E,KAAK,OAAO;AAAA,QAAA;AAAA,MACd,OACK;AAEL,gBAAQ,KAAK,+EAA+E;AAC5F,aAAK,YAAY;AAAA,MACnB;AAAA,IAAA;AAGM,mCAAU,CAAC,eAA6B;AAE1C,UAAA,KAAK,kBAAkB,mBAAmB;AAC5C,aAAK,SAAS;AAAA,MAAA,WAEL,KAAK,kBAAkB,oBAAoB;AACpD,aAAK,OAAO,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO,UAAU,CAAC;AAC/F,aAAK,OAAO;AACZ,aAAK,cAAc;AAAA,MAAA,OACd;AACL,gBAAQ,KAAK,sFAAsF;AACnG,aAAK,aAAa;AAAA,MACpB;AAAA,IAAA;AAGM,oCAAW,CAAC,eAA6B;AAE3C,UAAA,KAAK,kBAAkB,mBAAmB;AAC5C,aAAK,SAAS;AAAA,MAAA,WAEL,KAAK,kBAAkB,oBAAoB;AACpD,aAAK,OAAO,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO,UAAU,CAAC;AAC/F,aAAK,OAAO;AACZ,aAAK,cAAc;AAAA,MAAA,OACd;AACL,gBAAQ,KAAK,sFAAsF;AACnG,aAAK,aAAa;AAAA,MACpB;AAAA,IAAA;AAKM;AAAA,iDAAwB,CAAC,UAA4B;AAC3D,WAAK,YAAY,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAAA;AAI3C;AAAA,gDAAuB,CAAC,UAA4B;AAC1D,WAAK,WAAW,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAAA;AAG1C,8CAAqB,CAAC,UAA4B;AACxD,WAAK,SAAS,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAAA;AAGxC,iDAAwB,CAAC,UAA4B;AAC3D,WAAK,UAAU,IAAI,MAAM,SAAS,MAAM,OAAO;AAE1C,WAAA,YAAY,WAAW,KAAK,WAAW,KAAK,WAAW,EAAE,eAAe,KAAK,WAAW;AAE7F,YAAM,UAAU,KAAK;AAEhB,WAAA,WAAY,IAAI,KAAK,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAEpE,WAAA,SAAU,IAAI,KAAK,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAElE,WAAA,YAAY,KAAK,KAAK,SAAS;AAEpC,WAAK,OAAO;AAAA,IAAA;AAGN,gDAAuB,CAAC,UAA4B;AAC1D,WAAK,SAAS,IAAI,MAAM,SAAS,MAAM,OAAO;AAE9C,WAAK,WAAW,WAAW,KAAK,UAAU,KAAK,UAAU;AAErD,UAAA,KAAK,WAAW,IAAI,GAAG;AACpB,aAAA,QAAQ,KAAK,aAAc,CAAA;AAAA,MACvB,WAAA,KAAK,WAAW,IAAI,GAAG;AAC3B,aAAA,SAAS,KAAK,aAAc,CAAA;AAAA,MACnC;AAEK,WAAA,WAAW,KAAK,KAAK,QAAQ;AAElC,WAAK,OAAO;AAAA,IAAA;AAGN,8CAAqB,CAAC,UAA4B;AACxD,WAAK,OAAO,IAAI,MAAM,SAAS,MAAM,OAAO;AAEvC,WAAA,SAAS,WAAW,KAAK,QAAQ,KAAK,QAAQ,EAAE,eAAe,KAAK,QAAQ;AAEjF,WAAK,IAAI,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAEpC,WAAA,SAAS,KAAK,KAAK,MAAM;AAE9B,WAAK,OAAO;AAAA,IAAA;AAON,4CAAmB,CAAC,UAA4B;AAClD,UAAA,MAAM,SAAS,GAAG;AACf,aAAA,SAAS,KAAK,aAAc,CAAA;AAAA,MAAA,WACxB,MAAM,SAAS,GAAG;AACtB,aAAA,QAAQ,KAAK,aAAc,CAAA;AAAA,MAClC;AAEA,WAAK,OAAO;AAAA,IAAA;AAGN,yCAAgB,CAAC,UAA+B;AACtD,UAAI,cAAc;AAElB,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK,KAAK,KAAK;AACR,eAAA,IAAI,GAAG,KAAK,WAAW;AACd,wBAAA;AACd;AAAA,QAEF,KAAK,KAAK,KAAK;AACb,eAAK,IAAI,GAAG,CAAC,KAAK,WAAW;AACf,wBAAA;AACd;AAAA,QAEF,KAAK,KAAK,KAAK;AACR,eAAA,IAAI,KAAK,aAAa,CAAC;AACd,wBAAA;AACd;AAAA,QAEF,KAAK,KAAK,KAAK;AACb,eAAK,IAAI,CAAC,KAAK,aAAa,CAAC;AACf,wBAAA;AACd;AAAA,MACJ;AAEA,UAAI,aAAa;AAEf,cAAM,eAAe;AAErB,aAAK,OAAO;AAAA,MACd;AAAA,IAAA;AAGM,kDAAyB,CAAC,UAA4B;AACxD,UAAA,MAAM,QAAQ,UAAU,GAAG;AACxB,aAAA,YAAY,IAAI,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,MAAA,OAC9D;AACC,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AACrD,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAEtD,aAAA,YAAY,IAAI,GAAG,CAAC;AAAA,MAC3B;AAAA,IAAA;AAGM,+CAAsB,CAAC,UAA4B;AACrD,UAAA,MAAM,QAAQ,UAAU,GAAG;AACxB,aAAA,SAAS,IAAI,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,MAAA,OAC3D;AACC,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AACrD,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAEtD,aAAA,SAAS,IAAI,GAAG,CAAC;AAAA,MACxB;AAAA,IAAA;AAGM,iDAAwB,CAAC,UAA4B;AACrD,YAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAC/C,YAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAErD,YAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEvC,WAAA,WAAW,IAAI,GAAG,QAAQ;AAAA,IAAA;AAGzB,oDAA2B,CAAC,UAA4B;AAC9D,UAAI,KAAK;AAAY,aAAK,sBAAsB,KAAK;AAErD,UAAI,KAAK;AAAW,aAAK,oBAAoB,KAAK;AAAA,IAAA;AAG5C,uDAA8B,CAAC,UAA4B;AACjE,UAAI,KAAK;AAAY,aAAK,sBAAsB,KAAK;AAErD,UAAI,KAAK;AAAc,aAAK,uBAAuB,KAAK;AAAA,IAAA;AAGlD,iDAAwB,CAAC,UAA4B;AACvD,UAAA,MAAM,QAAQ,UAAU,GAAG;AACxB,aAAA,UAAU,IAAI,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,MAAA,OAC5D;AACC,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AACrD,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAEtD,aAAA,UAAU,IAAI,GAAG,CAAC;AAAA,MACzB;AAEK,WAAA,YAAY,WAAW,KAAK,WAAW,KAAK,WAAW,EAAE,eAAe,KAAK,WAAW;AAE7F,YAAM,UAAU,KAAK;AAEhB,WAAA,WAAY,IAAI,KAAK,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAEpE,WAAA,SAAU,IAAI,KAAK,KAAK,KAAK,YAAY,IAAK,QAAQ,YAAY;AAElE,WAAA,YAAY,KAAK,KAAK,SAAS;AAAA,IAAA;AAG9B,8CAAqB,CAAC,UAA4B;AACpD,UAAA,MAAM,QAAQ,UAAU,GAAG;AACxB,aAAA,OAAO,IAAI,MAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK;AAAA,MAAA,OACzD;AACC,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AACrD,cAAA,IAAI,OAAO,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAEtD,aAAA,OAAO,IAAI,GAAG,CAAC;AAAA,MACtB;AAEK,WAAA,SAAS,WAAW,KAAK,QAAQ,KAAK,QAAQ,EAAE,eAAe,KAAK,QAAQ;AAEjF,WAAK,IAAI,KAAK,SAAS,GAAG,KAAK,SAAS,CAAC;AAEpC,WAAA,SAAS,KAAK,KAAK,MAAM;AAAA,IAAA;AAGxB,gDAAuB,CAAC,UAA4B;AACpD,YAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAC/C,YAAA,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ,MAAM,QAAQ,CAAC,EAAE;AAErD,YAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAEvC,WAAA,SAAS,IAAI,GAAG,QAAQ;AAE7B,WAAK,WAAW,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,IAAI,KAAK,WAAW,GAAG,KAAK,SAAS,CAAC;AAE/E,WAAA,QAAQ,KAAK,WAAW,CAAC;AAEzB,WAAA,WAAW,KAAK,KAAK,QAAQ;AAAA,IAAA;AAG5B,mDAA0B,CAAC,UAA4B;AAC7D,UAAI,KAAK;AAAY,aAAK,qBAAqB,KAAK;AAEpD,UAAI,KAAK;AAAW,aAAK,mBAAmB,KAAK;AAAA,IAAA;AAG3C,sDAA6B,CAAC,UAA4B;AAChE,UAAI,KAAK;AAAY,aAAK,qBAAqB,KAAK;AAEpD,UAAI,KAAK;AAAc,aAAK,sBAAsB,KAAK;AAAA,IAAA;AAWjD;AAAA;AAAA;AAAA,uCAAc,CAAC,UAA4B;AACjD,UAAI,KAAK,YAAY;AAAO;AAI5B,YAAM,eAAe;AAKrB,WAAK,WAAW,QAAQ,KAAK,WAAW,MAAM,IAAI,OAAO;AAErD,UAAA;AAEJ,cAAQ,MAAM,QAAQ;AAAA,QACpB,KAAK;AACH,wBAAc,KAAK,aAAa;AAChC;AAAA,QAEF,KAAK;AACH,wBAAc,KAAK,aAAa;AAChC;AAAA,QAEF,KAAK;AACH,wBAAc,KAAK,aAAa;AAChC;AAAA,QAEF;AACgB,wBAAA;AAAA,MAClB;AAEA,cAAQ,aAAa;AAAA,QACnB,KAAK,MAAM;AACT,cAAI,KAAK,eAAe;AAAO;AAE/B,eAAK,qBAAqB,KAAK;AAE/B,eAAK,QAAQ,MAAM;AAEnB;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,gBAAI,KAAK,cAAc;AAAO;AAE9B,iBAAK,mBAAmB,KAAK;AAE7B,iBAAK,QAAQ,MAAM;AAAA,UAAA,OACd;AACL,gBAAI,KAAK,iBAAiB;AAAO;AAEjC,iBAAK,sBAAsB,KAAK;AAEhC,iBAAK,QAAQ,MAAM;AAAA,UACrB;AAEA;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU;AACpD,gBAAI,KAAK,iBAAiB;AAAO;AAEjC,iBAAK,sBAAsB,KAAK;AAEhC,iBAAK,QAAQ,MAAM;AAAA,UAAA,OACd;AACL,gBAAI,KAAK,cAAc;AAAO;AAE9B,iBAAK,mBAAmB,KAAK;AAE7B,iBAAK,QAAQ,MAAM;AAAA,UACrB;AAEA;AAAA,QAEF;AACE,eAAK,QAAQ,MAAM;AAAA,MACvB;AAEI,UAAA,KAAK,UAAU,MAAM,MAAM;AAC7B,iBAAS,iBAAiB,aAAa,KAAK,aAAa,KAAK;AAC9D,iBAAS,iBAAiB,WAAW,KAAK,WAAW,KAAK;AAGrD,aAAA,cAAc,KAAK,UAAU;AAAA,MACpC;AAAA,IAAA;AAGM,uCAAc,CAAC,UAA4B;AACjD,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAErB,cAAQ,KAAK,OAAO;AAAA,QAClB,KAAK,MAAM;AACT,cAAI,KAAK,iBAAiB;AAAO;AAEjC,eAAK,sBAAsB,KAAK;AAEhC;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,KAAK,eAAe;AAAO;AAE/B,eAAK,qBAAqB,KAAK;AAE/B;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,KAAK,cAAc;AAAO;AAE9B,eAAK,mBAAmB,KAAK;AAE7B;AAAA,MACJ;AAAA,IAAA;AAGM,qCAAY,MAAY;AAC9B,UAAI,KAAK,YAAY;AAAO;AAI5B,eAAS,oBAAoB,aAAa,KAAK,aAAa,KAAK;AACjE,eAAS,oBAAoB,WAAW,KAAK,WAAW,KAAK;AAGxD,WAAA,cAAc,KAAK,QAAQ;AAEhC,WAAK,QAAQ,MAAM;AAAA,IAAA;AAGb,wCAAe,CAAC,UAA4B;AAClD,UACE,KAAK,YAAY,SACjB,KAAK,eAAe,SACnB,KAAK,UAAU,MAAM,QAAQ,KAAK,UAAU,MAAM,QACnD;AACA;AAAA,MACF;AAEA,YAAM,eAAe;AAGhB,WAAA,cAAc,KAAK,UAAU;AAElC,WAAK,iBAAiB,KAAK;AAGtB,WAAA,cAAc,KAAK,QAAQ;AAAA,IAAA;AAG1B,qCAAY,CAAC,UAA+B;AAClD,UAAI,KAAK,YAAY,SAAS,KAAK,eAAe,SAAS,KAAK,cAAc;AAAO;AAErF,WAAK,cAAc,KAAK;AAAA,IAAA;AAGlB,wCAAe,CAAC,UAA4B;AAClD,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAEb,cAAA,MAAM,QAAQ,QAAQ;AAAA,QAC5B,KAAK;AACK,kBAAA,KAAK,QAAQ,KAAK;AAAA,YACxB,KAAK,MAAM;AACT,kBAAI,KAAK,iBAAiB;AAAO;AAEjC,mBAAK,uBAAuB,KAAK;AAEjC,mBAAK,QAAQ,MAAM;AAEnB;AAAA,YAEF,KAAK,MAAM;AACT,kBAAI,KAAK,cAAc;AAAO;AAE9B,mBAAK,oBAAoB,KAAK;AAE9B,mBAAK,QAAQ,MAAM;AAEnB;AAAA,YAEF;AACE,mBAAK,QAAQ,MAAM;AAAA,UACvB;AAEA;AAAA,QAEF,KAAK;AACK,kBAAA,KAAK,QAAQ,KAAK;AAAA,YACxB,KAAK,MAAM;AACT,kBAAI,KAAK,eAAe,SAAS,KAAK,cAAc;AAAO;AAE3D,mBAAK,yBAAyB,KAAK;AAEnC,mBAAK,QAAQ,MAAM;AAEnB;AAAA,YAEF,KAAK,MAAM;AACT,kBAAI,KAAK,eAAe,SAAS,KAAK,iBAAiB;AAAO;AAE9D,mBAAK,4BAA4B,KAAK;AAEtC,mBAAK,QAAQ,MAAM;AAEnB;AAAA,YAEF;AACE,mBAAK,QAAQ,MAAM;AAAA,UACvB;AAEA;AAAA,QAEF;AACE,eAAK,QAAQ,MAAM;AAAA,MACvB;AAEI,UAAA,KAAK,UAAU,MAAM,MAAM;AAExB,aAAA,cAAc,KAAK,UAAU;AAAA,MACpC;AAAA,IAAA;AAGM,uCAAc,CAAC,UAA4B;AACjD,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAErB,cAAQ,KAAK,OAAO;AAAA,QAClB,KAAK,MAAM;AACT,cAAI,KAAK,iBAAiB;AAAO;AAEjC,eAAK,sBAAsB,KAAK;AAEhC,eAAK,OAAO;AAEZ;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,KAAK,cAAc;AAAO;AAE9B,eAAK,mBAAmB,KAAK;AAE7B,eAAK,OAAO;AAEZ;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,KAAK,eAAe,SAAS,KAAK,cAAc;AAAO;AAE3D,eAAK,wBAAwB,KAAK;AAElC,eAAK,OAAO;AAEZ;AAAA,QAEF,KAAK,MAAM;AACT,cAAI,KAAK,eAAe,SAAS,KAAK,iBAAiB;AAAO;AAE9D,eAAK,2BAA2B,KAAK;AAErC,eAAK,OAAO;AAEZ;AAAA,QAEF;AACE,eAAK,QAAQ,MAAM;AAAA,MACvB;AAAA,IAAA;AAGM,sCAAa,MAAY;AAC/B,UAAI,KAAK,YAAY;AAAO;AAKvB,WAAA,cAAc,KAAK,QAAQ;AAEhC,WAAK,QAAQ,MAAM;AAAA,IAAA;AAGb,yCAAgB,CAAC,UAAuB;AAC9C,UAAI,KAAK,YAAY;AAAO;AAE5B,YAAM,eAAe;AAAA,IAAA;AAzzBrB,QAAI,eAAe,QAAW;AAC5B,cAAQ,KAAK,2EAA2E;AAAA,IAC1F;AACA,QAAI,sBAAsB,UAAU;AAC1B,cAAA;AAAA,QACN;AAAA,MAAA;AAAA,IAEJ;AAEA,SAAK,SAAS;AACd,SAAK,aAAa;AAElB,SAAK,eAAe;AAAA,MAClB,MAAM,MAAM;AAAA,MACZ,QAAQ,MAAM;AAAA,MACd,OAAO,MAAM;AAAA,IAAA;AAIV,SAAA,UAAU,KAAK,OAAO,MAAM;AACjC,SAAK,YAAY,KAAK,OAAO,SAAS,MAAM;AAC5C,SAAK,cAAc,KAAK,OAAO,WAAW,MAAM;AAC3C,SAAA,QAAQ,KAAK,OAAO;AAOzB,SAAK,OAAO,IAAI,WAAW,EAAE,mBAAmB,KAAK,OAAO,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC;AACpF,SAAK,cAAc,KAAK,KAAK,MAAA,EAAQ;AAEhC,SAAA,eAAe,IAAI;AACnB,SAAA,iBAAiB,IAAI;AAE1B,SAAK,WAAW,iBAAiB,eAAe,KAAK,eAAe,KAAK;AAEzE,SAAK,WAAW,iBAAiB,aAAa,KAAK,aAAa,KAAK;AACrE,SAAK,WAAW,iBAAiB,SAAS,KAAK,cAAc,KAAK;AAElE,SAAK,WAAW,iBAAiB,cAAc,KAAK,cAAc,KAAK;AACvE,SAAK,WAAW,iBAAiB,YAAY,KAAK,YAAY,KAAK;AACnE,SAAK,WAAW,iBAAiB,aAAa,KAAK,aAAa,KAAK;AAErE,SAAK,WAAW,iBAAiB,WAAW,KAAK,WAAW,KAAK;AAI7D,QAAA,KAAK,WAAW,aAAa,IAAI;AACnC,WAAK,WAAW,WAAW;AAAA,IAC7B;AAIK,SAAA,OAAO,OAAO,KAAK,MAAM;AAC9B,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AAAA,EA+TQ,gBAA+B;AAAA,EAEvC;AAAA,EAyJQ,iBAAgC;AAAA,EAExC;AAsSF;AASA,MAAM,yBAAyB,eAAe;AAAA,EAU5C,YAAY,QAAgD,YAAyB;AACnF,UAAM,QAAQ,UAAU;AAV1B;AAIA;AAQE,SAAK,eAAe;AAAA,MAClB,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,IAAA;AAEf,SAAK,UAAU;AAAA,MACb,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,IAAA;AAAA,EAEf;AACF;AASA,MAAM,uBAAuB,eAAe;AAAA,EAU1C,YAAY,QAAgD,YAAyB;AACnF,UAAM,QAAQ,UAAU;AAV1B;AAIA;AAQE,SAAK,eAAe;AAAA,MAClB,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,IAAA;AAEf,SAAK,UAAU;AAAA,MACb,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,IAAA;AAAA,EAEf;AACF;AASA,MAAM,6BAA6B,eAAe;AAAA,EAahD,YAAY,QAAgD,YAAyB;AACnF,UAAM,QAAQ,UAAU;AAb1B;AACA;AACA;AACA;AAIA;AAQE,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,aAAa;AAElB,SAAK,eAAe;AAAA,MAClB,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,IAAA;AAGf,SAAK,UAAU;AAAA,MACb,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,IAAA;AAAA,EAEf;AACF;"}