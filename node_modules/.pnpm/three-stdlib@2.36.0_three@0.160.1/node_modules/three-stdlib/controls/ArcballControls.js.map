{"version": 3, "file": "ArcballControls.js", "sources": ["../../src/controls/ArcballControls.ts"], "sourcesContent": ["import {\n  GridHelper,\n  EllipseCurve,\n  BufferGeometry,\n  Line,\n  LineBasicMaterial,\n  Raycaster,\n  Group,\n  Box3,\n  Sphere,\n  Quaternion,\n  Vector2,\n  Vector3,\n  Matrix4,\n  MathUtils,\n  Scene,\n  PerspectiveCamera,\n  OrthographicCamera,\n  Mesh,\n  Material,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\ntype Camera = OrthographicCamera | PerspectiveCamera\ntype Operation = 'PAN' | 'ROTATE' | 'ZOOM' | 'FOV'\ntype MouseButtonType = number | 'WHEEL'\ntype ModifierKey = 'CTRL' | 'SHIFT'\ntype MouseAction = {\n  operation: Operation\n  mouse: MouseButtonType\n  key: Modifier<PERSON>ey | null\n}\n\ntype Transformation = {\n  camera: Matrix4 | null\n  gizmos: Matrix4 | null\n}\n\n//trackball state\nconst STATE = {\n  IDLE: Symbol(),\n  ROTATE: Symbol(),\n  PAN: Symbol(),\n  SCALE: Symbol(),\n  FOV: Symbol(),\n  FOCUS: Symbol(),\n  ZROTATE: Symbol(),\n  TOUCH_MULTI: Symbol(),\n  ANIMATION_FOCUS: Symbol(),\n  ANIMATION_ROTATE: Symbol(),\n}\n\nconst INPUT = {\n  NONE: Symbol(),\n  ONE_FINGER: Symbol(),\n  ONE_FINGER_SWITCHED: Symbol(),\n  TWO_FINGER: Symbol(),\n  MULT_FINGER: Symbol(),\n  CURSOR: Symbol(),\n}\n\n//cursor center coordinates\nconst _center = {\n  x: 0,\n  y: 0,\n}\n\n//transformation matrices for gizmos and camera\nconst _transformation: Transformation = {\n  camera: /* @__PURE__ */ new Matrix4(),\n  gizmos: /* @__PURE__ */ new Matrix4(),\n}\n\n//events\nconst _changeEvent = { type: 'change' }\nconst _startEvent = { type: 'start' }\nconst _endEvent = { type: 'end' }\n\n/**\n *\n * @param {CamOrthographicCamera | PerspectiveCameraera} camera Virtual camera used in the scene\n * @param {HTMLElement=null} domElement Renderer's dom element\n * @param {Scene=null} scene The scene to be rendered\n */\nclass ArcballControls extends EventDispatcher<StandardControlsEventMap> {\n  private camera: OrthographicCamera | PerspectiveCamera | null\n  private domElement: HTMLElement | null | undefined\n  private scene: Scene | null | undefined\n\n  private mouseActions: (MouseAction & { state: Symbol })[]\n  private _mouseOp: Operation | null\n\n  private _v2_1: Vector2\n  private _v3_1: Vector3\n  private _v3_2: Vector3\n\n  private _m4_1: Matrix4\n  private _m4_2: Matrix4\n\n  private _quat: Quaternion\n\n  private _translationMatrix: Matrix4\n  private _rotationMatrix: Matrix4\n  private _scaleMatrix: Matrix4\n\n  private _rotationAxis: Vector3\n\n  private _cameraMatrixState: Matrix4\n  private _cameraProjectionState: Matrix4\n\n  private _fovState: number\n  private _upState: Vector3\n  private _zoomState: number\n  private _nearPos: number\n  private _farPos: number\n\n  private _gizmoMatrixState: Matrix4\n\n  private _up0: Vector3\n  private _zoom0: number\n  private _fov0: number\n  private _initialNear: number\n  private _nearPos0: number\n  private _initialFar: number\n  private _farPos0: number\n  private _cameraMatrixState0: Matrix4\n  private _gizmoMatrixState0: Matrix4\n\n  private _button: MouseButtonType\n  private _touchStart: PointerEvent[]\n  private _touchCurrent: PointerEvent[]\n  private _input: Symbol\n\n  private _switchSensibility: number\n  private _startFingerDistance: number\n  private _currentFingerDistance: number\n  private _startFingerRotation: number\n  private _currentFingerRotation: number\n\n  private _devPxRatio: number\n  private _downValid: boolean\n  private _nclicks: number\n  private _downEvents: PointerEvent[]\n  private _clickStart: number\n  private _maxDownTime: number\n  private _maxInterval: number\n  private _posThreshold: number\n  private _movementThreshold: number\n\n  private _currentCursorPosition: Vector3\n  private _startCursorPosition: Vector3\n\n  private _grid: GridHelper | null\n  private _gridPosition: Vector3\n\n  private _gizmos: Group\n  private _curvePts: number\n\n  private _timeStart: number\n  private _animationId: number\n\n  public focusAnimationTime: number\n\n  private _timePrev: number\n  private _timeCurrent: number\n  private _anglePrev: number\n  private _angleCurrent: number\n  private _cursorPosPrev: Vector3\n  private _cursorPosCurr: Vector3\n  private _wPrev: number\n  private _wCurr: number\n\n  public adjustNearFar: boolean\n  public scaleFactor: number\n  public dampingFactor: number\n  public wMax: number\n  public enableAnimations: boolean\n  public enableGrid: boolean\n  public cursorZoom: boolean\n  public minFov: number\n  public maxFov: number\n\n  public enabled: boolean\n  public enablePan: boolean\n  public enableRotate: boolean\n  public enableZoom: boolean\n\n  public minDistance: number\n  public maxDistance: number\n  public minZoom: number\n  public maxZoom: number\n\n  readonly target: Vector3\n  private _currentTarget: Vector3\n\n  private _tbRadius: number\n\n  private _state: Symbol\n\n  constructor(\n    camera: Camera | null,\n    domElement: HTMLElement | null | undefined = null,\n    scene: Scene | null | undefined = null,\n  ) {\n    super()\n    this.camera = null\n    this.domElement = domElement\n    this.scene = scene\n\n    this.mouseActions = []\n    this._mouseOp = null\n\n    //global vectors and matrices that are used in some operations to avoid creating new objects every time (e.g. every time cursor moves)\n    this._v2_1 = new Vector2()\n    this._v3_1 = new Vector3()\n    this._v3_2 = new Vector3()\n\n    this._m4_1 = new Matrix4()\n    this._m4_2 = new Matrix4()\n\n    this._quat = new Quaternion()\n\n    //transformation matrices\n    this._translationMatrix = new Matrix4() //matrix for translation operation\n    this._rotationMatrix = new Matrix4() //matrix for rotation operation\n    this._scaleMatrix = new Matrix4() //matrix for scaling operation\n\n    this._rotationAxis = new Vector3() //axis for rotate operation\n\n    //camera state\n    this._cameraMatrixState = new Matrix4()\n    this._cameraProjectionState = new Matrix4()\n\n    this._fovState = 1\n    this._upState = new Vector3()\n    this._zoomState = 1\n    this._nearPos = 0\n    this._farPos = 0\n\n    this._gizmoMatrixState = new Matrix4()\n\n    //initial values\n    this._up0 = new Vector3()\n    this._zoom0 = 1\n    this._fov0 = 0\n    this._initialNear = 0\n    this._nearPos0 = 0\n    this._initialFar = 0\n    this._farPos0 = 0\n    this._cameraMatrixState0 = new Matrix4()\n    this._gizmoMatrixState0 = new Matrix4()\n\n    //pointers array\n    this._button = -1\n    this._touchStart = []\n    this._touchCurrent = []\n    this._input = INPUT.NONE\n\n    //two fingers touch interaction\n    this._switchSensibility = 32 //minimum movement to be performed to fire single pan start after the second finger has been released\n    this._startFingerDistance = 0 //distance between two fingers\n    this._currentFingerDistance = 0\n    this._startFingerRotation = 0 //amount of rotation performed with two fingers\n    this._currentFingerRotation = 0\n\n    //double tap\n    this._devPxRatio = 0\n    this._downValid = true\n    this._nclicks = 0\n    this._downEvents = []\n    this._clickStart = 0 //first click time\n    this._maxDownTime = 250\n    this._maxInterval = 300\n    this._posThreshold = 24\n    this._movementThreshold = 24\n\n    //cursor positions\n    this._currentCursorPosition = new Vector3()\n    this._startCursorPosition = new Vector3()\n\n    //grid\n    this._grid = null //grid to be visualized during pan operation\n    this._gridPosition = new Vector3()\n\n    //gizmos\n    this._gizmos = new Group()\n    this._curvePts = 128\n\n    //animations\n    this._timeStart = -1 //initial time\n    this._animationId = -1\n\n    //focus animation\n    this.focusAnimationTime = 500 //duration of focus animation in ms\n\n    //rotate animation\n    this._timePrev = 0 //time at which previous rotate operation has been detected\n    this._timeCurrent = 0 //time at which current rotate operation has been detected\n    this._anglePrev = 0 //angle of previous rotation\n    this._angleCurrent = 0 //angle of current rotation\n    this._cursorPosPrev = new Vector3() //cursor position when previous rotate operation has been detected\n    this._cursorPosCurr = new Vector3() //cursor position when current rotate operation has been detected\n    this._wPrev = 0 //angular velocity of the previous rotate operation\n    this._wCurr = 0 //angular velocity of the current rotate operation\n\n    //parameters\n    this.adjustNearFar = false\n    this.scaleFactor = 1.1 //zoom/distance multiplier\n    this.dampingFactor = 25\n    this.wMax = 20 //maximum angular velocity allowed\n    this.enableAnimations = true //if animations should be performed\n    this.enableGrid = false //if grid should be showed during pan operation\n    this.cursorZoom = false //if wheel zoom should be cursor centered\n    this.minFov = 5\n    this.maxFov = 90\n\n    this.enabled = true\n    this.enablePan = true\n    this.enableRotate = true\n    this.enableZoom = true\n\n    this.minDistance = 0\n    this.maxDistance = Infinity\n    this.minZoom = 0\n    this.maxZoom = Infinity\n\n    //trackball parameters\n    this.target = new Vector3(0, 0, 0)\n    this._currentTarget = new Vector3(0, 0, 0)\n\n    this._tbRadius = 1\n\n    //FSA\n    this._state = STATE.IDLE\n\n    this.setCamera(camera)\n\n    if (this.scene) {\n      this.scene.add(this._gizmos)\n    }\n\n    this._devPxRatio = window.devicePixelRatio\n\n    this.initializeMouseActions()\n\n    if (this.domElement) this.connect(this.domElement)\n\n    window.addEventListener('resize', this.onWindowResize)\n  }\n\n  //listeners\n\n  private onWindowResize = (): void => {\n    const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3\n    if (this.camera) {\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n    }\n\n    const newRadius = this._tbRadius / scale\n    // @ts-ignore\n    const curve = new EllipseCurve(0, 0, newRadius, newRadius)\n    const points = curve.getPoints(this._curvePts)\n    const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n    for (const gizmo in this._gizmos.children) {\n      const child = this._gizmos.children[gizmo] as Mesh\n      child.geometry = curveGeometry\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  private onContextMenu = (event: MouseEvent): void => {\n    if (!this.enabled) {\n      return\n    }\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      if (this.mouseActions[i].mouse == 2) {\n        //prevent only if button 2 is actually used\n        event.preventDefault()\n        break\n      }\n    }\n  }\n\n  private onPointerCancel = (): void => {\n    this._touchStart.splice(0, this._touchStart.length)\n    this._touchCurrent.splice(0, this._touchCurrent.length)\n    this._input = INPUT.NONE\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    if (event.button == 0 && event.isPrimary) {\n      this._downValid = true\n      this._downEvents.push(event)\n    } else {\n      this._downValid = false\n    }\n\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      this._touchStart.push(event)\n      this._touchCurrent.push(event)\n\n      switch (this._input) {\n        case INPUT.NONE:\n          //singleStart\n          this._input = INPUT.ONE_FINGER\n          this.onSinglePanStart(event, 'ROTATE')\n\n          window.addEventListener('pointermove', this.onPointerMove)\n          window.addEventListener('pointerup', this.onPointerUp)\n\n          break\n\n        case INPUT.ONE_FINGER:\n        case INPUT.ONE_FINGER_SWITCHED:\n          //doubleStart\n          this._input = INPUT.TWO_FINGER\n\n          this.onRotateStart()\n          this.onPinchStart()\n          this.onDoublePanStart()\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //multipleStart\n          this._input = INPUT.MULT_FINGER\n          this.onTriplePanStart()\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.NONE) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      this._mouseOp = this.getOpFromAction(event.button, modifier)\n      if (this._mouseOp) {\n        window.addEventListener('pointermove', this.onPointerMove)\n        window.addEventListener('pointerup', this.onPointerUp)\n\n        //singleStart\n        this._input = INPUT.CURSOR\n        this._button = event.button\n        this.onSinglePanStart(event, this._mouseOp)\n      }\n    }\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      switch (this._input) {\n        case INPUT.ONE_FINGER:\n          //singleMove\n          this.updateTouchEvent(event)\n\n          this.onSinglePanMove(event, STATE.ROTATE)\n          break\n\n        case INPUT.ONE_FINGER_SWITCHED:\n          const movement = this.calculatePointersDistance(this._touchCurrent[0], event) * this._devPxRatio\n\n          if (movement >= this._switchSensibility) {\n            //singleMove\n            this._input = INPUT.ONE_FINGER\n            this.updateTouchEvent(event)\n\n            this.onSinglePanStart(event, 'ROTATE')\n            break\n          }\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //rotate/pan/pinchMove\n          this.updateTouchEvent(event)\n\n          this.onRotateMove()\n          this.onPinchMove()\n          this.onDoublePanMove()\n\n          break\n\n        case INPUT.MULT_FINGER:\n          //multMove\n          this.updateTouchEvent(event)\n\n          this.onTriplePanMove()\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.CURSOR) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      const mouseOpState = this.getOpStateFromAction(this._button, modifier)\n\n      if (mouseOpState) {\n        this.onSinglePanMove(event, mouseOpState)\n      }\n    }\n\n    //checkDistance\n    if (this._downValid) {\n      const movement =\n        this.calculatePointersDistance(this._downEvents[this._downEvents.length - 1], event) * this._devPxRatio\n      if (movement > this._movementThreshold) {\n        this._downValid = false\n      }\n    }\n  }\n\n  private onPointerUp = (event: PointerEvent): void => {\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      const nTouch = this._touchCurrent.length\n\n      for (let i = 0; i < nTouch; i++) {\n        if (this._touchCurrent[i].pointerId == event.pointerId) {\n          this._touchCurrent.splice(i, 1)\n          this._touchStart.splice(i, 1)\n          break\n        }\n      }\n\n      switch (this._input) {\n        case INPUT.ONE_FINGER:\n        case INPUT.ONE_FINGER_SWITCHED:\n          //singleEnd\n          window.removeEventListener('pointermove', this.onPointerMove)\n          window.removeEventListener('pointerup', this.onPointerUp)\n\n          this._input = INPUT.NONE\n          this.onSinglePanEnd()\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //doubleEnd\n          this.onDoublePanEnd()\n          this.onPinchEnd()\n          this.onRotateEnd()\n\n          //switching to singleStart\n          this._input = INPUT.ONE_FINGER_SWITCHED\n\n          break\n\n        case INPUT.MULT_FINGER:\n          if (this._touchCurrent.length == 0) {\n            window.removeEventListener('pointermove', this.onPointerMove)\n            window.removeEventListener('pointerup', this.onPointerUp)\n\n            //multCancel\n            this._input = INPUT.NONE\n            this.onTriplePanEnd()\n          }\n\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.CURSOR) {\n      window.removeEventListener('pointermove', this.onPointerMove)\n      window.removeEventListener('pointerup', this.onPointerUp)\n\n      this._input = INPUT.NONE\n      this.onSinglePanEnd()\n      this._button = -1\n    }\n\n    if (event.isPrimary) {\n      if (this._downValid) {\n        const downTime = event.timeStamp - this._downEvents[this._downEvents.length - 1].timeStamp\n\n        if (downTime <= this._maxDownTime) {\n          if (this._nclicks == 0) {\n            //first valid click detected\n            this._nclicks = 1\n            this._clickStart = performance.now()\n          } else {\n            const clickInterval = event.timeStamp - this._clickStart\n            const movement = this.calculatePointersDistance(this._downEvents[1], this._downEvents[0]) * this._devPxRatio\n\n            if (clickInterval <= this._maxInterval && movement <= this._posThreshold) {\n              //second valid click detected\n              //fire double tap and reset values\n              this._nclicks = 0\n              this._downEvents.splice(0, this._downEvents.length)\n              this.onDoubleTap(event)\n            } else {\n              //new 'first click'\n              this._nclicks = 1\n              this._downEvents.shift()\n              this._clickStart = performance.now()\n            }\n          }\n        } else {\n          this._downValid = false\n          this._nclicks = 0\n          this._downEvents.splice(0, this._downEvents.length)\n        }\n      } else {\n        this._nclicks = 0\n        this._downEvents.splice(0, this._downEvents.length)\n      }\n    }\n  }\n\n  private onWheel = (event: WheelEvent): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      const mouseOp = this.getOpFromAction('WHEEL', modifier)\n\n      if (mouseOp) {\n        event.preventDefault()\n        // @ts-ignore\n        this.dispatchEvent(_startEvent)\n\n        const notchDeltaY = 125 //distance of one notch of mouse wheel\n        let sgn = event.deltaY / notchDeltaY\n\n        let size = 1\n\n        if (sgn > 0) {\n          size = 1 / this.scaleFactor\n        } else if (sgn < 0) {\n          size = this.scaleFactor\n        }\n\n        switch (mouseOp) {\n          case 'ZOOM':\n            this.updateTbState(STATE.SCALE, true)\n\n            if (sgn > 0) {\n              size = 1 / Math.pow(this.scaleFactor, sgn)\n            } else if (sgn < 0) {\n              size = Math.pow(this.scaleFactor, -sgn)\n            }\n\n            if (this.cursorZoom && this.enablePan) {\n              let scalePoint\n\n              if (this.camera instanceof OrthographicCamera) {\n                scalePoint = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)\n                  ?.applyQuaternion(this.camera.quaternion)\n                  .multiplyScalar(1 / this.camera.zoom)\n                  .add(this._gizmos.position)\n              }\n\n              if (this.camera instanceof PerspectiveCamera) {\n                scalePoint = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)\n                  ?.applyQuaternion(this.camera.quaternion)\n                  .add(this._gizmos.position)\n              }\n\n              if (scalePoint !== undefined) this.applyTransformMatrix(this.applyScale(size, scalePoint))\n            } else {\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n            }\n\n            if (this._grid) {\n              this.disposeGrid()\n              this.drawGrid()\n            }\n\n            this.updateTbState(STATE.IDLE, false)\n\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n            // @ts-ignore\n            this.dispatchEvent(_endEvent)\n\n            break\n\n          case 'FOV':\n            if (this.camera instanceof PerspectiveCamera) {\n              this.updateTbState(STATE.FOV, true)\n\n              //Vertigo effect\n\n              //\t  fov / 2\n              //\t\t|\\\n              //\t\t| \\\n              //\t\t|  \\\n              //\tx\t|\t\\\n              //\t\t| \t \\\n              //\t\t| \t  \\\n              //\t\t| _ _ _\\\n              //\t\t\ty\n\n              //check for iOs shift shortcut\n              if (event.deltaX != 0) {\n                sgn = event.deltaX / notchDeltaY\n\n                size = 1\n\n                if (sgn > 0) {\n                  size = 1 / Math.pow(this.scaleFactor, sgn)\n                } else if (sgn < 0) {\n                  size = Math.pow(this.scaleFactor, -sgn)\n                }\n              }\n\n              this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n              const x = this._v3_1.distanceTo(this._gizmos.position)\n              let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n              //check min and max distance\n              xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n              const y = x * Math.tan(MathUtils.DEG2RAD * this.camera.fov * 0.5)\n\n              //calculate new fov\n              let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n              //check min and max fov\n              if (newFov > this.maxFov) {\n                newFov = this.maxFov\n              } else if (newFov < this.minFov) {\n                newFov = this.minFov\n              }\n\n              const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n              size = x / newDistance\n\n              this.setFov(newFov)\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position, false))\n            }\n\n            if (this._grid) {\n              this.disposeGrid()\n              this.drawGrid()\n            }\n\n            this.updateTbState(STATE.IDLE, false)\n\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n            // @ts-ignore\n            this.dispatchEvent(_endEvent)\n\n            break\n        }\n      }\n    }\n  }\n\n  private onSinglePanStart = (event: PointerEvent, operation: Operation): void => {\n    if (this.enabled && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.setCenter(event.clientX, event.clientY)\n\n      switch (operation) {\n        case 'PAN':\n          if (!this.enablePan) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n\n            this.activateGizmos(false)\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n          }\n\n          if (this.camera) {\n            this.updateTbState(STATE.PAN, true)\n            const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            if (rayDir !== undefined) {\n              this._startCursorPosition.copy(rayDir)\n            }\n            if (this.enableGrid) {\n              this.drawGrid()\n              // @ts-ignore\n              this.dispatchEvent(_changeEvent)\n            }\n          }\n\n          break\n\n        case 'ROTATE':\n          if (!this.enableRotate) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n          }\n\n          if (this.camera) {\n            this.updateTbState(STATE.ROTATE, true)\n            const rayDir = this.unprojectOnTbSurface(this.camera, _center.x, _center.y, this.domElement, this._tbRadius)\n            if (rayDir !== undefined) {\n              this._startCursorPosition.copy(rayDir)\n            }\n            this.activateGizmos(true)\n            if (this.enableAnimations) {\n              this._timePrev = this._timeCurrent = performance.now()\n              this._angleCurrent = this._anglePrev = 0\n              this._cursorPosPrev.copy(this._startCursorPosition)\n              this._cursorPosCurr.copy(this._cursorPosPrev)\n              this._wCurr = 0\n              this._wPrev = this._wCurr\n            }\n          }\n\n          // @ts-ignore\n          this.dispatchEvent(_changeEvent)\n          break\n\n        case 'FOV':\n          if (!this.enableZoom) return\n\n          if (this.camera instanceof PerspectiveCamera) {\n            if (this._animationId != -1) {\n              cancelAnimationFrame(this._animationId)\n              this._animationId = -1\n              this._timeStart = -1\n\n              this.activateGizmos(false)\n              // @ts-ignore\n              this.dispatchEvent(_changeEvent)\n            }\n\n            this.updateTbState(STATE.FOV, true)\n            this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n            this._currentCursorPosition.copy(this._startCursorPosition)\n          }\n          break\n\n        case 'ZOOM':\n          if (!this.enableZoom) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n\n            this.activateGizmos(false)\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n          }\n\n          this.updateTbState(STATE.SCALE, true)\n          this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n          this._currentCursorPosition.copy(this._startCursorPosition)\n          break\n      }\n    }\n  }\n\n  private onSinglePanMove = (event: PointerEvent, opState: Symbol): void => {\n    if (this.enabled && this.domElement) {\n      const restart = opState != this._state\n      this.setCenter(event.clientX, event.clientY)\n\n      switch (opState) {\n        case STATE.PAN:\n          if (this.enablePan && this.camera) {\n            if (restart) {\n              //switch to pan operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n              if (rayDir !== undefined) {\n                this._startCursorPosition.copy(rayDir)\n              }\n              if (this.enableGrid) {\n                this.drawGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with pan operation\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n              if (rayDir !== undefined) {\n                this._currentCursorPosition.copy(rayDir)\n              }\n              this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition))\n            }\n          }\n\n          break\n\n        case STATE.ROTATE:\n          if (this.enableRotate && this.camera) {\n            if (restart) {\n              //switch to rotate operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              const rayDir = this.unprojectOnTbSurface(\n                this.camera,\n                _center.x,\n                _center.y,\n                this.domElement,\n                this._tbRadius,\n              )\n              if (rayDir !== undefined) {\n                this._startCursorPosition.copy(rayDir)\n              }\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(true)\n            } else {\n              //continue with rotate operation\n              const rayDir = this.unprojectOnTbSurface(\n                this.camera,\n                _center.x,\n                _center.y,\n                this.domElement,\n                this._tbRadius,\n              )\n              if (rayDir !== undefined) {\n                this._currentCursorPosition.copy(rayDir)\n              }\n\n              const distance = this._startCursorPosition.distanceTo(this._currentCursorPosition)\n              const angle = this._startCursorPosition.angleTo(this._currentCursorPosition)\n              const amount = Math.max(distance / this._tbRadius, angle) //effective rotation angle\n\n              this.applyTransformMatrix(\n                this.rotate(this.calculateRotationAxis(this._startCursorPosition, this._currentCursorPosition), amount),\n              )\n\n              if (this.enableAnimations) {\n                this._timePrev = this._timeCurrent\n                this._timeCurrent = performance.now()\n                this._anglePrev = this._angleCurrent\n                this._angleCurrent = amount\n                this._cursorPosPrev.copy(this._cursorPosCurr)\n                this._cursorPosCurr.copy(this._currentCursorPosition)\n                this._wPrev = this._wCurr\n                this._wCurr = this.calculateAngularSpeed(\n                  this._anglePrev,\n                  this._angleCurrent,\n                  this._timePrev,\n                  this._timeCurrent,\n                )\n              }\n            }\n          }\n\n          break\n\n        case STATE.SCALE:\n          if (this.enableZoom) {\n            if (restart) {\n              //switch to zoom operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n              this._currentCursorPosition.copy(this._startCursorPosition)\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with zoom operation\n              const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n              this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n              const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n              let size = 1\n\n              if (movement < 0) {\n                size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n              } else if (movement > 0) {\n                size = Math.pow(this.scaleFactor, movement * screenNotches)\n              }\n\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n            }\n          }\n\n          break\n\n        case STATE.FOV:\n          if (this.enableZoom && this.camera instanceof PerspectiveCamera) {\n            if (restart) {\n              //switch to fov operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n              this._currentCursorPosition.copy(this._startCursorPosition)\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with fov operation\n              const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n              this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n              const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n              let size = 1\n\n              if (movement < 0) {\n                size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n              } else if (movement > 0) {\n                size = Math.pow(this.scaleFactor, movement * screenNotches)\n              }\n\n              this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n              const x = this._v3_1.distanceTo(this._gizmos.position)\n              let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n              //check min and max distance\n              xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n              const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5)\n\n              //calculate new fov\n              let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n              //check min and max fov\n              newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov)\n\n              const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n              size = x / newDistance\n              this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n              this.setFov(newFov)\n              this.applyTransformMatrix(this.applyScale(size, this._v3_2, false))\n\n              //adjusting distance\n              const direction = this._gizmos.position\n                .clone()\n                .sub(this.camera.position)\n                .normalize()\n                .multiplyScalar(newDistance / x)\n              this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n            }\n          }\n\n          break\n      }\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onSinglePanEnd = (): void => {\n    if (this._state == STATE.ROTATE) {\n      if (!this.enableRotate) {\n        return\n      }\n\n      if (this.enableAnimations) {\n        //perform rotation animation\n        const deltaTime = performance.now() - this._timeCurrent\n        if (deltaTime < 120) {\n          const w = Math.abs((this._wPrev + this._wCurr) / 2)\n\n          const self = this\n          this._animationId = window.requestAnimationFrame(function (t) {\n            self.updateTbState(STATE.ANIMATION_ROTATE, true)\n            const rotationAxis = self.calculateRotationAxis(self._cursorPosPrev, self._cursorPosCurr)\n\n            self.onRotationAnim(t, rotationAxis, Math.min(w, self.wMax))\n          })\n        } else {\n          //cursor has been standing still for over 120 ms since last movement\n          this.updateTbState(STATE.IDLE, false)\n          this.activateGizmos(false)\n          // @ts-ignore\n          this.dispatchEvent(_changeEvent)\n        }\n      } else {\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    } else if (this._state == STATE.PAN || this._state == STATE.IDLE) {\n      this.updateTbState(STATE.IDLE, false)\n\n      if (this.enableGrid) {\n        this.disposeGrid()\n      }\n\n      this.activateGizmos(false)\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onDoubleTap = (event: PointerEvent): void => {\n    if (this.enabled && this.enablePan && this.scene && this.camera && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.setCenter(event.clientX, event.clientY)\n      const hitP = this.unprojectOnObj(this.getCursorNDC(_center.x, _center.y, this.domElement), this.camera)\n\n      if (hitP && this.enableAnimations) {\n        const self = this\n        if (this._animationId != -1) {\n          window.cancelAnimationFrame(this._animationId)\n        }\n\n        this._timeStart = -1\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.updateTbState(STATE.ANIMATION_FOCUS, true)\n          self.onFocusAnim(t, hitP, self._cameraMatrixState, self._gizmoMatrixState)\n        })\n      } else if (hitP && !this.enableAnimations) {\n        this.updateTbState(STATE.FOCUS, true)\n        this.focus(hitP, this.scaleFactor)\n        this.updateTbState(STATE.IDLE, false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onDoublePanStart = (): void => {\n    if (this.enabled && this.enablePan && this.camera && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.PAN, true)\n\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n\n      const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true)\n      if (rayDir !== undefined) {\n        this._startCursorPosition.copy(rayDir)\n      }\n      this._currentCursorPosition.copy(this._startCursorPosition)\n\n      this.activateGizmos(false)\n    }\n  }\n\n  private onDoublePanMove = (): void => {\n    if (this.enabled && this.enablePan && this.camera && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n\n      if (this._state != STATE.PAN) {\n        this.updateTbState(STATE.PAN, true)\n        this._startCursorPosition.copy(this._currentCursorPosition)\n      }\n\n      const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true)\n      if (rayDir !== undefined) this._currentCursorPosition.copy(rayDir)\n      this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition, true))\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onDoublePanEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onRotateStart = (): void => {\n    if (this.enabled && this.enableRotate) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.ZROTATE, true)\n\n      //this._startFingerRotation = event.rotation;\n\n      this._startFingerRotation =\n        this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) +\n        this.getAngle(this._touchStart[1], this._touchStart[0])\n      this._currentFingerRotation = this._startFingerRotation\n\n      this.camera?.getWorldDirection(this._rotationAxis) //rotation axis\n\n      if (!this.enablePan && !this.enableZoom) {\n        this.activateGizmos(true)\n      }\n    }\n  }\n\n  private onRotateMove = (): void => {\n    if (this.enabled && this.enableRotate && this.camera && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n      let rotationPoint\n\n      if (this._state != STATE.ZROTATE) {\n        this.updateTbState(STATE.ZROTATE, true)\n        this._startFingerRotation = this._currentFingerRotation\n      }\n\n      //this._currentFingerRotation = event.rotation;\n      this._currentFingerRotation =\n        this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) +\n        this.getAngle(this._touchStart[1], this._touchStart[0])\n\n      if (!this.enablePan) {\n        rotationPoint = new Vector3().setFromMatrixPosition(this._gizmoMatrixState)\n      } else if (this.camera) {\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n        rotationPoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n          ?.applyQuaternion(this.camera.quaternion)\n          .multiplyScalar(1 / this.camera.zoom)\n          .add(this._v3_2)\n      }\n\n      const amount = MathUtils.DEG2RAD * (this._startFingerRotation - this._currentFingerRotation)\n\n      if (rotationPoint !== undefined) {\n        this.applyTransformMatrix(this.zRotate(rotationPoint, amount))\n      }\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onRotateEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    this.activateGizmos(false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onPinchStart = (): void => {\n    if (this.enabled && this.enableZoom) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n      this.updateTbState(STATE.SCALE, true)\n\n      this._startFingerDistance = this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1])\n      this._currentFingerDistance = this._startFingerDistance\n\n      this.activateGizmos(false)\n    }\n  }\n\n  private onPinchMove = (): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n      const minDistance = 12 //minimum distance between fingers (in css pixels)\n\n      if (this._state != STATE.SCALE) {\n        this._startFingerDistance = this._currentFingerDistance\n        this.updateTbState(STATE.SCALE, true)\n      }\n\n      this._currentFingerDistance = Math.max(\n        this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1]),\n        minDistance * this._devPxRatio,\n      )\n      const amount = this._currentFingerDistance / this._startFingerDistance\n\n      let scalePoint\n\n      if (!this.enablePan) {\n        scalePoint = this._gizmos.position\n      } else {\n        if (this.camera instanceof OrthographicCamera) {\n          scalePoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            ?.applyQuaternion(this.camera.quaternion)\n            .multiplyScalar(1 / this.camera.zoom)\n            .add(this._gizmos.position)\n        } else if (this.camera instanceof PerspectiveCamera) {\n          scalePoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            ?.applyQuaternion(this.camera.quaternion)\n            .add(this._gizmos.position)\n        }\n      }\n\n      if (scalePoint !== undefined) {\n        this.applyTransformMatrix(this.applyScale(amount, scalePoint))\n      }\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onPinchEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onTriplePanStart = (): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.SCALE, true)\n\n      //const center = event.center;\n      let clientX = 0\n      let clientY = 0\n      const nFingers = this._touchCurrent.length\n\n      for (let i = 0; i < nFingers; i++) {\n        clientX += this._touchCurrent[i].clientX\n        clientY += this._touchCurrent[i].clientY\n      }\n\n      this.setCenter(clientX / nFingers, clientY / nFingers)\n\n      this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n      this._currentCursorPosition.copy(this._startCursorPosition)\n    }\n  }\n\n  private onTriplePanMove = (): void => {\n    if (this.enabled && this.enableZoom && this.camera && this.domElement) {\n      //\t  fov / 2\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\tx\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t\t| _ _ _\\\n      //\t\t\ty\n\n      //const center = event.center;\n      let clientX = 0\n      let clientY = 0\n      const nFingers = this._touchCurrent.length\n\n      for (let i = 0; i < nFingers; i++) {\n        clientX += this._touchCurrent[i].clientX\n        clientY += this._touchCurrent[i].clientY\n      }\n\n      this.setCenter(clientX / nFingers, clientY / nFingers)\n\n      const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n      this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n      const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n      let size = 1\n\n      if (movement < 0) {\n        size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n      } else if (movement > 0) {\n        size = Math.pow(this.scaleFactor, movement * screenNotches)\n      }\n\n      this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n      const x = this._v3_1.distanceTo(this._gizmos.position)\n      let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n      //check min and max distance\n      xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n      const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5)\n\n      //calculate new fov\n      let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n      //check min and max fov\n      newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov)\n\n      const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n      size = x / newDistance\n      this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n      this.setFov(newFov)\n      this.applyTransformMatrix(this.applyScale(size, this._v3_2, false))\n\n      //adjusting distance\n      const direction = this._gizmos.position\n        .clone()\n        .sub(this.camera.position)\n        .normalize()\n        .multiplyScalar(newDistance / x)\n      this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onTriplePanEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n    //this.dispatchEvent( _changeEvent );\n  }\n\n  /**\n   * Set _center's x/y coordinates\n   * @param {Number} clientX\n   * @param {Number} clientY\n   */\n  private setCenter = (clientX: number, clientY: number): void => {\n    _center.x = clientX\n    _center.y = clientY\n  }\n\n  /**\n   * Set default mouse actions\n   */\n  private initializeMouseActions = (): void => {\n    this.setMouseAction('PAN', 0, 'CTRL')\n    this.setMouseAction('PAN', 2)\n\n    this.setMouseAction('ROTATE', 0)\n\n    this.setMouseAction('ZOOM', 'WHEEL')\n    this.setMouseAction('ZOOM', 1)\n\n    this.setMouseAction('FOV', 'WHEEL', 'SHIFT')\n    this.setMouseAction('FOV', 1, 'SHIFT')\n  }\n\n  /**\n   * Set a new mouse action by specifying the operation to be performed and a mouse/key combination. In case of conflict, replaces the existing one\n   * @param {String} operation The operation to be performed ('PAN', 'ROTATE', 'ZOOM', 'FOV)\n   * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n   * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n   * @returns {Boolean} True if the mouse action has been successfully added, false otherwise\n   */\n  private setMouseAction = (operation: Operation, mouse: MouseButtonType, key: ModifierKey | null = null): boolean => {\n    const operationInput = ['PAN', 'ROTATE', 'ZOOM', 'FOV']\n    const mouseInput = [0, 1, 2, 'WHEEL']\n    const keyInput = ['CTRL', 'SHIFT', null]\n    let state\n\n    if (!operationInput.includes(operation) || !mouseInput.includes(mouse) || !keyInput.includes(key)) {\n      //invalid parameters\n      return false\n    }\n\n    if (mouse == 'WHEEL') {\n      if (operation != 'ZOOM' && operation != 'FOV') {\n        //cannot associate 2D operation to 1D input\n        return false\n      }\n    }\n\n    switch (operation) {\n      case 'PAN':\n        state = STATE.PAN\n        break\n\n      case 'ROTATE':\n        state = STATE.ROTATE\n        break\n\n      case 'ZOOM':\n        state = STATE.SCALE\n        break\n\n      case 'FOV':\n        state = STATE.FOV\n        break\n    }\n\n    const action = {\n      operation: operation,\n      mouse: mouse,\n      key: key,\n      state: state,\n    }\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      if (this.mouseActions[i].mouse == action.mouse && this.mouseActions[i].key == action.key) {\n        this.mouseActions.splice(i, 1, action)\n        return true\n      }\n    }\n\n    this.mouseActions.push(action)\n    return true\n  }\n\n  /**\n   * Return the operation associated to a mouse/keyboard combination\n   * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n   * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n   * @returns The operation if it has been found, null otherwise\n   */\n  private getOpFromAction = (mouse: MouseButtonType, key: ModifierKey | null): Operation | null => {\n    let action\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      action = this.mouseActions[i]\n      if (action.mouse == mouse && action.key == key) {\n        return action.operation\n      }\n    }\n\n    if (key) {\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i]\n        if (action.mouse == mouse && action.key == null) {\n          return action.operation\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Get the operation associated to mouse and key combination and returns the corresponding FSA state\n   * @param {Number} mouse Mouse button\n   * @param {String} key Keyboard modifier\n   * @returns The FSA state obtained from the operation associated to mouse/keyboard combination\n   */\n  private getOpStateFromAction = (mouse: MouseButtonType, key: ModifierKey | null): Symbol | null => {\n    let action\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      action = this.mouseActions[i]\n      if (action.mouse == mouse && action.key == key) {\n        return action.state\n      }\n    }\n\n    if (key) {\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i]\n        if (action.mouse == mouse && action.key == null) {\n          return action.state\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Calculate the angle between two pointers\n   * @param {PointerEvent} p1\n   * @param {PointerEvent} p2\n   * @returns {Number} The angle between two pointers in degrees\n   */\n  private getAngle = (p1: PointerEvent, p2: PointerEvent): number => {\n    return (Math.atan2(p2.clientY - p1.clientY, p2.clientX - p1.clientX) * 180) / Math.PI\n  }\n\n  /**\n   * Update a PointerEvent inside current pointerevents array\n   * @param {PointerEvent} event\n   */\n  private updateTouchEvent = (event: PointerEvent): void => {\n    for (let i = 0; i < this._touchCurrent.length; i++) {\n      if (this._touchCurrent[i].pointerId == event.pointerId) {\n        this._touchCurrent.splice(i, 1, event)\n        break\n      }\n    }\n  }\n\n  /**\n   * Apply a transformation matrix, to the camera and gizmos\n   * @param {Object} transformation Object containing matrices to apply to camera and gizmos\n   */\n  private applyTransformMatrix(transformation: Transformation | undefined): void {\n    if (transformation?.camera && this.camera) {\n      this._m4_1.copy(this._cameraMatrixState).premultiply(transformation.camera)\n      this._m4_1.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n      this.camera.updateMatrix()\n\n      //update camera up vector\n      if (this._state == STATE.ROTATE || this._state == STATE.ZROTATE || this._state == STATE.ANIMATION_ROTATE) {\n        this.camera.up.copy(this._upState).applyQuaternion(this.camera.quaternion)\n      }\n    }\n\n    if (transformation?.gizmos) {\n      this._m4_1.copy(this._gizmoMatrixState).premultiply(transformation.gizmos)\n      this._m4_1.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n      this._gizmos.updateMatrix()\n    }\n\n    if (\n      (this._state == STATE.SCALE || this._state == STATE.FOCUS || this._state == STATE.ANIMATION_FOCUS) &&\n      this.camera\n    ) {\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n\n      if (this.adjustNearFar) {\n        const cameraDistance = this.camera.position.distanceTo(this._gizmos.position)\n\n        const bb = new Box3()\n        bb.setFromObject(this._gizmos)\n        const sphere = new Sphere()\n        bb.getBoundingSphere(sphere)\n\n        const adjustedNearPosition = Math.max(this._nearPos0, sphere.radius + sphere.center.length())\n        const regularNearPosition = cameraDistance - this._initialNear\n\n        const minNearPos = Math.min(adjustedNearPosition, regularNearPosition)\n        this.camera.near = cameraDistance - minNearPos\n\n        const adjustedFarPosition = Math.min(this._farPos0, -sphere.radius + sphere.center.length())\n        const regularFarPosition = cameraDistance - this._initialFar\n\n        const minFarPos = Math.min(adjustedFarPosition, regularFarPosition)\n        this.camera.far = cameraDistance - minFarPos\n\n        this.camera.updateProjectionMatrix()\n      } else {\n        let update = false\n\n        if (this.camera.near != this._initialNear) {\n          this.camera.near = this._initialNear\n          update = true\n        }\n\n        if (this.camera.far != this._initialFar) {\n          this.camera.far = this._initialFar\n          update = true\n        }\n\n        if (update) {\n          this.camera.updateProjectionMatrix()\n        }\n      }\n    }\n  }\n\n  /**\n   * Calculate the angular speed\n   * @param {Number} p0 Position at t0\n   * @param {Number} p1 Position at t1\n   * @param {Number} t0 Initial time in milliseconds\n   * @param {Number} t1 Ending time in milliseconds\n   */\n  private calculateAngularSpeed = (p0: number, p1: number, t0: number, t1: number): number => {\n    const s = p1 - p0\n    const t = (t1 - t0) / 1000\n    if (t == 0) {\n      return 0\n    }\n\n    return s / t\n  }\n\n  /**\n   * Calculate the distance between two pointers\n   * @param {PointerEvent} p0 The first pointer\n   * @param {PointerEvent} p1 The second pointer\n   * @returns {number} The distance between the two pointers\n   */\n  private calculatePointersDistance = (p0: PointerEvent, p1: PointerEvent): number => {\n    return Math.sqrt(Math.pow(p1.clientX - p0.clientX, 2) + Math.pow(p1.clientY - p0.clientY, 2))\n  }\n\n  /**\n   * Calculate the rotation axis as the vector perpendicular between two vectors\n   * @param {Vector3} vec1 The first vector\n   * @param {Vector3} vec2 The second vector\n   * @returns {Vector3} The normalized rotation axis\n   */\n  private calculateRotationAxis = (vec1: Vector3, vec2: Vector3): Vector3 => {\n    this._rotationMatrix.extractRotation(this._cameraMatrixState)\n    this._quat.setFromRotationMatrix(this._rotationMatrix)\n\n    this._rotationAxis.crossVectors(vec1, vec2).applyQuaternion(this._quat)\n    return this._rotationAxis.normalize().clone()\n  }\n\n  /**\n   * Calculate the trackball radius so that gizmo's diamater will be 2/3 of the minimum side of the camera frustum\n   * @param {Camera} camera\n   * @returns {Number} The trackball radius\n   */\n  private calculateTbRadius = (camera: Camera): number | undefined => {\n    const factor = 0.67\n    const distance = camera.position.distanceTo(this._gizmos.position)\n\n    if (camera instanceof PerspectiveCamera) {\n      const halfFovV = MathUtils.DEG2RAD * camera.fov * 0.5 //vertical fov/2 in radians\n      const halfFovH = Math.atan(camera.aspect * Math.tan(halfFovV)) //horizontal fov/2 in radians\n      return Math.tan(Math.min(halfFovV, halfFovH)) * distance * factor\n    } else if (camera instanceof OrthographicCamera) {\n      return Math.min(camera.top, camera.right) * factor\n    }\n  }\n\n  /**\n   * Focus operation consist of positioning the point of interest in front of the camera and a slightly zoom in\n   * @param {Vector3} point The point of interest\n   * @param {Number} size Scale factor\n   * @param {Number} amount Amount of operation to be completed (used for focus animations, default is complete full operation)\n   */\n  private focus = (point: Vector3, size: number, amount = 1): void => {\n    if (this.camera) {\n      const focusPoint = point.clone()\n\n      //move center of camera (along with gizmos) towards point of interest\n      focusPoint.sub(this._gizmos.position).multiplyScalar(amount)\n      this._translationMatrix.makeTranslation(focusPoint.x, focusPoint.y, focusPoint.z)\n\n      const gizmoStateTemp = this._gizmoMatrixState.clone()\n      this._gizmoMatrixState.premultiply(this._translationMatrix)\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n      const cameraStateTemp = this._cameraMatrixState.clone()\n      this._cameraMatrixState.premultiply(this._translationMatrix)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n\n      //apply zoom\n      if (this.enableZoom) {\n        this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n      }\n\n      this._gizmoMatrixState.copy(gizmoStateTemp)\n      this._cameraMatrixState.copy(cameraStateTemp)\n    }\n  }\n\n  /**\n   * Draw a grid and add it to the scene\n   */\n  private drawGrid = (): void => {\n    if (this.scene) {\n      const color = 0x888888\n      const multiplier = 3\n      let size, divisions, maxLength, tick\n\n      if (this.camera instanceof OrthographicCamera) {\n        const width = this.camera.right - this.camera.left\n        const height = this.camera.bottom - this.camera.top\n\n        maxLength = Math.max(width, height)\n        tick = maxLength / 20\n\n        size = (maxLength / this.camera.zoom) * multiplier\n        divisions = (size / tick) * this.camera.zoom\n      } else if (this.camera instanceof PerspectiveCamera) {\n        const distance = this.camera.position.distanceTo(this._gizmos.position)\n        const halfFovV = MathUtils.DEG2RAD * this.camera.fov * 0.5\n        const halfFovH = Math.atan(this.camera.aspect * Math.tan(halfFovV))\n\n        maxLength = Math.tan(Math.max(halfFovV, halfFovH)) * distance * 2\n        tick = maxLength / 20\n\n        size = maxLength * multiplier\n        divisions = size / tick\n      }\n\n      if (this._grid == null && this.camera) {\n        this._grid = new GridHelper(size, divisions, color, color)\n        this._grid.position.copy(this._gizmos.position)\n        this._gridPosition.copy(this._grid.position)\n        this._grid.quaternion.copy(this.camera.quaternion)\n        this._grid.rotateX(Math.PI * 0.5)\n\n        this.scene.add(this._grid)\n      }\n    }\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    // https://github.com/mrdoob/three.js/issues/20575\n\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.ArcballControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n    // disables touch scroll\n    // touch-action needs to be defined for pointer events to work on mobile\n    // https://stackoverflow.com/a/48254578\n    this.domElement.style.touchAction = 'none'\n    this.domElement.addEventListener('contextmenu', this.onContextMenu)\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('pointercancel', this.onPointerCancel)\n    this.domElement.addEventListener('wheel', this.onWheel)\n  }\n\n  /**\n   * Remove all listeners, stop animations and clean scene\n   */\n  public dispose = (): void => {\n    if (this._animationId != -1) {\n      window.cancelAnimationFrame(this._animationId)\n    }\n\n    this.domElement?.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement?.removeEventListener('pointercancel', this.onPointerCancel)\n    this.domElement?.removeEventListener('wheel', this.onWheel)\n    this.domElement?.removeEventListener('contextmenu', this.onContextMenu)\n\n    window.removeEventListener('pointermove', this.onPointerMove)\n    window.removeEventListener('pointerup', this.onPointerUp)\n\n    window.removeEventListener('resize', this.onWindowResize)\n\n    this.scene?.remove(this._gizmos)\n    this.disposeGrid()\n  }\n\n  /**\n   * remove the grid from the scene\n   */\n  private disposeGrid = (): void => {\n    if (this._grid && this.scene) {\n      this.scene.remove(this._grid)\n      this._grid = null\n    }\n  }\n\n  /**\n   * Compute the easing out cubic function for ease out effect in animation\n   * @param {Number} t The absolute progress of the animation in the bound of 0 (beginning of the) and 1 (ending of animation)\n   * @returns {Number} Result of easing out cubic at time t\n   */\n  private easeOutCubic = (t: number): number => {\n    return 1 - Math.pow(1 - t, 3)\n  }\n\n  /**\n   * Make rotation gizmos more or less visible\n   * @param {Boolean} isActive If true, make gizmos more visible\n   */\n  private activateGizmos = (isActive: boolean): void => {\n    for (const gizmo of this._gizmos.children) {\n      ;(gizmo as Mesh<BufferGeometry, Material>).material.setValues({ opacity: isActive ? 1 : 0.6 })\n    }\n  }\n\n  /**\n   * Calculate the cursor position in NDC\n   * @param {number} x Cursor horizontal coordinate within the canvas\n   * @param {number} y Cursor vertical coordinate within the canvas\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @returns {Vector2} Cursor normalized position inside the canvas\n   */\n  private getCursorNDC = (cursorX: number, cursorY: number, canvas: HTMLElement): Vector2 => {\n    const canvasRect = canvas.getBoundingClientRect()\n    this._v2_1.setX(((cursorX - canvasRect.left) / canvasRect.width) * 2 - 1)\n    this._v2_1.setY(((canvasRect.bottom - cursorY) / canvasRect.height) * 2 - 1)\n    return this._v2_1.clone()\n  }\n\n  /**\n   * Calculate the cursor position inside the canvas x/y coordinates with the origin being in the center of the canvas\n   * @param {Number} x Cursor horizontal coordinate within the canvas\n   * @param {Number} y Cursor vertical coordinate within the canvas\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @returns {Vector2} Cursor position inside the canvas\n   */\n  private getCursorPosition = (cursorX: number, cursorY: number, canvas: HTMLElement): Vector2 => {\n    this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n    if (this.camera instanceof OrthographicCamera) {\n      this._v2_1.x *= (this.camera.right - this.camera.left) * 0.5\n      this._v2_1.y *= (this.camera.top - this.camera.bottom) * 0.5\n    }\n    return this._v2_1.clone()\n  }\n\n  /**\n   * Set the camera to be controlled\n   * @param {Camera} camera The virtual camera to be controlled\n   */\n  private setCamera = (camera: Camera | null): void => {\n    if (camera) {\n      camera.lookAt(this.target)\n      camera.updateMatrix()\n\n      //setting state\n      if (camera instanceof PerspectiveCamera) {\n        this._fov0 = camera.fov\n        this._fovState = camera.fov\n      }\n\n      this._cameraMatrixState0.copy(camera.matrix)\n      this._cameraMatrixState.copy(this._cameraMatrixState0)\n      this._cameraProjectionState.copy(camera.projectionMatrix)\n      this._zoom0 = camera.zoom\n      this._zoomState = this._zoom0\n\n      this._initialNear = camera.near\n      this._nearPos0 = camera.position.distanceTo(this.target) - camera.near\n      this._nearPos = this._initialNear\n\n      this._initialFar = camera.far\n      this._farPos0 = camera.position.distanceTo(this.target) - camera.far\n      this._farPos = this._initialFar\n\n      this._up0.copy(camera.up)\n      this._upState.copy(camera.up)\n\n      this.camera = camera\n\n      this.camera.updateProjectionMatrix()\n\n      //making gizmos\n      const tbRadius = this.calculateTbRadius(camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n    }\n  }\n\n  /**\n   * Set gizmos visibility\n   * @param {Boolean} value Value of gizmos visibility\n   */\n  public setGizmosVisible(value: boolean): void {\n    this._gizmos.visible = value\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  /**\n   * Creates the rotation gizmos matching trackball center and radius\n   * @param {Vector3} tbCenter The trackball center\n   * @param {number} tbRadius The trackball radius\n   */\n  private makeGizmos = (tbCenter: Vector3, tbRadius: number): void => {\n    // @ts-ignore\n    const curve = new EllipseCurve(0, 0, tbRadius, tbRadius)\n    const points = curve.getPoints(this._curvePts)\n\n    //geometry\n    const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n    //material\n    const curveMaterialX = new LineBasicMaterial({ color: 0xff8080, fog: false, transparent: true, opacity: 0.6 })\n    const curveMaterialY = new LineBasicMaterial({ color: 0x80ff80, fog: false, transparent: true, opacity: 0.6 })\n    const curveMaterialZ = new LineBasicMaterial({ color: 0x8080ff, fog: false, transparent: true, opacity: 0.6 })\n\n    //line\n    const gizmoX = new Line(curveGeometry, curveMaterialX)\n    const gizmoY = new Line(curveGeometry, curveMaterialY)\n    const gizmoZ = new Line(curveGeometry, curveMaterialZ)\n\n    const rotation = Math.PI * 0.5\n    gizmoX.rotation.x = rotation\n    gizmoY.rotation.y = rotation\n\n    //setting state\n    this._gizmoMatrixState0.identity().setPosition(tbCenter)\n    this._gizmoMatrixState.copy(this._gizmoMatrixState0)\n\n    if (this.camera && this.camera.zoom != 1) {\n      //adapt gizmos size to camera zoom\n      const size = 1 / this.camera.zoom\n      this._scaleMatrix.makeScale(size, size, size)\n      this._translationMatrix.makeTranslation(-tbCenter.x, -tbCenter.y, -tbCenter.z)\n\n      this._gizmoMatrixState.premultiply(this._translationMatrix).premultiply(this._scaleMatrix)\n      this._translationMatrix.makeTranslation(tbCenter.x, tbCenter.y, tbCenter.z)\n      this._gizmoMatrixState.premultiply(this._translationMatrix)\n    }\n\n    this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n    this._gizmos.clear()\n\n    this._gizmos.add(gizmoX)\n    this._gizmos.add(gizmoY)\n    this._gizmos.add(gizmoZ)\n  }\n\n  /**\n   * Perform animation for focus operation\n   * @param {Number} time Instant in which this function is called as performance.now()\n   * @param {Vector3} point Point of interest for focus operation\n   * @param {Matrix4} cameraMatrix Camera matrix\n   * @param {Matrix4} gizmoMatrix Gizmos matrix\n   */\n  private onFocusAnim = (time: number, point: Vector3, cameraMatrix: Matrix4, gizmoMatrix: Matrix4): void => {\n    if (this._timeStart == -1) {\n      //animation start\n      this._timeStart = time\n    }\n\n    if (this._state == STATE.ANIMATION_FOCUS) {\n      const deltaTime = time - this._timeStart\n      const animTime = deltaTime / this.focusAnimationTime\n\n      this._gizmoMatrixState.copy(gizmoMatrix)\n\n      if (animTime >= 1) {\n        //animation end\n\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n        this.focus(point, this.scaleFactor)\n\n        this._timeStart = -1\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      } else {\n        const amount = this.easeOutCubic(animTime)\n        const size = 1 - amount + this.scaleFactor * amount\n\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n        this.focus(point, size, amount)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n        const self = this\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.onFocusAnim(t, point, cameraMatrix, gizmoMatrix.clone())\n        })\n      }\n    } else {\n      //interrupt animation\n\n      this._animationId = -1\n      this._timeStart = -1\n    }\n  }\n\n  /**\n   * Perform animation for rotation operation\n   * @param {Number} time Instant in which this function is called as performance.now()\n   * @param {Vector3} rotationAxis Rotation axis\n   * @param {number} w0 Initial angular velocity\n   */\n  private onRotationAnim = (time: number, rotationAxis: Vector3, w0: number): void => {\n    if (this._timeStart == -1) {\n      //animation start\n      this._anglePrev = 0\n      this._angleCurrent = 0\n      this._timeStart = time\n    }\n\n    if (this._state == STATE.ANIMATION_ROTATE) {\n      //w = w0 + alpha * t\n      const deltaTime = (time - this._timeStart) / 1000\n      const w = w0 + -this.dampingFactor * deltaTime\n\n      if (w > 0) {\n        //tetha = 0.5 * alpha * t^2 + w0 * t + tetha0\n        this._angleCurrent = 0.5 * -this.dampingFactor * Math.pow(deltaTime, 2) + w0 * deltaTime + 0\n        this.applyTransformMatrix(this.rotate(rotationAxis, this._angleCurrent))\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n        const self = this\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.onRotationAnim(t, rotationAxis, w0)\n        })\n      } else {\n        this._animationId = -1\n        this._timeStart = -1\n\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    } else {\n      //interrupt animation\n\n      this._animationId = -1\n      this._timeStart = -1\n\n      if (this._state != STATE.ROTATE) {\n        this.activateGizmos(false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    }\n  }\n\n  /**\n   * Perform pan operation moving camera between two points\n   * @param {Vector3} p0 Initial point\n   * @param {Vector3} p1 Ending point\n   * @param {Boolean} adjust If movement should be adjusted considering camera distance (Perspective only)\n   */\n  private pan = (p0: Vector3, p1: Vector3, adjust = false): Transformation => {\n    if (this.camera) {\n      const movement = p0.clone().sub(p1)\n\n      if (this.camera instanceof OrthographicCamera) {\n        //adjust movement amount\n        movement.multiplyScalar(1 / this.camera.zoom)\n      }\n\n      if (this.camera instanceof PerspectiveCamera && adjust) {\n        //adjust movement amount\n        this._v3_1.setFromMatrixPosition(this._cameraMatrixState0) //camera's initial position\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0) //gizmo's initial position\n        const distanceFactor =\n          this._v3_1.distanceTo(this._v3_2) / this.camera.position.distanceTo(this._gizmos.position)\n        movement.multiplyScalar(1 / distanceFactor)\n      }\n\n      this._v3_1.set(movement.x, movement.y, 0).applyQuaternion(this.camera.quaternion)\n\n      this._m4_1.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z)\n\n      this.setTransformationMatrices(this._m4_1, this._m4_1)\n    }\n    return _transformation\n  }\n\n  /**\n   * Reset trackball\n   */\n  public reset = (): void => {\n    if (this.camera) {\n      this.camera.zoom = this._zoom0\n\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = this._fov0\n      }\n\n      this.camera.near = this._nearPos\n      this.camera.far = this._farPos\n      this._cameraMatrixState.copy(this._cameraMatrixState0)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n      this.camera.up.copy(this._up0)\n\n      this.camera.updateMatrix()\n      this.camera.updateProjectionMatrix()\n\n      this._gizmoMatrixState.copy(this._gizmoMatrixState0)\n      this._gizmoMatrixState0.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n      this._gizmos.updateMatrix()\n\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this._gizmos.position, this._tbRadius)\n\n      this.camera.lookAt(this._gizmos.position)\n\n      this.updateTbState(STATE.IDLE, false)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  /**\n   * Rotate the camera around an axis passing by trackball's center\n   * @param {Vector3} axis Rotation axis\n   * @param {number} angle Angle in radians\n   * @returns {Object} Object with 'camera' field containing transformation matrix resulting from the operation to be applied to the camera\n   */\n  private rotate = (axis: Vector3, angle: number): Transformation => {\n    const point = this._gizmos.position //rotation center\n    this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z)\n    this._rotationMatrix.makeRotationAxis(axis, -angle)\n\n    //rotate camera\n    this._m4_1.makeTranslation(point.x, point.y, point.z)\n    this._m4_1.multiply(this._rotationMatrix)\n    this._m4_1.multiply(this._translationMatrix)\n\n    this.setTransformationMatrices(this._m4_1)\n\n    return _transformation\n  }\n\n  public copyState = (): void => {\n    if (this.camera) {\n      const state = JSON.stringify(\n        this.camera instanceof OrthographicCamera\n          ? {\n              arcballState: {\n                cameraFar: this.camera.far,\n                cameraMatrix: this.camera.matrix,\n                cameraNear: this.camera.near,\n                cameraUp: this.camera.up,\n                cameraZoom: this.camera.zoom,\n                gizmoMatrix: this._gizmos.matrix,\n              },\n            }\n          : {\n              arcballState: {\n                cameraFar: this.camera.far,\n                cameraFov: this.camera.fov,\n                cameraMatrix: this.camera.matrix,\n                cameraNear: this.camera.near,\n                cameraUp: this.camera.up,\n                cameraZoom: this.camera.zoom,\n                gizmoMatrix: this._gizmos.matrix,\n              },\n            },\n      )\n\n      navigator.clipboard.writeText(state)\n    }\n  }\n\n  public pasteState = (): void => {\n    const self = this\n    navigator.clipboard.readText().then(function resolved(value) {\n      self.setStateFromJSON(value)\n    })\n  }\n\n  /**\n   * Save the current state of the control. This can later be recovered with .reset\n   */\n  public saveState = (): void => {\n    if (!this.camera) return\n\n    this._cameraMatrixState0.copy(this.camera.matrix)\n    this._gizmoMatrixState0.copy(this._gizmos.matrix)\n    this._nearPos = this.camera.near\n    this._farPos = this.camera.far\n    this._zoom0 = this.camera.zoom\n    this._up0.copy(this.camera.up)\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._fov0 = this.camera.fov\n    }\n  }\n\n  /**\n   * Perform uniform scale operation around a given point\n   * @param {Number} size Scale factor\n   * @param {Vector3} point Point around which scale\n   * @param {Boolean} scaleGizmos If gizmos should be scaled (Perspective only)\n   * @returns {Object} Object with 'camera' and 'gizmo' fields containing transformation matrices resulting from the operation to be applied to the camera and gizmos\n   */\n  private applyScale = (size: number, point: Vector3, scaleGizmos = true): Transformation | undefined => {\n    if (!this.camera) return\n\n    const scalePoint = point.clone()\n    let sizeInverse = 1 / size\n\n    if (this.camera instanceof OrthographicCamera) {\n      //camera zoom\n      this.camera.zoom = this._zoomState\n      this.camera.zoom *= size\n\n      //check min and max zoom\n      if (this.camera.zoom > this.maxZoom) {\n        this.camera.zoom = this.maxZoom\n        sizeInverse = this._zoomState / this.maxZoom\n      } else if (this.camera.zoom < this.minZoom) {\n        this.camera.zoom = this.minZoom\n        sizeInverse = this._zoomState / this.minZoom\n      }\n\n      this.camera.updateProjectionMatrix()\n\n      this._v3_1.setFromMatrixPosition(this._gizmoMatrixState) //gizmos position\n\n      //scale gizmos so they appear in the same spot having the same dimension\n      this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse)\n      this._translationMatrix.makeTranslation(-this._v3_1.x, -this._v3_1.y, -this._v3_1.z)\n\n      this._m4_2.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z).multiply(this._scaleMatrix)\n      this._m4_2.multiply(this._translationMatrix)\n\n      //move camera and gizmos to obtain pinch effect\n      scalePoint.sub(this._v3_1)\n\n      const amount = scalePoint.clone().multiplyScalar(sizeInverse)\n      scalePoint.sub(amount)\n\n      this._m4_1.makeTranslation(scalePoint.x, scalePoint.y, scalePoint.z)\n      this._m4_2.premultiply(this._m4_1)\n\n      this.setTransformationMatrices(this._m4_1, this._m4_2)\n      return _transformation\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n      this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n      //move camera\n      let distance = this._v3_1.distanceTo(scalePoint)\n      let amount = distance - distance * sizeInverse\n\n      //check min and max distance\n      const newDistance = distance - amount\n      if (newDistance < this.minDistance) {\n        sizeInverse = this.minDistance / distance\n        amount = distance - distance * sizeInverse\n      } else if (newDistance > this.maxDistance) {\n        sizeInverse = this.maxDistance / distance\n        amount = distance - distance * sizeInverse\n      }\n\n      let direction = scalePoint.clone().sub(this._v3_1).normalize().multiplyScalar(amount)\n\n      this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n\n      if (scaleGizmos) {\n        //scale gizmos so they appear in the same spot having the same dimension\n        const pos = this._v3_2\n\n        distance = pos.distanceTo(scalePoint)\n        amount = distance - distance * sizeInverse\n        direction = scalePoint.clone().sub(this._v3_2).normalize().multiplyScalar(amount)\n\n        this._translationMatrix.makeTranslation(pos.x, pos.y, pos.z)\n        this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse)\n\n        this._m4_2.makeTranslation(direction.x, direction.y, direction.z).multiply(this._translationMatrix)\n        this._m4_2.multiply(this._scaleMatrix)\n\n        this._translationMatrix.makeTranslation(-pos.x, -pos.y, -pos.z)\n\n        this._m4_2.multiply(this._translationMatrix)\n        this.setTransformationMatrices(this._m4_1, this._m4_2)\n      } else {\n        this.setTransformationMatrices(this._m4_1)\n      }\n\n      return _transformation\n    }\n  }\n\n  /**\n   * Set camera fov\n   * @param {Number} value fov to be setted\n   */\n  private setFov = (value: number): void => {\n    if (this.camera instanceof PerspectiveCamera) {\n      this.camera.fov = MathUtils.clamp(value, this.minFov, this.maxFov)\n      this.camera.updateProjectionMatrix()\n    }\n  }\n\n  /**\n   * Set the trackball's center point\n   * @param {Number} x X coordinate\n   * @param {Number} y Y coordinate\n   * @param {Number} z Z coordinate\n   */\n  public setTarget = (x: number, y: number, z: number): void => {\n    if (this.camera) {\n      this.target.set(x, y, z)\n      this._gizmos.position.set(x, y, z) //for correct radius calculation\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n      this.camera.lookAt(this.target)\n    }\n  }\n\n  /**\n   * Set values in transformation object\n   * @param {Matrix4} camera Transformation to be applied to the camera\n   * @param {Matrix4} gizmos Transformation to be applied to gizmos\n   */\n  private setTransformationMatrices(camera: Matrix4 | null = null, gizmos: Matrix4 | null = null): void {\n    if (camera) {\n      if (_transformation.camera) {\n        _transformation.camera.copy(camera)\n      } else {\n        _transformation.camera = camera.clone()\n      }\n    } else {\n      _transformation.camera = null\n    }\n\n    if (gizmos) {\n      if (_transformation.gizmos) {\n        _transformation.gizmos.copy(gizmos)\n      } else {\n        _transformation.gizmos = gizmos.clone()\n      }\n    } else {\n      _transformation.gizmos = null\n    }\n  }\n\n  /**\n   * Rotate camera around its direction axis passing by a given point by a given angle\n   * @param {Vector3} point The point where the rotation axis is passing trough\n   * @param {Number} angle Angle in radians\n   * @returns The computed transormation matix\n   */\n  private zRotate = (point: Vector3, angle: number): Transformation => {\n    this._rotationMatrix.makeRotationAxis(this._rotationAxis, angle)\n    this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z)\n\n    this._m4_1.makeTranslation(point.x, point.y, point.z)\n    this._m4_1.multiply(this._rotationMatrix)\n    this._m4_1.multiply(this._translationMatrix)\n\n    this._v3_1.setFromMatrixPosition(this._gizmoMatrixState).sub(point) //vector from rotation center to gizmos position\n    this._v3_2.copy(this._v3_1).applyAxisAngle(this._rotationAxis, angle) //apply rotation\n    this._v3_2.sub(this._v3_1)\n\n    this._m4_2.makeTranslation(this._v3_2.x, this._v3_2.y, this._v3_2.z)\n\n    this.setTransformationMatrices(this._m4_1, this._m4_2)\n    return _transformation\n  }\n\n  /**\n   * Unproject the cursor on the 3D object surface\n   * @param {Vector2} cursor Cursor coordinates in NDC\n   * @param {Camera} camera Virtual camera\n   * @returns {Vector3} The point of intersection with the model, if exist, null otherwise\n   */\n  private unprojectOnObj = (cursor: Vector2, camera: Camera): Vector3 | null => {\n    if (!this.scene) return null\n\n    const raycaster = new Raycaster()\n    raycaster.near = camera.near\n    raycaster.far = camera.far\n    raycaster.setFromCamera(cursor, camera)\n\n    const intersect = raycaster.intersectObjects(this.scene.children, true)\n    for (let i = 0; i < intersect.length; i++) {\n      if (intersect[i].object.uuid != this._gizmos.uuid && intersect[i].face) {\n        return intersect[i].point.clone()\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Unproject the cursor on the trackball surface\n   * @param {Camera} camera The virtual camera\n   * @param {Number} cursorX Cursor horizontal coordinate on screen\n   * @param {Number} cursorY Cursor vertical coordinate on screen\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @param {number} tbRadius The trackball radius\n   * @returns {Vector3} The unprojected point on the trackball surface\n   */\n  private unprojectOnTbSurface = (\n    camera: Camera,\n    cursorX: number,\n    cursorY: number,\n    canvas: HTMLElement,\n    tbRadius: number,\n  ): Vector3 | undefined => {\n    if (camera instanceof OrthographicCamera) {\n      this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas))\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, 0)\n\n      const x2 = Math.pow(this._v2_1.x, 2)\n      const y2 = Math.pow(this._v2_1.y, 2)\n      const r2 = Math.pow(this._tbRadius, 2)\n\n      if (x2 + y2 <= r2 * 0.5) {\n        //intersection with sphere\n        this._v3_1.setZ(Math.sqrt(r2 - (x2 + y2)))\n      } else {\n        //intersection with hyperboloid\n        this._v3_1.setZ((r2 * 0.5) / Math.sqrt(x2 + y2))\n      }\n\n      return this._v3_1\n    }\n\n    if (camera instanceof PerspectiveCamera) {\n      //unproject cursor on the near plane\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, -1)\n      this._v3_1.applyMatrix4(camera.projectionMatrixInverse)\n\n      const rayDir = this._v3_1.clone().normalize() //unprojected ray direction\n      const cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position)\n      const radius2 = Math.pow(tbRadius, 2)\n\n      //\t  camera\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\th\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t_ _ | _ _ _\\ _ _  near plane\n      //\t\t\tl\n\n      const h = this._v3_1.z\n      const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2))\n\n      if (l == 0) {\n        //ray aligned with camera\n        rayDir.set(this._v3_1.x, this._v3_1.y, tbRadius)\n        return rayDir\n      }\n\n      const m = h / l\n      const q = cameraGizmoDistance\n\n      /*\n       * calculate intersection point between unprojected ray and trackball surface\n       *|y = m * x + q\n       *|x^2 + y^2 = r^2\n       *\n       * (m^2 + 1) * x^2 + (2 * m * q) * x + q^2 - r^2 = 0\n       */\n      let a = Math.pow(m, 2) + 1\n      let b = 2 * m * q\n      let c = Math.pow(q, 2) - radius2\n      let delta = Math.pow(b, 2) - 4 * a * c\n\n      if (delta >= 0) {\n        //intersection with sphere\n        this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a))\n        this._v2_1.setY(m * this._v2_1.x + q)\n\n        const angle = MathUtils.RAD2DEG * this._v2_1.angle()\n\n        if (angle >= 45) {\n          //if angle between intersection point and X' axis is >= 45°, return that point\n          //otherwise, calculate intersection point with hyperboloid\n\n          const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2))\n          rayDir.multiplyScalar(rayLength)\n          rayDir.z += cameraGizmoDistance\n          return rayDir\n        }\n      }\n\n      //intersection with hyperboloid\n      /*\n       *|y = m * x + q\n       *|y = (1 / x) * (r^2 / 2)\n       *\n       * m * x^2 + q * x - r^2 / 2 = 0\n       */\n\n      a = m\n      b = q\n      c = -radius2 * 0.5\n      delta = Math.pow(b, 2) - 4 * a * c\n      this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a))\n      this._v2_1.setY(m * this._v2_1.x + q)\n\n      const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2))\n\n      rayDir.multiplyScalar(rayLength)\n      rayDir.z += cameraGizmoDistance\n      return rayDir\n    }\n  }\n\n  /**\n   * Unproject the cursor on the plane passing through the center of the trackball orthogonal to the camera\n   * @param {Camera} camera The virtual camera\n   * @param {Number} cursorX Cursor horizontal coordinate on screen\n   * @param {Number} cursorY Cursor vertical coordinate on screen\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @param {Boolean} initialDistance If initial distance between camera and gizmos should be used for calculations instead of current (Perspective only)\n   * @returns {Vector3} The unprojected point on the trackball plane\n   */\n  private unprojectOnTbPlane = (\n    camera: Camera,\n    cursorX: number,\n    cursorY: number,\n    canvas: HTMLElement,\n    initialDistance = false,\n  ): Vector3 | undefined => {\n    if (camera instanceof OrthographicCamera) {\n      this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas))\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, 0)\n\n      return this._v3_1.clone()\n    }\n\n    if (camera instanceof PerspectiveCamera) {\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n\n      //unproject cursor on the near plane\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, -1)\n      this._v3_1.applyMatrix4(camera.projectionMatrixInverse)\n\n      const rayDir = this._v3_1.clone().normalize() //unprojected ray direction\n\n      //\t  camera\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\th\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t_ _ | _ _ _\\ _ _  near plane\n      //\t\t\tl\n\n      const h = this._v3_1.z\n      const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2))\n      let cameraGizmoDistance\n\n      if (initialDistance) {\n        cameraGizmoDistance = this._v3_1\n          .setFromMatrixPosition(this._cameraMatrixState0)\n          .distanceTo(this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0))\n      } else {\n        cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position)\n      }\n\n      /*\n       * calculate intersection point between unprojected ray and the plane\n       *|y = mx + q\n       *|y = 0\n       *\n       * x = -q/m\n       */\n      if (l == 0) {\n        //ray aligned with camera\n        rayDir.set(0, 0, 0)\n        return rayDir\n      }\n\n      const m = h / l\n      const q = cameraGizmoDistance\n      const x = -q / m\n\n      const rayLength = Math.sqrt(Math.pow(q, 2) + Math.pow(x, 2))\n      rayDir.multiplyScalar(rayLength)\n      rayDir.z = 0\n      return rayDir\n    }\n  }\n\n  /**\n   * Update camera and gizmos state\n   */\n  private updateMatrixState = (): void => {\n    if (!this.camera) return\n\n    //update camera and gizmos state\n    this._cameraMatrixState.copy(this.camera.matrix)\n    this._gizmoMatrixState.copy(this._gizmos.matrix)\n\n    if (this.camera instanceof OrthographicCamera) {\n      this._cameraProjectionState.copy(this.camera.projectionMatrix)\n      this.camera.updateProjectionMatrix()\n      this._zoomState = this.camera.zoom\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._fovState = this.camera.fov\n    }\n  }\n\n  /**\n   * Update the trackball FSA\n   * @param {STATE} newState New state of the FSA\n   * @param {Boolean} updateMatrices If matriices state should be updated\n   */\n  private updateTbState = (newState: Symbol, updateMatrices: boolean): void => {\n    this._state = newState\n    if (updateMatrices) {\n      this.updateMatrixState()\n    }\n  }\n\n  public update = (): void => {\n    const EPS = 0.000001\n\n    // Update target and gizmos state\n    if (!this.target.equals(this._currentTarget) && this.camera) {\n      this._gizmos.position.set(this.target.x, this.target.y, this.target.z) //for correct radius calculation\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n      this._currentTarget.copy(this.target)\n    }\n\n    if (!this.camera) return\n\n    //check min/max parameters\n    if (this.camera instanceof OrthographicCamera) {\n      //check zoom\n      if (this.camera.zoom > this.maxZoom || this.camera.zoom < this.minZoom) {\n        const newZoom = MathUtils.clamp(this.camera.zoom, this.minZoom, this.maxZoom)\n        this.applyTransformMatrix(this.applyScale(newZoom / this.camera.zoom, this._gizmos.position, true))\n      }\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      //check distance\n      const distance = this.camera.position.distanceTo(this._gizmos.position)\n\n      if (distance > this.maxDistance + EPS || distance < this.minDistance - EPS) {\n        const newDistance = MathUtils.clamp(distance, this.minDistance, this.maxDistance)\n        this.applyTransformMatrix(this.applyScale(newDistance / distance, this._gizmos.position))\n        this.updateMatrixState()\n      }\n\n      //check fov\n      if (this.camera.fov < this.minFov || this.camera.fov > this.maxFov) {\n        this.camera.fov = MathUtils.clamp(this.camera.fov, this.minFov, this.maxFov)\n        this.camera.updateProjectionMatrix()\n      }\n\n      const oldRadius = this._tbRadius\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n\n      if (oldRadius < this._tbRadius - EPS || oldRadius > this._tbRadius + EPS) {\n        const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3\n        const newRadius = this._tbRadius / scale\n        // @ts-ignore\n        const curve = new EllipseCurve(0, 0, newRadius, newRadius)\n        const points = curve.getPoints(this._curvePts)\n        const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n        for (const gizmo in this._gizmos.children) {\n          const child = this._gizmos.children[gizmo] as Mesh\n          child.geometry = curveGeometry\n        }\n      }\n    }\n\n    this.camera.lookAt(this._gizmos.position)\n  }\n\n  private setStateFromJSON = (json: string): void => {\n    const state = JSON.parse(json)\n\n    if (state.arcballState && this.camera) {\n      this._cameraMatrixState.fromArray(state.arcballState.cameraMatrix.elements)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n\n      this.camera.up.copy(state.arcballState.cameraUp)\n      this.camera.near = state.arcballState.cameraNear\n      this.camera.far = state.arcballState.cameraFar\n\n      this.camera.zoom = state.arcballState.cameraZoom\n\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = state.arcballState.cameraFov\n      }\n\n      this._gizmoMatrixState.fromArray(state.arcballState.gizmoMatrix.elements)\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n      this.camera.updateMatrix()\n      this.camera.updateProjectionMatrix()\n\n      this._gizmos.updateMatrix()\n\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      const gizmoTmp = new Matrix4().copy(this._gizmoMatrixState0)\n      this.makeGizmos(this._gizmos.position, this._tbRadius)\n      this._gizmoMatrixState0.copy(gizmoTmp)\n\n      this.camera.lookAt(this._gizmos.position)\n      this.updateTbState(STATE.IDLE, false)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n}\n\nexport { ArcballControls }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;AAwCA,MAAM,QAAQ;AAAA,EACZ,MAAM,OAAO;AAAA,EACb,QAAQ,OAAO;AAAA,EACf,KAAK,OAAO;AAAA,EACZ,OAAO,OAAO;AAAA,EACd,KAAK,OAAO;AAAA,EACZ,OAAO,OAAO;AAAA,EACd,SAAS,OAAO;AAAA,EAChB,aAAa,OAAO;AAAA,EACpB,iBAAiB,OAAO;AAAA,EACxB,kBAAkB,OAAO;AAC3B;AAEA,MAAM,QAAQ;AAAA,EACZ,MAAM,OAAO;AAAA,EACb,YAAY,OAAO;AAAA,EACnB,qBAAqB,OAAO;AAAA,EAC5B,YAAY,OAAO;AAAA,EACnB,aAAa,OAAO;AAAA,EACpB,QAAQ,OAAO;AACjB;AAGA,MAAM,UAAU;AAAA,EACd,GAAG;AAAA,EACH,GAAG;AACL;AAGA,MAAM,kBAAkC;AAAA,EACtC,4BAA4B,QAAQ;AAAA,EACpC,4BAA4B,QAAQ;AACtC;AAGA,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,cAAc,EAAE,MAAM;AAC5B,MAAM,YAAY,EAAE,MAAM;AAQ1B,MAAM,wBAAwB,gBAA0C;AAAA,EAmHtE,YACE,QACA,aAA6C,MAC7C,QAAkC,MAClC;AACM;AAvHA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAED;AAEC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEE;AACD;AAEA;AAEA;AA2JA;AAAA,0CAAiB,MAAY;AACnC,YAAM,SAAS,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,KAAK;AACrF,UAAI,KAAK,QAAQ;AACf,cAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AAAA,MACF;AAEM,YAAA,YAAY,KAAK,YAAY;AAEnC,YAAM,QAAQ,IAAI,aAAa,GAAG,GAAG,WAAW,SAAS;AACzD,YAAM,SAAS,MAAM,UAAU,KAAK,SAAS;AAC7C,YAAM,gBAAgB,IAAI,eAAe,EAAE,cAAc,MAAM;AAEpD,iBAAA,SAAS,KAAK,QAAQ,UAAU;AACzC,cAAM,QAAQ,KAAK,QAAQ,SAAS,KAAK;AACzC,cAAM,WAAW;AAAA,MACnB;AAGA,WAAK,cAAc,YAAY;AAAA,IAAA;AAGzB,yCAAgB,CAAC,UAA4B;AAC/C,UAAA,CAAC,KAAK,SAAS;AACjB;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,YAAI,KAAK,aAAa,CAAC,EAAE,SAAS,GAAG;AAEnC,gBAAM,eAAe;AACrB;AAAA,QACF;AAAA,MACF;AAAA,IAAA;AAGM,2CAAkB,MAAY;AACpC,WAAK,YAAY,OAAO,GAAG,KAAK,YAAY,MAAM;AAClD,WAAK,cAAc,OAAO,GAAG,KAAK,cAAc,MAAM;AACtD,WAAK,SAAS,MAAM;AAAA,IAAA;AAGd,yCAAgB,CAAC,UAA8B;AACrD,UAAI,MAAM,UAAU,KAAK,MAAM,WAAW;AACxC,aAAK,aAAa;AACb,aAAA,YAAY,KAAK,KAAK;AAAA,MAAA,OACtB;AACL,aAAK,aAAa;AAAA,MACpB;AAEA,UAAI,MAAM,eAAe,WAAW,KAAK,UAAU,MAAM,QAAQ;AAC1D,aAAA,YAAY,KAAK,KAAK;AACtB,aAAA,cAAc,KAAK,KAAK;AAE7B,gBAAQ,KAAK,QAAQ;AAAA,UACnB,KAAK,MAAM;AAET,iBAAK,SAAS,MAAM;AACf,iBAAA,iBAAiB,OAAO,QAAQ;AAE9B,mBAAA,iBAAiB,eAAe,KAAK,aAAa;AAClD,mBAAA,iBAAiB,aAAa,KAAK,WAAW;AAErD;AAAA,UAEF,KAAK,MAAM;AAAA,UACX,KAAK,MAAM;AAET,iBAAK,SAAS,MAAM;AAEpB,iBAAK,cAAc;AACnB,iBAAK,aAAa;AAClB,iBAAK,iBAAiB;AAEtB;AAAA,UAEF,KAAK,MAAM;AAET,iBAAK,SAAS,MAAM;AACpB,iBAAK,iBAAiB;AACtB;AAAA,QACJ;AAAA,MAAA,WACS,MAAM,eAAe,WAAW,KAAK,UAAU,MAAM,MAAM;AACpE,YAAI,WAA+B;AAE/B,YAAA,MAAM,WAAW,MAAM,SAAS;AACvB,qBAAA;AAAA,QAAA,WACF,MAAM,UAAU;AACd,qBAAA;AAAA,QACb;AAEA,aAAK,WAAW,KAAK,gBAAgB,MAAM,QAAQ,QAAQ;AAC3D,YAAI,KAAK,UAAU;AACV,iBAAA,iBAAiB,eAAe,KAAK,aAAa;AAClD,iBAAA,iBAAiB,aAAa,KAAK,WAAW;AAGrD,eAAK,SAAS,MAAM;AACpB,eAAK,UAAU,MAAM;AAChB,eAAA,iBAAiB,OAAO,KAAK,QAAQ;AAAA,QAC5C;AAAA,MACF;AAAA,IAAA;AAGM,yCAAgB,CAAC,UAA8B;AACrD,UAAI,MAAM,eAAe,WAAW,KAAK,UAAU,MAAM,QAAQ;AAC/D,gBAAQ,KAAK,QAAQ;AAAA,UACnB,KAAK,MAAM;AAET,iBAAK,iBAAiB,KAAK;AAEtB,iBAAA,gBAAgB,OAAO,MAAM,MAAM;AACxC;AAAA,UAEF,KAAK,MAAM;AACH,kBAAA,WAAW,KAAK,0BAA0B,KAAK,cAAc,CAAC,GAAG,KAAK,IAAI,KAAK;AAEjF,gBAAA,YAAY,KAAK,oBAAoB;AAEvC,mBAAK,SAAS,MAAM;AACpB,mBAAK,iBAAiB,KAAK;AAEtB,mBAAA,iBAAiB,OAAO,QAAQ;AACrC;AAAA,YACF;AAEA;AAAA,UAEF,KAAK,MAAM;AAET,iBAAK,iBAAiB,KAAK;AAE3B,iBAAK,aAAa;AAClB,iBAAK,YAAY;AACjB,iBAAK,gBAAgB;AAErB;AAAA,UAEF,KAAK,MAAM;AAET,iBAAK,iBAAiB,KAAK;AAE3B,iBAAK,gBAAgB;AACrB;AAAA,QACJ;AAAA,MAAA,WACS,MAAM,eAAe,WAAW,KAAK,UAAU,MAAM,QAAQ;AACtE,YAAI,WAA+B;AAE/B,YAAA,MAAM,WAAW,MAAM,SAAS;AACvB,qBAAA;AAAA,QAAA,WACF,MAAM,UAAU;AACd,qBAAA;AAAA,QACb;AAEA,cAAM,eAAe,KAAK,qBAAqB,KAAK,SAAS,QAAQ;AAErE,YAAI,cAAc;AACX,eAAA,gBAAgB,OAAO,YAAY;AAAA,QAC1C;AAAA,MACF;AAGA,UAAI,KAAK,YAAY;AACnB,cAAM,WACJ,KAAK,0BAA0B,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC,GAAG,KAAK,IAAI,KAAK;AAC1F,YAAA,WAAW,KAAK,oBAAoB;AACtC,eAAK,aAAa;AAAA,QACpB;AAAA,MACF;AAAA,IAAA;AAGM,uCAAc,CAAC,UAA8B;AACnD,UAAI,MAAM,eAAe,WAAW,KAAK,UAAU,MAAM,QAAQ;AACzD,cAAA,SAAS,KAAK,cAAc;AAElC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAI,KAAK,cAAc,CAAC,EAAE,aAAa,MAAM,WAAW;AACjD,iBAAA,cAAc,OAAO,GAAG,CAAC;AACzB,iBAAA,YAAY,OAAO,GAAG,CAAC;AAC5B;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,KAAK,QAAQ;AAAA,UACnB,KAAK,MAAM;AAAA,UACX,KAAK,MAAM;AAEF,mBAAA,oBAAoB,eAAe,KAAK,aAAa;AACrD,mBAAA,oBAAoB,aAAa,KAAK,WAAW;AAExD,iBAAK,SAAS,MAAM;AACpB,iBAAK,eAAe;AAEpB;AAAA,UAEF,KAAK,MAAM;AAET,iBAAK,eAAe;AACpB,iBAAK,WAAW;AAChB,iBAAK,YAAY;AAGjB,iBAAK,SAAS,MAAM;AAEpB;AAAA,UAEF,KAAK,MAAM;AACL,gBAAA,KAAK,cAAc,UAAU,GAAG;AAC3B,qBAAA,oBAAoB,eAAe,KAAK,aAAa;AACrD,qBAAA,oBAAoB,aAAa,KAAK,WAAW;AAGxD,mBAAK,SAAS,MAAM;AACpB,mBAAK,eAAe;AAAA,YACtB;AAEA;AAAA,QACJ;AAAA,MAAA,WACS,MAAM,eAAe,WAAW,KAAK,UAAU,MAAM,QAAQ;AAC/D,eAAA,oBAAoB,eAAe,KAAK,aAAa;AACrD,eAAA,oBAAoB,aAAa,KAAK,WAAW;AAExD,aAAK,SAAS,MAAM;AACpB,aAAK,eAAe;AACpB,aAAK,UAAU;AAAA,MACjB;AAEA,UAAI,MAAM,WAAW;AACnB,YAAI,KAAK,YAAY;AACb,gBAAA,WAAW,MAAM,YAAY,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC,EAAE;AAE7E,cAAA,YAAY,KAAK,cAAc;AAC7B,gBAAA,KAAK,YAAY,GAAG;AAEtB,mBAAK,WAAW;AACX,mBAAA,cAAc,YAAY;YAAI,OAC9B;AACC,oBAAA,gBAAgB,MAAM,YAAY,KAAK;AAC7C,oBAAM,WAAW,KAAK,0BAA0B,KAAK,YAAY,CAAC,GAAG,KAAK,YAAY,CAAC,CAAC,IAAI,KAAK;AAEjG,kBAAI,iBAAiB,KAAK,gBAAgB,YAAY,KAAK,eAAe;AAGxE,qBAAK,WAAW;AAChB,qBAAK,YAAY,OAAO,GAAG,KAAK,YAAY,MAAM;AAClD,qBAAK,YAAY,KAAK;AAAA,cAAA,OACjB;AAEL,qBAAK,WAAW;AAChB,qBAAK,YAAY;AACZ,qBAAA,cAAc,YAAY;cACjC;AAAA,YACF;AAAA,UAAA,OACK;AACL,iBAAK,aAAa;AAClB,iBAAK,WAAW;AAChB,iBAAK,YAAY,OAAO,GAAG,KAAK,YAAY,MAAM;AAAA,UACpD;AAAA,QAAA,OACK;AACL,eAAK,WAAW;AAChB,eAAK,YAAY,OAAO,GAAG,KAAK,YAAY,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,IAAA;AAGM,mCAAU,CAAC,UAA4B;;AAC7C,UAAI,KAAK,WAAW,KAAK,cAAc,KAAK,YAAY;AACtD,YAAI,WAA+B;AAE/B,YAAA,MAAM,WAAW,MAAM,SAAS;AACvB,qBAAA;AAAA,QAAA,WACF,MAAM,UAAU;AACd,qBAAA;AAAA,QACb;AAEA,cAAM,UAAU,KAAK,gBAAgB,SAAS,QAAQ;AAEtD,YAAI,SAAS;AACX,gBAAM,eAAe;AAErB,eAAK,cAAc,WAAW;AAE9B,gBAAM,cAAc;AAChB,cAAA,MAAM,MAAM,SAAS;AAEzB,cAAI,OAAO;AAEX,cAAI,MAAM,GAAG;AACX,mBAAO,IAAI,KAAK;AAAA,UAAA,WACP,MAAM,GAAG;AAClB,mBAAO,KAAK;AAAA,UACd;AAEA,kBAAQ,SAAS;AAAA,YACf,KAAK;AACE,mBAAA,cAAc,MAAM,OAAO,IAAI;AAEpC,kBAAI,MAAM,GAAG;AACX,uBAAO,IAAI,KAAK,IAAI,KAAK,aAAa,GAAG;AAAA,cAAA,WAChC,MAAM,GAAG;AAClB,uBAAO,KAAK,IAAI,KAAK,aAAa,CAAC,GAAG;AAAA,cACxC;AAEI,kBAAA,KAAK,cAAc,KAAK,WAAW;AACjC,oBAAA;AAEA,oBAAA,KAAK,kBAAkB,oBAAoB;AAChC,gCAAA,UAAK,mBAAmB,KAAK,QAAQ,MAAM,SAAS,MAAM,SAAS,KAAK,UAAU,MAAlF,mBACT,gBAAgB,KAAK,OAAO,YAC7B,eAAe,IAAI,KAAK,OAAO,MAC/B,IAAI,KAAK,QAAQ;AAAA,gBACtB;AAEI,oBAAA,KAAK,kBAAkB,mBAAmB;AAC5C,gCAAa,UAAK,mBAAmB,KAAK,QAAQ,MAAM,SAAS,MAAM,SAAS,KAAK,UAAU,MAAlF,mBACT,gBAAgB,KAAK,OAAO,YAC7B,IAAI,KAAK,QAAQ;AAAA,gBACtB;AAEA,oBAAI,eAAe;AAAW,uBAAK,qBAAqB,KAAK,WAAW,MAAM,UAAU,CAAC;AAAA,cAAA,OACpF;AACL,qBAAK,qBAAqB,KAAK,WAAW,MAAM,KAAK,QAAQ,QAAQ,CAAC;AAAA,cACxE;AAEA,kBAAI,KAAK,OAAO;AACd,qBAAK,YAAY;AACjB,qBAAK,SAAS;AAAA,cAChB;AAEK,mBAAA,cAAc,MAAM,MAAM,KAAK;AAGpC,mBAAK,cAAc,YAAY;AAE/B,mBAAK,cAAc,SAAS;AAE5B;AAAA,YAEF,KAAK;AACC,kBAAA,KAAK,kBAAkB,mBAAmB;AACvC,qBAAA,cAAc,MAAM,KAAK,IAAI;AAe9B,oBAAA,MAAM,UAAU,GAAG;AACrB,wBAAM,MAAM,SAAS;AAEd,yBAAA;AAEP,sBAAI,MAAM,GAAG;AACX,2BAAO,IAAI,KAAK,IAAI,KAAK,aAAa,GAAG;AAAA,kBAAA,WAChC,MAAM,GAAG;AAClB,2BAAO,KAAK,IAAI,KAAK,aAAa,CAAC,GAAG;AAAA,kBACxC;AAAA,gBACF;AAEK,qBAAA,MAAM,sBAAsB,KAAK,kBAAkB;AACxD,sBAAM,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,QAAQ;AACrD,oBAAI,OAAO,IAAI;AAGf,uBAAO,UAAU,MAAM,MAAM,KAAK,aAAa,KAAK,WAAW;AAEzD,sBAAA,IAAI,IAAI,KAAK,IAAI,UAAU,UAAU,KAAK,OAAO,MAAM,GAAG;AAGhE,oBAAI,SAAS,UAAU,WAAW,KAAK,KAAK,IAAI,IAAI,IAAI;AAGpD,oBAAA,SAAS,KAAK,QAAQ;AACxB,2BAAS,KAAK;AAAA,gBAAA,WACL,SAAS,KAAK,QAAQ;AAC/B,2BAAS,KAAK;AAAA,gBAChB;AAEA,sBAAM,cAAc,IAAI,KAAK,IAAI,UAAU,WAAW,SAAS,EAAE;AACjE,uBAAO,IAAI;AAEX,qBAAK,OAAO,MAAM;AACb,qBAAA,qBAAqB,KAAK,WAAW,MAAM,KAAK,QAAQ,UAAU,KAAK,CAAC;AAAA,cAC/E;AAEA,kBAAI,KAAK,OAAO;AACd,qBAAK,YAAY;AACjB,qBAAK,SAAS;AAAA,cAChB;AAEK,mBAAA,cAAc,MAAM,MAAM,KAAK;AAGpC,mBAAK,cAAc,YAAY;AAE/B,mBAAK,cAAc,SAAS;AAE5B;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IAAA;AAGM,4CAAmB,CAAC,OAAqB,cAA+B;AAC1E,UAAA,KAAK,WAAW,KAAK,YAAY;AAEnC,aAAK,cAAc,WAAW;AAE9B,aAAK,UAAU,MAAM,SAAS,MAAM,OAAO;AAE3C,gBAAQ,WAAW;AAAA,UACjB,KAAK;AACH,gBAAI,CAAC,KAAK;AAAW;AAEjB,gBAAA,KAAK,gBAAgB,IAAI;AAC3B,mCAAqB,KAAK,YAAY;AACtC,mBAAK,eAAe;AACpB,mBAAK,aAAa;AAElB,mBAAK,eAAe,KAAK;AAEzB,mBAAK,cAAc,YAAY;AAAA,YACjC;AAEA,gBAAI,KAAK,QAAQ;AACV,mBAAA,cAAc,MAAM,KAAK,IAAI;AAC5B,oBAAA,SAAS,KAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU;AACzF,kBAAI,WAAW,QAAW;AACnB,qBAAA,qBAAqB,KAAK,MAAM;AAAA,cACvC;AACA,kBAAI,KAAK,YAAY;AACnB,qBAAK,SAAS;AAEd,qBAAK,cAAc,YAAY;AAAA,cACjC;AAAA,YACF;AAEA;AAAA,UAEF,KAAK;AACH,gBAAI,CAAC,KAAK;AAAc;AAEpB,gBAAA,KAAK,gBAAgB,IAAI;AAC3B,mCAAqB,KAAK,YAAY;AACtC,mBAAK,eAAe;AACpB,mBAAK,aAAa;AAAA,YACpB;AAEA,gBAAI,KAAK,QAAQ;AACV,mBAAA,cAAc,MAAM,QAAQ,IAAI;AACrC,oBAAM,SAAS,KAAK,qBAAqB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,YAAY,KAAK,SAAS;AAC3G,kBAAI,WAAW,QAAW;AACnB,qBAAA,qBAAqB,KAAK,MAAM;AAAA,cACvC;AACA,mBAAK,eAAe,IAAI;AACxB,kBAAI,KAAK,kBAAkB;AACzB,qBAAK,YAAY,KAAK,eAAe,YAAY,IAAI;AAChD,qBAAA,gBAAgB,KAAK,aAAa;AAClC,qBAAA,eAAe,KAAK,KAAK,oBAAoB;AAC7C,qBAAA,eAAe,KAAK,KAAK,cAAc;AAC5C,qBAAK,SAAS;AACd,qBAAK,SAAS,KAAK;AAAA,cACrB;AAAA,YACF;AAGA,iBAAK,cAAc,YAAY;AAC/B;AAAA,UAEF,KAAK;AACH,gBAAI,CAAC,KAAK;AAAY;AAElB,gBAAA,KAAK,kBAAkB,mBAAmB;AACxC,kBAAA,KAAK,gBAAgB,IAAI;AAC3B,qCAAqB,KAAK,YAAY;AACtC,qBAAK,eAAe;AACpB,qBAAK,aAAa;AAElB,qBAAK,eAAe,KAAK;AAEzB,qBAAK,cAAc,YAAY;AAAA,cACjC;AAEK,mBAAA,cAAc,MAAM,KAAK,IAAI;AAClC,mBAAK,qBAAqB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAC1F,mBAAA,uBAAuB,KAAK,KAAK,oBAAoB;AAAA,YAC5D;AACA;AAAA,UAEF,KAAK;AACH,gBAAI,CAAC,KAAK;AAAY;AAElB,gBAAA,KAAK,gBAAgB,IAAI;AAC3B,mCAAqB,KAAK,YAAY;AACtC,mBAAK,eAAe;AACpB,mBAAK,aAAa;AAElB,mBAAK,eAAe,KAAK;AAEzB,mBAAK,cAAc,YAAY;AAAA,YACjC;AAEK,iBAAA,cAAc,MAAM,OAAO,IAAI;AACpC,iBAAK,qBAAqB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAC1F,iBAAA,uBAAuB,KAAK,KAAK,oBAAoB;AAC1D;AAAA,QACJ;AAAA,MACF;AAAA,IAAA;AAGM,2CAAkB,CAAC,OAAqB,YAA0B;AACpE,UAAA,KAAK,WAAW,KAAK,YAAY;AAC7B,cAAA,UAAU,WAAW,KAAK;AAChC,aAAK,UAAU,MAAM,SAAS,MAAM,OAAO;AAE3C,gBAAQ,SAAS;AAAA,UACf,KAAK,MAAM;AACL,gBAAA,KAAK,aAAa,KAAK,QAAQ;AACjC,kBAAI,SAAS;AAIX,qBAAK,cAAc,SAAS;AAE5B,qBAAK,cAAc,WAAW;AAEzB,qBAAA,cAAc,SAAS,IAAI;AAC1B,sBAAA,SAAS,KAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU;AACzF,oBAAI,WAAW,QAAW;AACnB,uBAAA,qBAAqB,KAAK,MAAM;AAAA,gBACvC;AACA,oBAAI,KAAK,YAAY;AACnB,uBAAK,SAAS;AAAA,gBAChB;AAEA,qBAAK,eAAe,KAAK;AAAA,cAAA,OACpB;AAEC,sBAAA,SAAS,KAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU;AACzF,oBAAI,WAAW,QAAW;AACnB,uBAAA,uBAAuB,KAAK,MAAM;AAAA,gBACzC;AACA,qBAAK,qBAAqB,KAAK,IAAI,KAAK,sBAAsB,KAAK,sBAAsB,CAAC;AAAA,cAC5F;AAAA,YACF;AAEA;AAAA,UAEF,KAAK,MAAM;AACL,gBAAA,KAAK,gBAAgB,KAAK,QAAQ;AACpC,kBAAI,SAAS;AAIX,qBAAK,cAAc,SAAS;AAE5B,qBAAK,cAAc,WAAW;AAEzB,qBAAA,cAAc,SAAS,IAAI;AAChC,sBAAM,SAAS,KAAK;AAAA,kBAClB,KAAK;AAAA,kBACL,QAAQ;AAAA,kBACR,QAAQ;AAAA,kBACR,KAAK;AAAA,kBACL,KAAK;AAAA,gBAAA;AAEP,oBAAI,WAAW,QAAW;AACnB,uBAAA,qBAAqB,KAAK,MAAM;AAAA,gBACvC;AAEA,oBAAI,KAAK,YAAY;AACnB,uBAAK,YAAY;AAAA,gBACnB;AAEA,qBAAK,eAAe,IAAI;AAAA,cAAA,OACnB;AAEL,sBAAM,SAAS,KAAK;AAAA,kBAClB,KAAK;AAAA,kBACL,QAAQ;AAAA,kBACR,QAAQ;AAAA,kBACR,KAAK;AAAA,kBACL,KAAK;AAAA,gBAAA;AAEP,oBAAI,WAAW,QAAW;AACnB,uBAAA,uBAAuB,KAAK,MAAM;AAAA,gBACzC;AAEA,sBAAM,WAAW,KAAK,qBAAqB,WAAW,KAAK,sBAAsB;AACjF,sBAAM,QAAQ,KAAK,qBAAqB,QAAQ,KAAK,sBAAsB;AAC3E,sBAAM,SAAS,KAAK,IAAI,WAAW,KAAK,WAAW,KAAK;AAEnD,qBAAA;AAAA,kBACH,KAAK,OAAO,KAAK,sBAAsB,KAAK,sBAAsB,KAAK,sBAAsB,GAAG,MAAM;AAAA,gBAAA;AAGxG,oBAAI,KAAK,kBAAkB;AACzB,uBAAK,YAAY,KAAK;AACjB,uBAAA,eAAe,YAAY;AAChC,uBAAK,aAAa,KAAK;AACvB,uBAAK,gBAAgB;AAChB,uBAAA,eAAe,KAAK,KAAK,cAAc;AACvC,uBAAA,eAAe,KAAK,KAAK,sBAAsB;AACpD,uBAAK,SAAS,KAAK;AACnB,uBAAK,SAAS,KAAK;AAAA,oBACjB,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAA,kBAAA;AAAA,gBAET;AAAA,cACF;AAAA,YACF;AAEA;AAAA,UAEF,KAAK,MAAM;AACT,gBAAI,KAAK,YAAY;AACnB,kBAAI,SAAS;AAIX,qBAAK,cAAc,SAAS;AAE5B,qBAAK,cAAc,WAAW;AAEzB,qBAAA,cAAc,SAAS,IAAI;AAChC,qBAAK,qBAAqB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAC1F,qBAAA,uBAAuB,KAAK,KAAK,oBAAoB;AAE1D,oBAAI,KAAK,YAAY;AACnB,uBAAK,YAAY;AAAA,gBACnB;AAEA,qBAAK,eAAe,KAAK;AAAA,cAAA,OACpB;AAEL,sBAAM,gBAAgB;AACtB,qBAAK,uBAAuB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAEjG,sBAAM,WAAW,KAAK,uBAAuB,IAAI,KAAK,qBAAqB;AAE3E,oBAAI,OAAO;AAEX,oBAAI,WAAW,GAAG;AAChB,yBAAO,IAAI,KAAK,IAAI,KAAK,aAAa,CAAC,WAAW,aAAa;AAAA,gBAAA,WACtD,WAAW,GAAG;AACvB,yBAAO,KAAK,IAAI,KAAK,aAAa,WAAW,aAAa;AAAA,gBAC5D;AAEA,qBAAK,qBAAqB,KAAK,WAAW,MAAM,KAAK,QAAQ,QAAQ,CAAC;AAAA,cACxE;AAAA,YACF;AAEA;AAAA,UAEF,KAAK,MAAM;AACT,gBAAI,KAAK,cAAc,KAAK,kBAAkB,mBAAmB;AAC/D,kBAAI,SAAS;AAIX,qBAAK,cAAc,SAAS;AAE5B,qBAAK,cAAc,WAAW;AAEzB,qBAAA,cAAc,SAAS,IAAI;AAChC,qBAAK,qBAAqB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAC1F,qBAAA,uBAAuB,KAAK,KAAK,oBAAoB;AAE1D,oBAAI,KAAK,YAAY;AACnB,uBAAK,YAAY;AAAA,gBACnB;AAEA,qBAAK,eAAe,KAAK;AAAA,cAAA,OACpB;AAEL,sBAAM,gBAAgB;AACtB,qBAAK,uBAAuB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAEjG,sBAAM,WAAW,KAAK,uBAAuB,IAAI,KAAK,qBAAqB;AAE3E,oBAAI,OAAO;AAEX,oBAAI,WAAW,GAAG;AAChB,yBAAO,IAAI,KAAK,IAAI,KAAK,aAAa,CAAC,WAAW,aAAa;AAAA,gBAAA,WACtD,WAAW,GAAG;AACvB,yBAAO,KAAK,IAAI,KAAK,aAAa,WAAW,aAAa;AAAA,gBAC5D;AAEK,qBAAA,MAAM,sBAAsB,KAAK,kBAAkB;AACxD,sBAAM,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,QAAQ;AACrD,oBAAI,OAAO,IAAI;AAGf,uBAAO,UAAU,MAAM,MAAM,KAAK,aAAa,KAAK,WAAW;AAEzD,sBAAA,IAAI,IAAI,KAAK,IAAI,UAAU,UAAU,KAAK,YAAY,GAAG;AAG/D,oBAAI,SAAS,UAAU,WAAW,KAAK,KAAK,IAAI,IAAI,IAAI;AAGxD,yBAAS,UAAU,MAAM,QAAQ,KAAK,QAAQ,KAAK,MAAM;AAEzD,sBAAM,cAAc,IAAI,KAAK,IAAI,UAAU,WAAW,SAAS,EAAE;AACjE,uBAAO,IAAI;AACN,qBAAA,MAAM,sBAAsB,KAAK,iBAAiB;AAEvD,qBAAK,OAAO,MAAM;AAClB,qBAAK,qBAAqB,KAAK,WAAW,MAAM,KAAK,OAAO,KAAK,CAAC;AAGlE,sBAAM,YAAY,KAAK,QAAQ,SAC5B,MACA,EAAA,IAAI,KAAK,OAAO,QAAQ,EACxB,UAAA,EACA,eAAe,cAAc,CAAC;AACjC,qBAAK,MAAM,gBAAgB,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC;AAAA,cAClE;AAAA,YACF;AAEA;AAAA,QACJ;AAGA,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AAGM,0CAAiB,MAAY;AAC/B,UAAA,KAAK,UAAU,MAAM,QAAQ;AAC3B,YAAA,CAAC,KAAK,cAAc;AACtB;AAAA,QACF;AAEA,YAAI,KAAK,kBAAkB;AAEzB,gBAAM,YAAY,YAAY,IAAI,IAAI,KAAK;AAC3C,cAAI,YAAY,KAAK;AACnB,kBAAM,IAAI,KAAK,KAAK,KAAK,SAAS,KAAK,UAAU,CAAC;AAElD,kBAAM,OAAO;AACb,iBAAK,eAAe,OAAO,sBAAsB,SAAU,GAAG;AACvD,mBAAA,cAAc,MAAM,kBAAkB,IAAI;AAC/C,oBAAM,eAAe,KAAK,sBAAsB,KAAK,gBAAgB,KAAK,cAAc;AAEnF,mBAAA,eAAe,GAAG,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,YAAA,CAC5D;AAAA,UAAA,OACI;AAEA,iBAAA,cAAc,MAAM,MAAM,KAAK;AACpC,iBAAK,eAAe,KAAK;AAEzB,iBAAK,cAAc,YAAY;AAAA,UACjC;AAAA,QAAA,OACK;AACA,eAAA,cAAc,MAAM,MAAM,KAAK;AACpC,eAAK,eAAe,KAAK;AAEzB,eAAK,cAAc,YAAY;AAAA,QACjC;AAAA,MAAA,WACS,KAAK,UAAU,MAAM,OAAO,KAAK,UAAU,MAAM,MAAM;AAC3D,aAAA,cAAc,MAAM,MAAM,KAAK;AAEpC,YAAI,KAAK,YAAY;AACnB,eAAK,YAAY;AAAA,QACnB;AAEA,aAAK,eAAe,KAAK;AAEzB,aAAK,cAAc,YAAY;AAAA,MACjC;AAGA,WAAK,cAAc,SAAS;AAAA,IAAA;AAGtB,uCAAc,CAAC,UAA8B;AAC/C,UAAA,KAAK,WAAW,KAAK,aAAa,KAAK,SAAS,KAAK,UAAU,KAAK,YAAY;AAElF,aAAK,cAAc,WAAW;AAE9B,aAAK,UAAU,MAAM,SAAS,MAAM,OAAO;AAC3C,cAAM,OAAO,KAAK,eAAe,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,GAAG,KAAK,MAAM;AAElG,YAAA,QAAQ,KAAK,kBAAkB;AACjC,gBAAM,OAAO;AACT,cAAA,KAAK,gBAAgB,IAAI;AACpB,mBAAA,qBAAqB,KAAK,YAAY;AAAA,UAC/C;AAEA,eAAK,aAAa;AAClB,eAAK,eAAe,OAAO,sBAAsB,SAAU,GAAG;AACvD,iBAAA,cAAc,MAAM,iBAAiB,IAAI;AAC9C,iBAAK,YAAY,GAAG,MAAM,KAAK,oBAAoB,KAAK,iBAAiB;AAAA,UAAA,CAC1E;AAAA,QACQ,WAAA,QAAQ,CAAC,KAAK,kBAAkB;AACpC,eAAA,cAAc,MAAM,OAAO,IAAI;AAC/B,eAAA,MAAM,MAAM,KAAK,WAAW;AAC5B,eAAA,cAAc,MAAM,MAAM,KAAK;AAEpC,eAAK,cAAc,YAAY;AAAA,QACjC;AAAA,MACF;AAGA,WAAK,cAAc,SAAS;AAAA,IAAA;AAGtB,4CAAmB,MAAY;AACrC,UAAI,KAAK,WAAW,KAAK,aAAa,KAAK,UAAU,KAAK,YAAY;AAEpE,aAAK,cAAc,WAAW;AAEzB,aAAA,cAAc,MAAM,KAAK,IAAI;AAE7B,aAAA;AAAA,WACF,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,WACjE,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,QAAA;AAG9D,cAAA,SAAS,KAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,YAAY,IAAI;AAC/F,YAAI,WAAW,QAAW;AACnB,eAAA,qBAAqB,KAAK,MAAM;AAAA,QACvC;AACK,aAAA,uBAAuB,KAAK,KAAK,oBAAoB;AAE1D,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IAAA;AAGM,2CAAkB,MAAY;AACpC,UAAI,KAAK,WAAW,KAAK,aAAa,KAAK,UAAU,KAAK,YAAY;AAC/D,aAAA;AAAA,WACF,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,WACjE,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,QAAA;AAGhE,YAAA,KAAK,UAAU,MAAM,KAAK;AACvB,eAAA,cAAc,MAAM,KAAK,IAAI;AAC7B,eAAA,qBAAqB,KAAK,KAAK,sBAAsB;AAAA,QAC5D;AAEM,cAAA,SAAS,KAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,YAAY,IAAI;AAC/F,YAAI,WAAW;AAAgB,eAAA,uBAAuB,KAAK,MAAM;AAC5D,aAAA,qBAAqB,KAAK,IAAI,KAAK,sBAAsB,KAAK,wBAAwB,IAAI,CAAC;AAEhG,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AAGM,0CAAiB,MAAY;AAC9B,WAAA,cAAc,MAAM,MAAM,KAAK;AAEpC,WAAK,cAAc,SAAS;AAAA,IAAA;AAGtB,yCAAgB,MAAY;;AAC9B,UAAA,KAAK,WAAW,KAAK,cAAc;AAErC,aAAK,cAAc,WAAW;AAEzB,aAAA,cAAc,MAAM,SAAS,IAAI;AAIjC,aAAA,uBACH,KAAK,SAAS,KAAK,cAAc,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC,IAC1D,KAAK,SAAS,KAAK,YAAY,CAAC,GAAG,KAAK,YAAY,CAAC,CAAC;AACxD,aAAK,yBAAyB,KAAK;AAE9B,mBAAA,WAAA,mBAAQ,kBAAkB,KAAK;AAEpC,YAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY;AACvC,eAAK,eAAe,IAAI;AAAA,QAC1B;AAAA,MACF;AAAA,IAAA;AAGM,wCAAe,MAAY;;AACjC,UAAI,KAAK,WAAW,KAAK,gBAAgB,KAAK,UAAU,KAAK,YAAY;AAClE,aAAA;AAAA,WACF,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,WACjE,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,QAAA;AAEhE,YAAA;AAEA,YAAA,KAAK,UAAU,MAAM,SAAS;AAC3B,eAAA,cAAc,MAAM,SAAS,IAAI;AACtC,eAAK,uBAAuB,KAAK;AAAA,QACnC;AAGK,aAAA,yBACH,KAAK,SAAS,KAAK,cAAc,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC,IAC1D,KAAK,SAAS,KAAK,YAAY,CAAC,GAAG,KAAK,YAAY,CAAC,CAAC;AAEpD,YAAA,CAAC,KAAK,WAAW;AACnB,0BAAgB,IAAI,QAAU,EAAA,sBAAsB,KAAK,iBAAiB;AAAA,QAAA,WACjE,KAAK,QAAQ;AACjB,eAAA,MAAM,sBAAsB,KAAK,iBAAiB;AACvC,2BAAA,UAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,MAA1E,mBACZ,gBAAgB,KAAK,OAAO,YAC7B,eAAe,IAAI,KAAK,OAAO,MAC/B,IAAI,KAAK;AAAA,QACd;AAEA,cAAM,SAAS,UAAU,WAAW,KAAK,uBAAuB,KAAK;AAErE,YAAI,kBAAkB,QAAW;AAC/B,eAAK,qBAAqB,KAAK,QAAQ,eAAe,MAAM,CAAC;AAAA,QAC/D;AAEA,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AAGM,uCAAc,MAAY;AAC3B,WAAA,cAAc,MAAM,MAAM,KAAK;AACpC,WAAK,eAAe,KAAK;AAEzB,WAAK,cAAc,SAAS;AAAA,IAAA;AAGtB,wCAAe,MAAY;AAC7B,UAAA,KAAK,WAAW,KAAK,YAAY;AAEnC,aAAK,cAAc,WAAW;AACzB,aAAA,cAAc,MAAM,OAAO,IAAI;AAE/B,aAAA,uBAAuB,KAAK,0BAA0B,KAAK,cAAc,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC;AACvG,aAAK,yBAAyB,KAAK;AAEnC,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IAAA;AAGM,uCAAc,MAAY;;AAChC,UAAI,KAAK,WAAW,KAAK,cAAc,KAAK,YAAY;AACjD,aAAA;AAAA,WACF,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,WACjE,KAAK,cAAc,CAAC,EAAE,UAAU,KAAK,cAAc,CAAC,EAAE,WAAW;AAAA,QAAA;AAEpE,cAAM,cAAc;AAEhB,YAAA,KAAK,UAAU,MAAM,OAAO;AAC9B,eAAK,uBAAuB,KAAK;AAC5B,eAAA,cAAc,MAAM,OAAO,IAAI;AAAA,QACtC;AAEA,aAAK,yBAAyB,KAAK;AAAA,UACjC,KAAK,0BAA0B,KAAK,cAAc,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,UAC3E,cAAc,KAAK;AAAA,QAAA;AAEf,cAAA,SAAS,KAAK,yBAAyB,KAAK;AAE9C,YAAA;AAEA,YAAA,CAAC,KAAK,WAAW;AACnB,uBAAa,KAAK,QAAQ;AAAA,QAAA,OACrB;AACD,cAAA,KAAK,kBAAkB,oBAAoB;AAChC,0BAAA,UAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,MAA1E,mBACT,gBAAgB,KAAK,OAAO,YAC7B,eAAe,IAAI,KAAK,OAAO,MAC/B,IAAI,KAAK,QAAQ;AAAA,UAAQ,WACnB,KAAK,kBAAkB,mBAAmB;AACnD,0BAAa,UAAK,mBAAmB,KAAK,QAAQ,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,MAA1E,mBACT,gBAAgB,KAAK,OAAO,YAC7B,IAAI,KAAK,QAAQ;AAAA,UACtB;AAAA,QACF;AAEA,YAAI,eAAe,QAAW;AAC5B,eAAK,qBAAqB,KAAK,WAAW,QAAQ,UAAU,CAAC;AAAA,QAC/D;AAEA,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AAGM,sCAAa,MAAY;AAC1B,WAAA,cAAc,MAAM,MAAM,KAAK;AAEpC,WAAK,cAAc,SAAS;AAAA,IAAA;AAGtB,4CAAmB,MAAY;AACrC,UAAI,KAAK,WAAW,KAAK,cAAc,KAAK,YAAY;AAEtD,aAAK,cAAc,WAAW;AAEzB,aAAA,cAAc,MAAM,OAAO,IAAI;AAGpC,YAAI,UAAU;AACd,YAAI,UAAU;AACR,cAAA,WAAW,KAAK,cAAc;AAEpC,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACtB,qBAAA,KAAK,cAAc,CAAC,EAAE;AACtB,qBAAA,KAAK,cAAc,CAAC,EAAE;AAAA,QACnC;AAEA,aAAK,UAAU,UAAU,UAAU,UAAU,QAAQ;AAErD,aAAK,qBAAqB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAC1F,aAAA,uBAAuB,KAAK,KAAK,oBAAoB;AAAA,MAC5D;AAAA,IAAA;AAGM,2CAAkB,MAAY;AACpC,UAAI,KAAK,WAAW,KAAK,cAAc,KAAK,UAAU,KAAK,YAAY;AAYrE,YAAI,UAAU;AACd,YAAI,UAAU;AACR,cAAA,WAAW,KAAK,cAAc;AAEpC,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACtB,qBAAA,KAAK,cAAc,CAAC,EAAE;AACtB,qBAAA,KAAK,cAAc,CAAC,EAAE;AAAA,QACnC;AAEA,aAAK,UAAU,UAAU,UAAU,UAAU,QAAQ;AAErD,cAAM,gBAAgB;AACtB,aAAK,uBAAuB,KAAK,KAAK,aAAa,QAAQ,GAAG,QAAQ,GAAG,KAAK,UAAU,EAAE,IAAI,GAAG;AAEjG,cAAM,WAAW,KAAK,uBAAuB,IAAI,KAAK,qBAAqB;AAE3E,YAAI,OAAO;AAEX,YAAI,WAAW,GAAG;AAChB,iBAAO,IAAI,KAAK,IAAI,KAAK,aAAa,CAAC,WAAW,aAAa;AAAA,QAAA,WACtD,WAAW,GAAG;AACvB,iBAAO,KAAK,IAAI,KAAK,aAAa,WAAW,aAAa;AAAA,QAC5D;AAEK,aAAA,MAAM,sBAAsB,KAAK,kBAAkB;AACxD,cAAM,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,QAAQ;AACrD,YAAI,OAAO,IAAI;AAGf,eAAO,UAAU,MAAM,MAAM,KAAK,aAAa,KAAK,WAAW;AAEzD,cAAA,IAAI,IAAI,KAAK,IAAI,UAAU,UAAU,KAAK,YAAY,GAAG;AAG/D,YAAI,SAAS,UAAU,WAAW,KAAK,KAAK,IAAI,IAAI,IAAI;AAGxD,iBAAS,UAAU,MAAM,QAAQ,KAAK,QAAQ,KAAK,MAAM;AAEzD,cAAM,cAAc,IAAI,KAAK,IAAI,UAAU,WAAW,SAAS,EAAE;AACjE,eAAO,IAAI;AACN,aAAA,MAAM,sBAAsB,KAAK,iBAAiB;AAEvD,aAAK,OAAO,MAAM;AAClB,aAAK,qBAAqB,KAAK,WAAW,MAAM,KAAK,OAAO,KAAK,CAAC;AAGlE,cAAM,YAAY,KAAK,QAAQ,SAC5B,MACA,EAAA,IAAI,KAAK,OAAO,QAAQ,EACxB,UAAA,EACA,eAAe,cAAc,CAAC;AACjC,aAAK,MAAM,gBAAgB,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC;AAGhE,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AAGM,0CAAiB,MAAY;AAC9B,WAAA,cAAc,MAAM,MAAM,KAAK;AAEpC,WAAK,cAAc,SAAS;AAAA,IAAA;AAStB;AAAA;AAAA;AAAA;AAAA;AAAA,qCAAY,CAAC,SAAiB,YAA0B;AAC9D,cAAQ,IAAI;AACZ,cAAQ,IAAI;AAAA,IAAA;AAMN;AAAA;AAAA;AAAA,kDAAyB,MAAY;AACtC,WAAA,eAAe,OAAO,GAAG,MAAM;AAC/B,WAAA,eAAe,OAAO,CAAC;AAEvB,WAAA,eAAe,UAAU,CAAC;AAE1B,WAAA,eAAe,QAAQ,OAAO;AAC9B,WAAA,eAAe,QAAQ,CAAC;AAExB,WAAA,eAAe,OAAO,SAAS,OAAO;AACtC,WAAA,eAAe,OAAO,GAAG,OAAO;AAAA,IAAA;AAU/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,WAAsB,OAAwB,MAA0B,SAAkB;AAClH,YAAM,iBAAiB,CAAC,OAAO,UAAU,QAAQ,KAAK;AACtD,YAAM,aAAa,CAAC,GAAG,GAAG,GAAG,OAAO;AACpC,YAAM,WAAW,CAAC,QAAQ,SAAS,IAAI;AACnC,UAAA;AAEJ,UAAI,CAAC,eAAe,SAAS,SAAS,KAAK,CAAC,WAAW,SAAS,KAAK,KAAK,CAAC,SAAS,SAAS,GAAG,GAAG;AAE1F,eAAA;AAAA,MACT;AAEA,UAAI,SAAS,SAAS;AAChB,YAAA,aAAa,UAAU,aAAa,OAAO;AAEtC,iBAAA;AAAA,QACT;AAAA,MACF;AAEA,cAAQ,WAAW;AAAA,QACjB,KAAK;AACH,kBAAQ,MAAM;AACd;AAAA,QAEF,KAAK;AACH,kBAAQ,MAAM;AACd;AAAA,QAEF,KAAK;AACH,kBAAQ,MAAM;AACd;AAAA,QAEF,KAAK;AACH,kBAAQ,MAAM;AACd;AAAA,MACJ;AAEA,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAGF,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,YAAI,KAAK,aAAa,CAAC,EAAE,SAAS,OAAO,SAAS,KAAK,aAAa,CAAC,EAAE,OAAO,OAAO,KAAK;AACxF,eAAK,aAAa,OAAO,GAAG,GAAG,MAAM;AAC9B,iBAAA;AAAA,QACT;AAAA,MACF;AAEK,WAAA,aAAa,KAAK,MAAM;AACtB,aAAA;AAAA,IAAA;AASD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAAkB,CAAC,OAAwB,QAA8C;AAC3F,UAAA;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACxC,iBAAA,KAAK,aAAa,CAAC;AAC5B,YAAI,OAAO,SAAS,SAAS,OAAO,OAAO,KAAK;AAC9C,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAEA,UAAI,KAAK;AACP,iBAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACxC,mBAAA,KAAK,aAAa,CAAC;AAC5B,cAAI,OAAO,SAAS,SAAS,OAAO,OAAO,MAAM;AAC/C,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AASD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gDAAuB,CAAC,OAAwB,QAA2C;AAC7F,UAAA;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACxC,iBAAA,KAAK,aAAa,CAAC;AAC5B,YAAI,OAAO,SAAS,SAAS,OAAO,OAAO,KAAK;AAC9C,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAEA,UAAI,KAAK;AACP,iBAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACxC,mBAAA,KAAK,aAAa,CAAC;AAC5B,cAAI,OAAO,SAAS,SAAS,OAAO,OAAO,MAAM;AAC/C,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AASD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAW,CAAC,IAAkB,OAA6B;AACjE,aAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,OAAO,IAAI,MAAO,KAAK;AAAA,IAAA;AAO7E;AAAA;AAAA;AAAA;AAAA,4CAAmB,CAAC,UAA8B;AACxD,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,QAAQ,KAAK;AAClD,YAAI,KAAK,cAAc,CAAC,EAAE,aAAa,MAAM,WAAW;AACtD,eAAK,cAAc,OAAO,GAAG,GAAG,KAAK;AACrC;AAAA,QACF;AAAA,MACF;AAAA,IAAA;AAkFM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDAAwB,CAAC,IAAY,IAAY,IAAY,OAAuB;AAC1F,YAAM,IAAI,KAAK;AACT,YAAA,KAAK,KAAK,MAAM;AACtB,UAAI,KAAK,GAAG;AACH,eAAA;AAAA,MACT;AAEA,aAAO,IAAI;AAAA,IAAA;AASL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qDAA4B,CAAC,IAAkB,OAA6B;AAClF,aAAO,KAAK,KAAK,KAAK,IAAI,GAAG,UAAU,GAAG,SAAS,CAAC,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG,SAAS,CAAC,CAAC;AAAA,IAAA;AAStF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iDAAwB,CAAC,MAAe,SAA2B;AACpE,WAAA,gBAAgB,gBAAgB,KAAK,kBAAkB;AACvD,WAAA,MAAM,sBAAsB,KAAK,eAAe;AAErD,WAAK,cAAc,aAAa,MAAM,IAAI,EAAE,gBAAgB,KAAK,KAAK;AACtE,aAAO,KAAK,cAAc,UAAU,EAAE,MAAM;AAAA,IAAA;AAQtC;AAAA;AAAA;AAAA;AAAA;AAAA,6CAAoB,CAAC,WAAuC;AAClE,YAAM,SAAS;AACf,YAAM,WAAW,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AAEjE,UAAI,kBAAkB,mBAAmB;AACvC,cAAM,WAAW,UAAU,UAAU,OAAO,MAAM;AAC5C,cAAA,WAAW,KAAK,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AACtD,eAAA,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ,CAAC,IAAI,WAAW;AAAA,MAAA,WAClD,kBAAkB,oBAAoB;AAC/C,eAAO,KAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI;AAAA,MAC9C;AAAA,IAAA;AASM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAAQ,CAAC,OAAgB,MAAc,SAAS,MAAY;AAClE,UAAI,KAAK,QAAQ;AACT,cAAA,aAAa,MAAM;AAGzB,mBAAW,IAAI,KAAK,QAAQ,QAAQ,EAAE,eAAe,MAAM;AAC3D,aAAK,mBAAmB,gBAAgB,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;AAE1E,cAAA,iBAAiB,KAAK,kBAAkB,MAAM;AAC/C,aAAA,kBAAkB,YAAY,KAAK,kBAAkB;AACrD,aAAA,kBAAkB,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AAE7F,cAAA,kBAAkB,KAAK,mBAAmB,MAAM;AACjD,aAAA,mBAAmB,YAAY,KAAK,kBAAkB;AACtD,aAAA,mBAAmB,UAAU,KAAK,OAAO,UAAU,KAAK,OAAO,YAAY,KAAK,OAAO,KAAK;AAGjG,YAAI,KAAK,YAAY;AACnB,eAAK,qBAAqB,KAAK,WAAW,MAAM,KAAK,QAAQ,QAAQ,CAAC;AAAA,QACxE;AAEK,aAAA,kBAAkB,KAAK,cAAc;AACrC,aAAA,mBAAmB,KAAK,eAAe;AAAA,MAC9C;AAAA,IAAA;AAMM;AAAA;AAAA;AAAA,oCAAW,MAAY;AAC7B,UAAI,KAAK,OAAO;AACd,cAAM,QAAQ;AACd,cAAM,aAAa;AACf,YAAA,MAAM,WAAW,WAAW;AAE5B,YAAA,KAAK,kBAAkB,oBAAoB;AAC7C,gBAAM,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO;AAC9C,gBAAM,SAAS,KAAK,OAAO,SAAS,KAAK,OAAO;AAEpC,sBAAA,KAAK,IAAI,OAAO,MAAM;AAClC,iBAAO,YAAY;AAEX,iBAAA,YAAY,KAAK,OAAO,OAAQ;AAC3B,sBAAA,OAAO,OAAQ,KAAK,OAAO;AAAA,QAAA,WAC/B,KAAK,kBAAkB,mBAAmB;AACnD,gBAAM,WAAW,KAAK,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AACtE,gBAAM,WAAW,UAAU,UAAU,KAAK,OAAO,MAAM;AACjD,gBAAA,WAAW,KAAK,KAAK,KAAK,OAAO,SAAS,KAAK,IAAI,QAAQ,CAAC;AAEtD,sBAAA,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ,CAAC,IAAI,WAAW;AAChE,iBAAO,YAAY;AAEnB,iBAAO,YAAY;AACnB,sBAAY,OAAO;AAAA,QACrB;AAEA,YAAI,KAAK,SAAS,QAAQ,KAAK,QAAQ;AACrC,eAAK,QAAQ,IAAI,WAAW,MAAM,WAAW,OAAO,KAAK;AACzD,eAAK,MAAM,SAAS,KAAK,KAAK,QAAQ,QAAQ;AAC9C,eAAK,cAAc,KAAK,KAAK,MAAM,QAAQ;AAC3C,eAAK,MAAM,WAAW,KAAK,KAAK,OAAO,UAAU;AACjD,eAAK,MAAM,QAAQ,KAAK,KAAK,GAAG;AAE3B,eAAA,MAAM,IAAI,KAAK,KAAK;AAAA,QAC3B;AAAA,MACF;AAAA,IAAA;AAGK,mCAAU,CAAC,eAAkC;AAGlD,UAAK,eAAuB,UAAU;AAC5B,gBAAA;AAAA,UACN;AAAA,QAAA;AAAA,MAEJ;AACA,WAAK,aAAa;AAIb,WAAA,WAAW,MAAM,cAAc;AACpC,WAAK,WAAW,iBAAiB,eAAe,KAAK,aAAa;AAClE,WAAK,WAAW,iBAAiB,eAAe,KAAK,aAAa;AAClE,WAAK,WAAW,iBAAiB,iBAAiB,KAAK,eAAe;AACtE,WAAK,WAAW,iBAAiB,SAAS,KAAK,OAAO;AAAA,IAAA;AAMjD;AAAA;AAAA;AAAA,mCAAU,MAAY;;AACvB,UAAA,KAAK,gBAAgB,IAAI;AACpB,eAAA,qBAAqB,KAAK,YAAY;AAAA,MAC/C;AAEA,iBAAK,eAAL,mBAAiB,oBAAoB,eAAe,KAAK;AACzD,iBAAK,eAAL,mBAAiB,oBAAoB,iBAAiB,KAAK;AAC3D,iBAAK,eAAL,mBAAiB,oBAAoB,SAAS,KAAK;AACnD,iBAAK,eAAL,mBAAiB,oBAAoB,eAAe,KAAK;AAElD,aAAA,oBAAoB,eAAe,KAAK,aAAa;AACrD,aAAA,oBAAoB,aAAa,KAAK,WAAW;AAEjD,aAAA,oBAAoB,UAAU,KAAK,cAAc;AAEnD,iBAAA,UAAA,mBAAO,OAAO,KAAK;AACxB,WAAK,YAAY;AAAA,IAAA;AAMX;AAAA;AAAA;AAAA,uCAAc,MAAY;AAC5B,UAAA,KAAK,SAAS,KAAK,OAAO;AACvB,aAAA,MAAM,OAAO,KAAK,KAAK;AAC5B,aAAK,QAAQ;AAAA,MACf;AAAA,IAAA;AAQM;AAAA;AAAA;AAAA;AAAA;AAAA,wCAAe,CAAC,MAAsB;AAC5C,aAAO,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAA,IAAA;AAOtB;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,aAA4B;AACzC,iBAAA,SAAS,KAAK,QAAQ,UAAU;AACvC,cAAyC,SAAS,UAAU,EAAE,SAAS,WAAW,IAAI,KAAK;AAAA,MAC/F;AAAA,IAAA;AAUM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wCAAe,CAAC,SAAiB,SAAiB,WAAiC;AACnF,YAAA,aAAa,OAAO;AACrB,WAAA,MAAM,MAAO,UAAU,WAAW,QAAQ,WAAW,QAAS,IAAI,CAAC;AACnE,WAAA,MAAM,MAAO,WAAW,SAAS,WAAW,WAAW,SAAU,IAAI,CAAC;AACpE,aAAA,KAAK,MAAM;IAAM;AAUlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6CAAoB,CAAC,SAAiB,SAAiB,WAAiC;AAC9F,WAAK,MAAM,KAAK,KAAK,aAAa,SAAS,SAAS,MAAM,CAAC;AACvD,UAAA,KAAK,kBAAkB,oBAAoB;AAC7C,aAAK,MAAM,MAAM,KAAK,OAAO,QAAQ,KAAK,OAAO,QAAQ;AACzD,aAAK,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,UAAU;AAAA,MAC3D;AACO,aAAA,KAAK,MAAM;IAAM;AAOlB;AAAA;AAAA;AAAA;AAAA,qCAAY,CAAC,WAAgC;AACnD,UAAI,QAAQ;AACH,eAAA,OAAO,KAAK,MAAM;AACzB,eAAO,aAAa;AAGpB,YAAI,kBAAkB,mBAAmB;AACvC,eAAK,QAAQ,OAAO;AACpB,eAAK,YAAY,OAAO;AAAA,QAC1B;AAEK,aAAA,oBAAoB,KAAK,OAAO,MAAM;AACtC,aAAA,mBAAmB,KAAK,KAAK,mBAAmB;AAChD,aAAA,uBAAuB,KAAK,OAAO,gBAAgB;AACxD,aAAK,SAAS,OAAO;AACrB,aAAK,aAAa,KAAK;AAEvB,aAAK,eAAe,OAAO;AAC3B,aAAK,YAAY,OAAO,SAAS,WAAW,KAAK,MAAM,IAAI,OAAO;AAClE,aAAK,WAAW,KAAK;AAErB,aAAK,cAAc,OAAO;AAC1B,aAAK,WAAW,OAAO,SAAS,WAAW,KAAK,MAAM,IAAI,OAAO;AACjE,aAAK,UAAU,KAAK;AAEf,aAAA,KAAK,KAAK,OAAO,EAAE;AACnB,aAAA,SAAS,KAAK,OAAO,EAAE;AAE5B,aAAK,SAAS;AAEd,aAAK,OAAO;AAGN,cAAA,WAAW,KAAK,kBAAkB,MAAM;AAC9C,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,aAAK,WAAW,KAAK,QAAQ,KAAK,SAAS;AAAA,MAC7C;AAAA,IAAA;AAkBM;AAAA;AAAA;AAAA;AAAA;AAAA,sCAAa,CAAC,UAAmB,aAA2B;AAElE,YAAM,QAAQ,IAAI,aAAa,GAAG,GAAG,UAAU,QAAQ;AACvD,YAAM,SAAS,MAAM,UAAU,KAAK,SAAS;AAG7C,YAAM,gBAAgB,IAAI,eAAe,EAAE,cAAc,MAAM;AAG/D,YAAM,iBAAiB,IAAI,kBAAkB,EAAE,OAAO,UAAU,KAAK,OAAO,aAAa,MAAM,SAAS,IAAK,CAAA;AAC7G,YAAM,iBAAiB,IAAI,kBAAkB,EAAE,OAAO,SAAU,KAAK,OAAO,aAAa,MAAM,SAAS,IAAK,CAAA;AAC7G,YAAM,iBAAiB,IAAI,kBAAkB,EAAE,OAAO,SAAU,KAAK,OAAO,aAAa,MAAM,SAAS,IAAK,CAAA;AAG7G,YAAM,SAAS,IAAI,KAAK,eAAe,cAAc;AACrD,YAAM,SAAS,IAAI,KAAK,eAAe,cAAc;AACrD,YAAM,SAAS,IAAI,KAAK,eAAe,cAAc;AAE/C,YAAA,WAAW,KAAK,KAAK;AAC3B,aAAO,SAAS,IAAI;AACpB,aAAO,SAAS,IAAI;AAGpB,WAAK,mBAAmB,SAAW,EAAA,YAAY,QAAQ;AAClD,WAAA,kBAAkB,KAAK,KAAK,kBAAkB;AAEnD,UAAI,KAAK,UAAU,KAAK,OAAO,QAAQ,GAAG;AAElC,cAAA,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAK,aAAa,UAAU,MAAM,MAAM,IAAI;AACvC,aAAA,mBAAmB,gBAAgB,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC;AAE7E,aAAK,kBAAkB,YAAY,KAAK,kBAAkB,EAAE,YAAY,KAAK,YAAY;AACzF,aAAK,mBAAmB,gBAAgB,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AACrE,aAAA,kBAAkB,YAAY,KAAK,kBAAkB;AAAA,MAC5D;AAEK,WAAA,kBAAkB,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AAEnG,WAAK,QAAQ;AAER,WAAA,QAAQ,IAAI,MAAM;AAClB,WAAA,QAAQ,IAAI,MAAM;AAClB,WAAA,QAAQ,IAAI,MAAM;AAAA,IAAA;AAUjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAAc,CAAC,MAAc,OAAgB,cAAuB,gBAA+B;AACrG,UAAA,KAAK,cAAc,IAAI;AAEzB,aAAK,aAAa;AAAA,MACpB;AAEI,UAAA,KAAK,UAAU,MAAM,iBAAiB;AAClC,cAAA,YAAY,OAAO,KAAK;AACxB,cAAA,WAAW,YAAY,KAAK;AAE7B,aAAA,kBAAkB,KAAK,WAAW;AAEvC,YAAI,YAAY,GAAG;AAGZ,eAAA,kBAAkB,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AAE9F,eAAA,MAAM,OAAO,KAAK,WAAW;AAElC,eAAK,aAAa;AACb,eAAA,cAAc,MAAM,MAAM,KAAK;AACpC,eAAK,eAAe,KAAK;AAGzB,eAAK,cAAc,YAAY;AAAA,QAAA,OAC1B;AACC,gBAAA,SAAS,KAAK,aAAa,QAAQ;AACzC,gBAAM,OAAO,IAAI,SAAS,KAAK,cAAc;AAExC,eAAA,kBAAkB,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AAC9F,eAAA,MAAM,OAAO,MAAM,MAAM;AAG9B,eAAK,cAAc,YAAY;AAC/B,gBAAM,OAAO;AACb,eAAK,eAAe,OAAO,sBAAsB,SAAU,GAAG;AAC5D,iBAAK,YAAY,GAAG,OAAO,cAAc,YAAY,OAAO;AAAA,UAAA,CAC7D;AAAA,QACH;AAAA,MAAA,OACK;AAGL,aAAK,eAAe;AACpB,aAAK,aAAa;AAAA,MACpB;AAAA,IAAA;AASM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,MAAc,cAAuB,OAAqB;AAC9E,UAAA,KAAK,cAAc,IAAI;AAEzB,aAAK,aAAa;AAClB,aAAK,gBAAgB;AACrB,aAAK,aAAa;AAAA,MACpB;AAEI,UAAA,KAAK,UAAU,MAAM,kBAAkB;AAEnC,cAAA,aAAa,OAAO,KAAK,cAAc;AAC7C,cAAM,IAAI,KAAK,CAAC,KAAK,gBAAgB;AAErC,YAAI,IAAI,GAAG;AAEJ,eAAA,gBAAgB,MAAM,CAAC,KAAK,gBAAgB,KAAK,IAAI,WAAW,CAAC,IAAI,KAAK,YAAY;AAC3F,eAAK,qBAAqB,KAAK,OAAO,cAAc,KAAK,aAAa,CAAC;AAEvE,eAAK,cAAc,YAAY;AAC/B,gBAAM,OAAO;AACb,eAAK,eAAe,OAAO,sBAAsB,SAAU,GAAG;AACvD,iBAAA,eAAe,GAAG,cAAc,EAAE;AAAA,UAAA,CACxC;AAAA,QAAA,OACI;AACL,eAAK,eAAe;AACpB,eAAK,aAAa;AAEb,eAAA,cAAc,MAAM,MAAM,KAAK;AACpC,eAAK,eAAe,KAAK;AAGzB,eAAK,cAAc,YAAY;AAAA,QACjC;AAAA,MAAA,OACK;AAGL,aAAK,eAAe;AACpB,aAAK,aAAa;AAEd,YAAA,KAAK,UAAU,MAAM,QAAQ;AAC/B,eAAK,eAAe,KAAK;AAEzB,eAAK,cAAc,YAAY;AAAA,QACjC;AAAA,MACF;AAAA,IAAA;AASM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAAM,CAAC,IAAa,IAAa,SAAS,UAA0B;AAC1E,UAAI,KAAK,QAAQ;AACf,cAAM,WAAW,GAAG,MAAM,EAAE,IAAI,EAAE;AAE9B,YAAA,KAAK,kBAAkB,oBAAoB;AAE7C,mBAAS,eAAe,IAAI,KAAK,OAAO,IAAI;AAAA,QAC9C;AAEI,YAAA,KAAK,kBAAkB,qBAAqB,QAAQ;AAEjD,eAAA,MAAM,sBAAsB,KAAK,mBAAmB;AACpD,eAAA,MAAM,sBAAsB,KAAK,kBAAkB;AACxD,gBAAM,iBACJ,KAAK,MAAM,WAAW,KAAK,KAAK,IAAI,KAAK,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AAClF,mBAAA,eAAe,IAAI,cAAc;AAAA,QAC5C;AAEK,aAAA,MAAM,IAAI,SAAS,GAAG,SAAS,GAAG,CAAC,EAAE,gBAAgB,KAAK,OAAO,UAAU;AAE3E,aAAA,MAAM,gBAAgB,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC;AAEnE,aAAK,0BAA0B,KAAK,OAAO,KAAK,KAAK;AAAA,MACvD;AACO,aAAA;AAAA,IAAA;AAMF;AAAA;AAAA;AAAA,iCAAQ,MAAY;AACzB,UAAI,KAAK,QAAQ;AACV,aAAA,OAAO,OAAO,KAAK;AAEpB,YAAA,KAAK,kBAAkB,mBAAmB;AACvC,eAAA,OAAO,MAAM,KAAK;AAAA,QACzB;AAEK,aAAA,OAAO,OAAO,KAAK;AACnB,aAAA,OAAO,MAAM,KAAK;AAClB,aAAA,mBAAmB,KAAK,KAAK,mBAAmB;AAChD,aAAA,mBAAmB,UAAU,KAAK,OAAO,UAAU,KAAK,OAAO,YAAY,KAAK,OAAO,KAAK;AACjG,aAAK,OAAO,GAAG,KAAK,KAAK,IAAI;AAE7B,aAAK,OAAO;AACZ,aAAK,OAAO;AAEP,aAAA,kBAAkB,KAAK,KAAK,kBAAkB;AAC9C,aAAA,mBAAmB,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AACpG,aAAK,QAAQ;AAEb,cAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,aAAK,WAAW,KAAK,QAAQ,UAAU,KAAK,SAAS;AAErD,aAAK,OAAO,OAAO,KAAK,QAAQ,QAAQ;AAEnC,aAAA,cAAc,MAAM,MAAM,KAAK;AAGpC,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AASM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAAS,CAAC,MAAe,UAAkC;AAC3D,YAAA,QAAQ,KAAK,QAAQ;AACtB,WAAA,mBAAmB,gBAAgB,CAAC,MAAM,GAAG,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC;AACpE,WAAK,gBAAgB,iBAAiB,MAAM,CAAC,KAAK;AAGlD,WAAK,MAAM,gBAAgB,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAC/C,WAAA,MAAM,SAAS,KAAK,eAAe;AACnC,WAAA,MAAM,SAAS,KAAK,kBAAkB;AAEtC,WAAA,0BAA0B,KAAK,KAAK;AAElC,aAAA;AAAA,IAAA;AAGF,qCAAY,MAAY;AAC7B,UAAI,KAAK,QAAQ;AACf,cAAM,QAAQ,KAAK;AAAA,UACjB,KAAK,kBAAkB,qBACnB;AAAA,YACE,cAAc;AAAA,cACZ,WAAW,KAAK,OAAO;AAAA,cACvB,cAAc,KAAK,OAAO;AAAA,cAC1B,YAAY,KAAK,OAAO;AAAA,cACxB,UAAU,KAAK,OAAO;AAAA,cACtB,YAAY,KAAK,OAAO;AAAA,cACxB,aAAa,KAAK,QAAQ;AAAA,YAC5B;AAAA,UAAA,IAEF;AAAA,YACE,cAAc;AAAA,cACZ,WAAW,KAAK,OAAO;AAAA,cACvB,WAAW,KAAK,OAAO;AAAA,cACvB,cAAc,KAAK,OAAO;AAAA,cAC1B,YAAY,KAAK,OAAO;AAAA,cACxB,UAAU,KAAK,OAAO;AAAA,cACtB,YAAY,KAAK,OAAO;AAAA,cACxB,aAAa,KAAK,QAAQ;AAAA,YAC5B;AAAA,UACF;AAAA,QAAA;AAGI,kBAAA,UAAU,UAAU,KAAK;AAAA,MACrC;AAAA,IAAA;AAGK,sCAAa,MAAY;AAC9B,YAAM,OAAO;AACb,gBAAU,UAAU,SAAS,EAAE,KAAK,SAAS,SAAS,OAAO;AAC3D,aAAK,iBAAiB,KAAK;AAAA,MAAA,CAC5B;AAAA,IAAA;AAMI;AAAA;AAAA;AAAA,qCAAY,MAAY;AAC7B,UAAI,CAAC,KAAK;AAAQ;AAElB,WAAK,oBAAoB,KAAK,KAAK,OAAO,MAAM;AAChD,WAAK,mBAAmB,KAAK,KAAK,QAAQ,MAAM;AAC3C,WAAA,WAAW,KAAK,OAAO;AACvB,WAAA,UAAU,KAAK,OAAO;AACtB,WAAA,SAAS,KAAK,OAAO;AAC1B,WAAK,KAAK,KAAK,KAAK,OAAO,EAAE;AAEzB,UAAA,KAAK,kBAAkB,mBAAmB;AACvC,aAAA,QAAQ,KAAK,OAAO;AAAA,MAC3B;AAAA,IAAA;AAUM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCAAa,CAAC,MAAc,OAAgB,cAAc,SAAqC;AACrG,UAAI,CAAC,KAAK;AAAQ;AAEZ,YAAA,aAAa,MAAM;AACzB,UAAI,cAAc,IAAI;AAElB,UAAA,KAAK,kBAAkB,oBAAoB;AAExC,aAAA,OAAO,OAAO,KAAK;AACxB,aAAK,OAAO,QAAQ;AAGpB,YAAI,KAAK,OAAO,OAAO,KAAK,SAAS;AAC9B,eAAA,OAAO,OAAO,KAAK;AACV,wBAAA,KAAK,aAAa,KAAK;AAAA,QAC5B,WAAA,KAAK,OAAO,OAAO,KAAK,SAAS;AACrC,eAAA,OAAO,OAAO,KAAK;AACV,wBAAA,KAAK,aAAa,KAAK;AAAA,QACvC;AAEA,aAAK,OAAO;AAEP,aAAA,MAAM,sBAAsB,KAAK,iBAAiB;AAGvD,aAAK,aAAa,UAAU,aAAa,aAAa,WAAW;AACjE,aAAK,mBAAmB,gBAAgB,CAAC,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM,CAAC;AAEnF,aAAK,MAAM,gBAAgB,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,EAAE,SAAS,KAAK,YAAY;AAC1F,aAAA,MAAM,SAAS,KAAK,kBAAkB;AAGhC,mBAAA,IAAI,KAAK,KAAK;AAEzB,cAAM,SAAS,WAAW,MAAM,EAAE,eAAe,WAAW;AAC5D,mBAAW,IAAI,MAAM;AAErB,aAAK,MAAM,gBAAgB,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;AAC9D,aAAA,MAAM,YAAY,KAAK,KAAK;AAEjC,aAAK,0BAA0B,KAAK,OAAO,KAAK,KAAK;AAC9C,eAAA;AAAA,MACT;AAEI,UAAA,KAAK,kBAAkB,mBAAmB;AACvC,aAAA,MAAM,sBAAsB,KAAK,kBAAkB;AACnD,aAAA,MAAM,sBAAsB,KAAK,iBAAiB;AAGvD,YAAI,WAAW,KAAK,MAAM,WAAW,UAAU;AAC3C,YAAA,SAAS,WAAW,WAAW;AAGnC,cAAM,cAAc,WAAW;AAC3B,YAAA,cAAc,KAAK,aAAa;AAClC,wBAAc,KAAK,cAAc;AACjC,mBAAS,WAAW,WAAW;AAAA,QAAA,WACtB,cAAc,KAAK,aAAa;AACzC,wBAAc,KAAK,cAAc;AACjC,mBAAS,WAAW,WAAW;AAAA,QACjC;AAEI,YAAA,YAAY,WAAW,MAAA,EAAQ,IAAI,KAAK,KAAK,EAAE,UAAA,EAAY,eAAe,MAAM;AAEpF,aAAK,MAAM,gBAAgB,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC;AAEhE,YAAI,aAAa;AAEf,gBAAM,MAAM,KAAK;AAEN,qBAAA,IAAI,WAAW,UAAU;AACpC,mBAAS,WAAW,WAAW;AACnB,sBAAA,WAAW,MAAM,EAAE,IAAI,KAAK,KAAK,EAAE,UAAA,EAAY,eAAe,MAAM;AAEhF,eAAK,mBAAmB,gBAAgB,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC3D,eAAK,aAAa,UAAU,aAAa,aAAa,WAAW;AAE5D,eAAA,MAAM,gBAAgB,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC,EAAE,SAAS,KAAK,kBAAkB;AAC7F,eAAA,MAAM,SAAS,KAAK,YAAY;AAEhC,eAAA,mBAAmB,gBAAgB,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;AAEzD,eAAA,MAAM,SAAS,KAAK,kBAAkB;AAC3C,eAAK,0BAA0B,KAAK,OAAO,KAAK,KAAK;AAAA,QAAA,OAChD;AACA,eAAA,0BAA0B,KAAK,KAAK;AAAA,QAC3C;AAEO,eAAA;AAAA,MACT;AAAA,IAAA;AAOM;AAAA;AAAA;AAAA;AAAA,kCAAS,CAAC,UAAwB;AACpC,UAAA,KAAK,kBAAkB,mBAAmB;AACvC,aAAA,OAAO,MAAM,UAAU,MAAM,OAAO,KAAK,QAAQ,KAAK,MAAM;AACjE,aAAK,OAAO;MACd;AAAA,IAAA;AASK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qCAAY,CAAC,GAAW,GAAW,MAAoB;AAC5D,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,IAAI,GAAG,GAAG,CAAC;AACvB,aAAK,QAAQ,SAAS,IAAI,GAAG,GAAG,CAAC;AACjC,cAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,aAAK,WAAW,KAAK,QAAQ,KAAK,SAAS;AACtC,aAAA,OAAO,OAAO,KAAK,MAAM;AAAA,MAChC;AAAA,IAAA;AAoCM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCAAU,CAAC,OAAgB,UAAkC;AACnE,WAAK,gBAAgB,iBAAiB,KAAK,eAAe,KAAK;AAC1D,WAAA,mBAAmB,gBAAgB,CAAC,MAAM,GAAG,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC;AAEpE,WAAK,MAAM,gBAAgB,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAC/C,WAAA,MAAM,SAAS,KAAK,eAAe;AACnC,WAAA,MAAM,SAAS,KAAK,kBAAkB;AAE3C,WAAK,MAAM,sBAAsB,KAAK,iBAAiB,EAAE,IAAI,KAAK;AAC7D,WAAA,MAAM,KAAK,KAAK,KAAK,EAAE,eAAe,KAAK,eAAe,KAAK;AAC/D,WAAA,MAAM,IAAI,KAAK,KAAK;AAEpB,WAAA,MAAM,gBAAgB,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC;AAEnE,WAAK,0BAA0B,KAAK,OAAO,KAAK,KAAK;AAC9C,aAAA;AAAA,IAAA;AASD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAiB,CAAC,QAAiB,WAAmC;AAC5E,UAAI,CAAC,KAAK;AAAc,eAAA;AAElB,YAAA,YAAY,IAAI;AACtB,gBAAU,OAAO,OAAO;AACxB,gBAAU,MAAM,OAAO;AACb,gBAAA,cAAc,QAAQ,MAAM;AAEtC,YAAM,YAAY,UAAU,iBAAiB,KAAK,MAAM,UAAU,IAAI;AACtE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,YAAA,UAAU,CAAC,EAAE,OAAO,QAAQ,KAAK,QAAQ,QAAQ,UAAU,CAAC,EAAE,MAAM;AACtE,iBAAO,UAAU,CAAC,EAAE,MAAM,MAAM;AAAA,QAClC;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AAYD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gDAAuB,CAC7B,QACA,SACA,SACA,QACA,aACwB;AACxB,UAAI,kBAAkB,oBAAoB;AACxC,aAAK,MAAM,KAAK,KAAK,kBAAkB,SAAS,SAAS,MAAM,CAAC;AAC3D,aAAA,MAAM,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,CAAC;AAE5C,cAAM,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC;AACnC,cAAM,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC;AACnC,cAAM,KAAK,KAAK,IAAI,KAAK,WAAW,CAAC;AAEjC,YAAA,KAAK,MAAM,KAAK,KAAK;AAEvB,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC;AAAA,QAAA,OACpC;AAEA,eAAA,MAAM,KAAM,KAAK,MAAO,KAAK,KAAK,KAAK,EAAE,CAAC;AAAA,QACjD;AAEA,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,kBAAkB,mBAAmB;AAEvC,aAAK,MAAM,KAAK,KAAK,aAAa,SAAS,SAAS,MAAM,CAAC;AAEtD,aAAA,MAAM,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE;AACxC,aAAA,MAAM,aAAa,OAAO,uBAAuB;AAEtD,cAAM,SAAS,KAAK,MAAM,QAAQ,UAAU;AAC5C,cAAM,sBAAsB,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AAC5E,cAAM,UAAU,KAAK,IAAI,UAAU,CAAC;AAY9B,cAAA,IAAI,KAAK,MAAM;AACrB,cAAM,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC;AAEzE,YAAI,KAAK,GAAG;AAEV,iBAAO,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,QAAQ;AACxC,iBAAA;AAAA,QACT;AAEA,cAAM,IAAI,IAAI;AACd,cAAM,IAAI;AASV,YAAI,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AACrB,YAAA,IAAI,IAAI,IAAI;AAChB,YAAI,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AACzB,YAAI,QAAQ,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;AAErC,YAAI,SAAS,GAAG;AAET,eAAA,MAAM,MAAM,CAAC,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI,EAAE;AACjD,eAAK,MAAM,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC;AAEpC,gBAAM,QAAQ,UAAU,UAAU,KAAK,MAAM;AAE7C,cAAI,SAAS,IAAI;AAIf,kBAAMA,aAAY,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,sBAAsB,KAAK,MAAM,GAAG,CAAC,CAAC;AACvG,mBAAO,eAAeA,UAAS;AAC/B,mBAAO,KAAK;AACL,mBAAA;AAAA,UACT;AAAA,QACF;AAUI,YAAA;AACA,YAAA;AACJ,YAAI,CAAC,UAAU;AACf,gBAAQ,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;AAC5B,aAAA,MAAM,MAAM,CAAC,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI,EAAE;AACjD,aAAK,MAAM,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC;AAEpC,cAAM,YAAY,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,sBAAsB,KAAK,MAAM,GAAG,CAAC,CAAC;AAEvG,eAAO,eAAe,SAAS;AAC/B,eAAO,KAAK;AACL,eAAA;AAAA,MACT;AAAA,IAAA;AAYM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8CAAqB,CAC3B,QACA,SACA,SACA,QACA,kBAAkB,UACM;AACxB,UAAI,kBAAkB,oBAAoB;AACxC,aAAK,MAAM,KAAK,KAAK,kBAAkB,SAAS,SAAS,MAAM,CAAC;AAC3D,aAAA,MAAM,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,CAAC;AAErC,eAAA,KAAK,MAAM;MACpB;AAEA,UAAI,kBAAkB,mBAAmB;AACvC,aAAK,MAAM,KAAK,KAAK,aAAa,SAAS,SAAS,MAAM,CAAC;AAGtD,aAAA,MAAM,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE;AACxC,aAAA,MAAM,aAAa,OAAO,uBAAuB;AAEtD,cAAM,SAAS,KAAK,MAAM,QAAQ,UAAU;AAYtC,cAAA,IAAI,KAAK,MAAM;AACrB,cAAM,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC;AACrE,YAAA;AAEJ,YAAI,iBAAiB;AACnB,gCAAsB,KAAK,MACxB,sBAAsB,KAAK,mBAAmB,EAC9C,WAAW,KAAK,MAAM,sBAAsB,KAAK,kBAAkB,CAAC;AAAA,QAAA,OAClE;AACL,gCAAsB,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AAAA,QACxE;AASA,YAAI,KAAK,GAAG;AAEH,iBAAA,IAAI,GAAG,GAAG,CAAC;AACX,iBAAA;AAAA,QACT;AAEA,cAAM,IAAI,IAAI;AACd,cAAM,IAAI;AACJ,cAAA,IAAI,CAAC,IAAI;AAEf,cAAM,YAAY,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAC3D,eAAO,eAAe,SAAS;AAC/B,eAAO,IAAI;AACJ,eAAA;AAAA,MACT;AAAA,IAAA;AAMM;AAAA;AAAA;AAAA,6CAAoB,MAAY;AACtC,UAAI,CAAC,KAAK;AAAQ;AAGlB,WAAK,mBAAmB,KAAK,KAAK,OAAO,MAAM;AAC/C,WAAK,kBAAkB,KAAK,KAAK,QAAQ,MAAM;AAE3C,UAAA,KAAK,kBAAkB,oBAAoB;AAC7C,aAAK,uBAAuB,KAAK,KAAK,OAAO,gBAAgB;AAC7D,aAAK,OAAO;AACP,aAAA,aAAa,KAAK,OAAO;AAAA,MAChC;AAEI,UAAA,KAAK,kBAAkB,mBAAmB;AACvC,aAAA,YAAY,KAAK,OAAO;AAAA,MAC/B;AAAA,IAAA;AAQM;AAAA;AAAA;AAAA;AAAA;AAAA,yCAAgB,CAAC,UAAkB,mBAAkC;AAC3E,WAAK,SAAS;AACd,UAAI,gBAAgB;AAClB,aAAK,kBAAkB;AAAA,MACzB;AAAA,IAAA;AAGK,kCAAS,MAAY;AAC1B,YAAM,MAAM;AAGR,UAAA,CAAC,KAAK,OAAO,OAAO,KAAK,cAAc,KAAK,KAAK,QAAQ;AACtD,aAAA,QAAQ,SAAS,IAAI,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC;AACrE,cAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,aAAK,WAAW,KAAK,QAAQ,KAAK,SAAS;AACtC,aAAA,eAAe,KAAK,KAAK,MAAM;AAAA,MACtC;AAEA,UAAI,CAAC,KAAK;AAAQ;AAGd,UAAA,KAAK,kBAAkB,oBAAoB;AAEzC,YAAA,KAAK,OAAO,OAAO,KAAK,WAAW,KAAK,OAAO,OAAO,KAAK,SAAS;AAChE,gBAAA,UAAU,UAAU,MAAM,KAAK,OAAO,MAAM,KAAK,SAAS,KAAK,OAAO;AACvE,eAAA,qBAAqB,KAAK,WAAW,UAAU,KAAK,OAAO,MAAM,KAAK,QAAQ,UAAU,IAAI,CAAC;AAAA,QACpG;AAAA,MACF;AAEI,UAAA,KAAK,kBAAkB,mBAAmB;AAE5C,cAAM,WAAW,KAAK,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AAEtE,YAAI,WAAW,KAAK,cAAc,OAAO,WAAW,KAAK,cAAc,KAAK;AAC1E,gBAAM,cAAc,UAAU,MAAM,UAAU,KAAK,aAAa,KAAK,WAAW;AAC3E,eAAA,qBAAqB,KAAK,WAAW,cAAc,UAAU,KAAK,QAAQ,QAAQ,CAAC;AACxF,eAAK,kBAAkB;AAAA,QACzB;AAGI,YAAA,KAAK,OAAO,MAAM,KAAK,UAAU,KAAK,OAAO,MAAM,KAAK,QAAQ;AAC7D,eAAA,OAAO,MAAM,UAAU,MAAM,KAAK,OAAO,KAAK,KAAK,QAAQ,KAAK,MAAM;AAC3E,eAAK,OAAO;QACd;AAEA,cAAM,YAAY,KAAK;AACvB,cAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AAEA,YAAI,YAAY,KAAK,YAAY,OAAO,YAAY,KAAK,YAAY,KAAK;AACxE,gBAAM,SAAS,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,KAAK;AAC/E,gBAAA,YAAY,KAAK,YAAY;AAEnC,gBAAM,QAAQ,IAAI,aAAa,GAAG,GAAG,WAAW,SAAS;AACzD,gBAAM,SAAS,MAAM,UAAU,KAAK,SAAS;AAC7C,gBAAM,gBAAgB,IAAI,eAAe,EAAE,cAAc,MAAM;AAEpD,qBAAA,SAAS,KAAK,QAAQ,UAAU;AACzC,kBAAM,QAAQ,KAAK,QAAQ,SAAS,KAAK;AACzC,kBAAM,WAAW;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAEA,WAAK,OAAO,OAAO,KAAK,QAAQ,QAAQ;AAAA,IAAA;AAGlC,4CAAmB,CAAC,SAAuB;AAC3C,YAAA,QAAQ,KAAK,MAAM,IAAI;AAEzB,UAAA,MAAM,gBAAgB,KAAK,QAAQ;AACrC,aAAK,mBAAmB,UAAU,MAAM,aAAa,aAAa,QAAQ;AACrE,aAAA,mBAAmB,UAAU,KAAK,OAAO,UAAU,KAAK,OAAO,YAAY,KAAK,OAAO,KAAK;AAEjG,aAAK,OAAO,GAAG,KAAK,MAAM,aAAa,QAAQ;AAC1C,aAAA,OAAO,OAAO,MAAM,aAAa;AACjC,aAAA,OAAO,MAAM,MAAM,aAAa;AAEhC,aAAA,OAAO,OAAO,MAAM,aAAa;AAElC,YAAA,KAAK,kBAAkB,mBAAmB;AACvC,eAAA,OAAO,MAAM,MAAM,aAAa;AAAA,QACvC;AAEA,aAAK,kBAAkB,UAAU,MAAM,aAAa,YAAY,QAAQ;AACnE,aAAA,kBAAkB,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AAEnG,aAAK,OAAO;AACZ,aAAK,OAAO;AAEZ,aAAK,QAAQ;AAEb,cAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,YAAI,aAAa,QAAW;AAC1B,eAAK,YAAY;AAAA,QACnB;AACA,cAAM,WAAW,IAAI,QAAA,EAAU,KAAK,KAAK,kBAAkB;AAC3D,aAAK,WAAW,KAAK,QAAQ,UAAU,KAAK,SAAS;AAChD,aAAA,mBAAmB,KAAK,QAAQ;AAErC,aAAK,OAAO,OAAO,KAAK,QAAQ,QAAQ;AACnC,aAAA,cAAc,MAAM,MAAM,KAAK;AAGpC,aAAK,cAAc,YAAY;AAAA,MACjC;AAAA,IAAA;AA5jFA,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,QAAQ;AAEb,SAAK,eAAe;AACpB,SAAK,WAAW;AAGX,SAAA,QAAQ,IAAI;AACZ,SAAA,QAAQ,IAAI;AACZ,SAAA,QAAQ,IAAI;AAEZ,SAAA,QAAQ,IAAI;AACZ,SAAA,QAAQ,IAAI;AAEZ,SAAA,QAAQ,IAAI;AAGZ,SAAA,qBAAqB,IAAI;AACzB,SAAA,kBAAkB,IAAI;AACtB,SAAA,eAAe,IAAI;AAEnB,SAAA,gBAAgB,IAAI;AAGpB,SAAA,qBAAqB,IAAI;AACzB,SAAA,yBAAyB,IAAI;AAElC,SAAK,YAAY;AACZ,SAAA,WAAW,IAAI;AACpB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,UAAU;AAEV,SAAA,oBAAoB,IAAI;AAGxB,SAAA,OAAO,IAAI;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,WAAW;AACX,SAAA,sBAAsB,IAAI;AAC1B,SAAA,qBAAqB,IAAI;AAG9B,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,SAAS,MAAM;AAGpB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB;AAG9B,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAGrB,SAAA,yBAAyB,IAAI;AAC7B,SAAA,uBAAuB,IAAI;AAGhC,SAAK,QAAQ;AACR,SAAA,gBAAgB,IAAI;AAGpB,SAAA,UAAU,IAAI;AACnB,SAAK,YAAY;AAGjB,SAAK,aAAa;AAClB,SAAK,eAAe;AAGpB,SAAK,qBAAqB;AAG1B,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AAChB,SAAA,iBAAiB,IAAI;AACrB,SAAA,iBAAiB,IAAI;AAC1B,SAAK,SAAS;AACd,SAAK,SAAS;AAGd,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,SAAS;AAEd,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa;AAElB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,UAAU;AAGf,SAAK,SAAS,IAAI,QAAQ,GAAG,GAAG,CAAC;AACjC,SAAK,iBAAiB,IAAI,QAAQ,GAAG,GAAG,CAAC;AAEzC,SAAK,YAAY;AAGjB,SAAK,SAAS,MAAM;AAEpB,SAAK,UAAU,MAAM;AAErB,QAAI,KAAK,OAAO;AACT,WAAA,MAAM,IAAI,KAAK,OAAO;AAAA,IAC7B;AAEA,SAAK,cAAc,OAAO;AAE1B,SAAK,uBAAuB;AAE5B,QAAI,KAAK;AAAiB,WAAA,QAAQ,KAAK,UAAU;AAE1C,WAAA,iBAAiB,UAAU,KAAK,cAAc;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EA2vCQ,qBAAqB,gBAAkD;AACzE,SAAA,iDAAgB,WAAU,KAAK,QAAQ;AACzC,WAAK,MAAM,KAAK,KAAK,kBAAkB,EAAE,YAAY,eAAe,MAAM;AACrE,WAAA,MAAM,UAAU,KAAK,OAAO,UAAU,KAAK,OAAO,YAAY,KAAK,OAAO,KAAK;AACpF,WAAK,OAAO;AAGR,UAAA,KAAK,UAAU,MAAM,UAAU,KAAK,UAAU,MAAM,WAAW,KAAK,UAAU,MAAM,kBAAkB;AACnG,aAAA,OAAO,GAAG,KAAK,KAAK,QAAQ,EAAE,gBAAgB,KAAK,OAAO,UAAU;AAAA,MAC3E;AAAA,IACF;AAEA,QAAI,iDAAgB,QAAQ;AAC1B,WAAK,MAAM,KAAK,KAAK,iBAAiB,EAAE,YAAY,eAAe,MAAM;AACpE,WAAA,MAAM,UAAU,KAAK,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK;AACvF,WAAK,QAAQ;IACf;AAEA,SACG,KAAK,UAAU,MAAM,SAAS,KAAK,UAAU,MAAM,SAAS,KAAK,UAAU,MAAM,oBAClF,KAAK,QACL;AACA,YAAM,WAAW,KAAK,kBAAkB,KAAK,MAAM;AACnD,UAAI,aAAa,QAAW;AAC1B,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI,KAAK,eAAe;AACtB,cAAM,iBAAiB,KAAK,OAAO,SAAS,WAAW,KAAK,QAAQ,QAAQ;AAEtE,cAAA,KAAK,IAAI;AACZ,WAAA,cAAc,KAAK,OAAO;AACvB,cAAA,SAAS,IAAI;AACnB,WAAG,kBAAkB,MAAM;AAErB,cAAA,uBAAuB,KAAK,IAAI,KAAK,WAAW,OAAO,SAAS,OAAO,OAAO,OAAQ,CAAA;AACtF,cAAA,sBAAsB,iBAAiB,KAAK;AAElD,cAAM,aAAa,KAAK,IAAI,sBAAsB,mBAAmB;AAChE,aAAA,OAAO,OAAO,iBAAiB;AAE9B,cAAA,sBAAsB,KAAK,IAAI,KAAK,UAAU,CAAC,OAAO,SAAS,OAAO,OAAO,OAAQ,CAAA;AACrF,cAAA,qBAAqB,iBAAiB,KAAK;AAEjD,cAAM,YAAY,KAAK,IAAI,qBAAqB,kBAAkB;AAC7D,aAAA,OAAO,MAAM,iBAAiB;AAEnC,aAAK,OAAO;MAAuB,OAC9B;AACL,YAAI,SAAS;AAEb,YAAI,KAAK,OAAO,QAAQ,KAAK,cAAc;AACpC,eAAA,OAAO,OAAO,KAAK;AACf,mBAAA;AAAA,QACX;AAEA,YAAI,KAAK,OAAO,OAAO,KAAK,aAAa;AAClC,eAAA,OAAO,MAAM,KAAK;AACd,mBAAA;AAAA,QACX;AAEA,YAAI,QAAQ;AACV,eAAK,OAAO;QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EA4RO,iBAAiB,OAAsB;AAC5C,SAAK,QAAQ,UAAU;AAEvB,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2bQ,0BAA0B,SAAyB,MAAM,SAAyB,MAAY;AACpG,QAAI,QAAQ;AACV,UAAI,gBAAgB,QAAQ;AACV,wBAAA,OAAO,KAAK,MAAM;AAAA,MAAA,OAC7B;AACW,wBAAA,SAAS,OAAO;MAClC;AAAA,IAAA,OACK;AACL,sBAAgB,SAAS;AAAA,IAC3B;AAEA,QAAI,QAAQ;AACV,UAAI,gBAAgB,QAAQ;AACV,wBAAA,OAAO,KAAK,MAAM;AAAA,MAAA,OAC7B;AACW,wBAAA,SAAS,OAAO;MAClC;AAAA,IAAA,OACK;AACL,sBAAgB,SAAS;AAAA,IAC3B;AAAA,EACF;AAmYF;"}