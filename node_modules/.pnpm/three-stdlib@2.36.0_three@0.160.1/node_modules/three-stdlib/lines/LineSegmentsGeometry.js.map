{"version": 3, "file": "LineSegmentsGeometry.js", "sources": ["../../src/lines/LineSegmentsGeometry.js"], "sourcesContent": ["import {\n  Box3,\n  Float32<PERSON><PERSON>er<PERSON>ttribute,\n  InstancedBufferGeometry,\n  InstancedInterleavedBuffer,\n  InterleavedBufferAttribute,\n  Sphere,\n  Vector3,\n  WireframeGeometry,\n} from 'three'\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _vector = /* @__PURE__ */ new Vector3()\n\nclass LineSegmentsGeometry extends InstancedBufferGeometry {\n  constructor() {\n    super()\n\n    this.isLineSegmentsGeometry = true\n\n    this.type = 'LineSegmentsGeometry'\n\n    const positions = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0]\n    const uvs = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2]\n    const index = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5]\n\n    this.setIndex(index)\n    this.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n  }\n\n  applyMatrix4(matrix) {\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined) {\n      start.applyMatrix4(matrix)\n\n      end.applyMatrix4(matrix)\n\n      start.needsUpdate = true\n    }\n\n    if (this.boundingBox !== null) {\n      this.computeBoundingBox()\n    }\n\n    if (this.boundingSphere !== null) {\n      this.computeBoundingSphere()\n    }\n\n    return this\n  }\n\n  setPositions(array) {\n    let lineSegments\n\n    if (array instanceof Float32Array) {\n      lineSegments = array\n    } else if (Array.isArray(array)) {\n      lineSegments = new Float32Array(array)\n    }\n\n    const instanceBuffer = new InstancedInterleavedBuffer(lineSegments, 6, 1) // xyz, xyz\n\n    this.setAttribute('instanceStart', new InterleavedBufferAttribute(instanceBuffer, 3, 0)) // xyz\n    this.setAttribute('instanceEnd', new InterleavedBufferAttribute(instanceBuffer, 3, 3)) // xyz\n\n    //\n\n    this.computeBoundingBox()\n    this.computeBoundingSphere()\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    let colors\n\n    if (array instanceof Float32Array) {\n      colors = array\n    } else if (Array.isArray(array)) {\n      colors = new Float32Array(array)\n    }\n\n    const instanceColorBuffer = new InstancedInterleavedBuffer(colors, itemSize * 2, 1) // rgb(a), rgb(a)\n\n    this.setAttribute('instanceColorStart', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, 0)) // rgb(a)\n    this.setAttribute('instanceColorEnd', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, itemSize)) // rgb(a)\n\n    return this\n  }\n\n  fromWireframeGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromEdgesGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromMesh(mesh) {\n    this.fromWireframeGeometry(new WireframeGeometry(mesh.geometry))\n\n    // set colors, maybe\n\n    return this\n  }\n\n  fromLineSegments(lineSegments) {\n    const geometry = lineSegments.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n\n  computeBoundingBox() {\n    if (this.boundingBox === null) {\n      this.boundingBox = new Box3()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      this.boundingBox.setFromBufferAttribute(start)\n\n      _box.setFromBufferAttribute(end)\n\n      this.boundingBox.union(_box)\n    }\n  }\n\n  computeBoundingSphere() {\n    if (this.boundingSphere === null) {\n      this.boundingSphere = new Sphere()\n    }\n\n    if (this.boundingBox === null) {\n      this.computeBoundingBox()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      const center = this.boundingSphere.center\n\n      this.boundingBox.getCenter(center)\n\n      let maxRadiusSq = 0\n\n      for (let i = 0, il = start.count; i < il; i++) {\n        _vector.fromBufferAttribute(start, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n\n        _vector.fromBufferAttribute(end, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n      }\n\n      this.boundingSphere.radius = Math.sqrt(maxRadiusSq)\n\n      if (isNaN(this.boundingSphere.radius)) {\n        console.error(\n          'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.',\n          this,\n        )\n      }\n    }\n  }\n\n  toJSON() {\n    // todo\n  }\n\n  applyMatrix(matrix) {\n    console.warn('THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().')\n\n    return this.applyMatrix4(matrix)\n  }\n}\n\nexport { LineSegmentsGeometry }\n"], "names": [], "mappings": ";AAWA,MAAM,OAAuB,oBAAI,KAAM;AACvC,MAAM,UAA0B,oBAAI,QAAS;AAE7C,MAAM,6BAA6B,wBAAwB;AAAA,EACzD,cAAc;AACZ,UAAO;AAEP,SAAK,yBAAyB;AAE9B,SAAK,OAAO;AAEZ,UAAM,YAAY,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC;AAC/F,UAAM,MAAM,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE;AACnE,UAAM,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEnE,SAAK,SAAS,KAAK;AACnB,SAAK,aAAa,YAAY,IAAI,uBAAuB,WAAW,CAAC,CAAC;AACtE,SAAK,aAAa,MAAM,IAAI,uBAAuB,KAAK,CAAC,CAAC;AAAA,EAC3D;AAAA,EAED,aAAa,QAAQ;AACnB,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,MAAM,KAAK,WAAW;AAE5B,QAAI,UAAU,QAAW;AACvB,YAAM,aAAa,MAAM;AAEzB,UAAI,aAAa,MAAM;AAEvB,YAAM,cAAc;AAAA,IACrB;AAED,QAAI,KAAK,gBAAgB,MAAM;AAC7B,WAAK,mBAAoB;AAAA,IAC1B;AAED,QAAI,KAAK,mBAAmB,MAAM;AAChC,WAAK,sBAAuB;AAAA,IAC7B;AAED,WAAO;AAAA,EACR;AAAA,EAED,aAAa,OAAO;AAClB,QAAI;AAEJ,QAAI,iBAAiB,cAAc;AACjC,qBAAe;AAAA,IAChB,WAAU,MAAM,QAAQ,KAAK,GAAG;AAC/B,qBAAe,IAAI,aAAa,KAAK;AAAA,IACtC;AAED,UAAM,iBAAiB,IAAI,2BAA2B,cAAc,GAAG,CAAC;AAExE,SAAK,aAAa,iBAAiB,IAAI,2BAA2B,gBAAgB,GAAG,CAAC,CAAC;AACvF,SAAK,aAAa,eAAe,IAAI,2BAA2B,gBAAgB,GAAG,CAAC,CAAC;AAIrF,SAAK,mBAAoB;AACzB,SAAK,sBAAuB;AAE5B,WAAO;AAAA,EACR;AAAA,EAED,UAAU,OAAO,WAAW,GAAG;AAC7B,QAAI;AAEJ,QAAI,iBAAiB,cAAc;AACjC,eAAS;AAAA,IACV,WAAU,MAAM,QAAQ,KAAK,GAAG;AAC/B,eAAS,IAAI,aAAa,KAAK;AAAA,IAChC;AAED,UAAM,sBAAsB,IAAI,2BAA2B,QAAQ,WAAW,GAAG,CAAC;AAElF,SAAK,aAAa,sBAAsB,IAAI,2BAA2B,qBAAqB,UAAU,CAAC,CAAC;AACxG,SAAK,aAAa,oBAAoB,IAAI,2BAA2B,qBAAqB,UAAU,QAAQ,CAAC;AAE7G,WAAO;AAAA,EACR;AAAA,EAED,sBAAsB,UAAU;AAC9B,SAAK,aAAa,SAAS,WAAW,SAAS,KAAK;AAEpD,WAAO;AAAA,EACR;AAAA,EAED,kBAAkB,UAAU;AAC1B,SAAK,aAAa,SAAS,WAAW,SAAS,KAAK;AAEpD,WAAO;AAAA,EACR;AAAA,EAED,SAAS,MAAM;AACb,SAAK,sBAAsB,IAAI,kBAAkB,KAAK,QAAQ,CAAC;AAI/D,WAAO;AAAA,EACR;AAAA,EAED,iBAAiB,cAAc;AAC7B,UAAM,WAAW,aAAa;AAE9B,SAAK,aAAa,SAAS,WAAW,SAAS,KAAK;AAIpD,WAAO;AAAA,EACR;AAAA,EAED,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,MAAM;AAC7B,WAAK,cAAc,IAAI,KAAM;AAAA,IAC9B;AAED,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,MAAM,KAAK,WAAW;AAE5B,QAAI,UAAU,UAAa,QAAQ,QAAW;AAC5C,WAAK,YAAY,uBAAuB,KAAK;AAE7C,WAAK,uBAAuB,GAAG;AAE/B,WAAK,YAAY,MAAM,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA,EAED,wBAAwB;AACtB,QAAI,KAAK,mBAAmB,MAAM;AAChC,WAAK,iBAAiB,IAAI,OAAQ;AAAA,IACnC;AAED,QAAI,KAAK,gBAAgB,MAAM;AAC7B,WAAK,mBAAoB;AAAA,IAC1B;AAED,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,MAAM,KAAK,WAAW;AAE5B,QAAI,UAAU,UAAa,QAAQ,QAAW;AAC5C,YAAM,SAAS,KAAK,eAAe;AAEnC,WAAK,YAAY,UAAU,MAAM;AAEjC,UAAI,cAAc;AAElB,eAAS,IAAI,GAAG,KAAK,MAAM,OAAO,IAAI,IAAI,KAAK;AAC7C,gBAAQ,oBAAoB,OAAO,CAAC;AACpC,sBAAc,KAAK,IAAI,aAAa,OAAO,kBAAkB,OAAO,CAAC;AAErE,gBAAQ,oBAAoB,KAAK,CAAC;AAClC,sBAAc,KAAK,IAAI,aAAa,OAAO,kBAAkB,OAAO,CAAC;AAAA,MACtE;AAED,WAAK,eAAe,SAAS,KAAK,KAAK,WAAW;AAElD,UAAI,MAAM,KAAK,eAAe,MAAM,GAAG;AACrC,gBAAQ;AAAA,UACN;AAAA,UACA;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAED,SAAS;AAAA,EAER;AAAA,EAED,YAAY,QAAQ;AAClB,YAAQ,KAAK,+EAA+E;AAE5F,WAAO,KAAK,aAAa,MAAM;AAAA,EAChC;AACH;"}