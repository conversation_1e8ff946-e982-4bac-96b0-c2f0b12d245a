{"version": 3, "file": "LineMaterial.js", "sources": ["../../src/lines/LineMaterial.js"], "sourcesContent": ["/**\n * parameters = {\n *  color: <hex>,\n *  linewidth: <float>,\n *  dashed: <boolean>,\n *  dashScale: <float>,\n *  dashSize: <float>,\n *  dashOffset: <float>,\n *  gapSize: <float>,\n * }\n */\n\nimport { ShaderMaterial, UniformsLib, UniformsUtils, Vector2 } from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass LineMaterial extends ShaderMaterial {\n  constructor(parameters) {\n    super({\n      type: 'LineMaterial',\n\n      uniforms: UniformsUtils.clone(\n        UniformsUtils.merge([\n          UniformsLib.common,\n          UniformsLib.fog,\n          {\n            worldUnits: { value: 1 },\n            linewidth: { value: 1 },\n            resolution: { value: new Vector2(1, 1) },\n            dashOffset: { value: 0 },\n            dashScale: { value: 1 },\n            dashSize: { value: 1 },\n            gapSize: { value: 1 }, // todo FIX - maybe change to totalSize\n          },\n        ]),\n      ),\n\n      vertexShader: /* glsl */ `\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_vertex>\n\t\t\t\t#include <logdepthbuf_pars_vertex>\n\t\t\t\t#include <clipping_planes_pars_vertex>\n\n\t\t\t\tuniform float linewidth;\n\t\t\t\tuniform vec2 resolution;\n\n\t\t\t\tattribute vec3 instanceStart;\n\t\t\t\tattribute vec3 instanceEnd;\n\n\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\tvarying vec4 vLineColor;\n\t\t\t\t\t\tattribute vec4 instanceColorStart;\n\t\t\t\t\t\tattribute vec4 instanceColorEnd;\n\t\t\t\t\t#else\n\t\t\t\t\t\tvarying vec3 vLineColor;\n\t\t\t\t\t\tattribute vec3 instanceColorStart;\n\t\t\t\t\t\tattribute vec3 instanceColorEnd;\n\t\t\t\t\t#endif\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\tvarying vec4 worldPos;\n\t\t\t\t\tvarying vec3 worldStart;\n\t\t\t\t\tvarying vec3 worldEnd;\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#else\n\n\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\tuniform float dashScale;\n\t\t\t\t\tattribute float instanceDistanceStart;\n\t\t\t\t\tattribute float instanceDistanceEnd;\n\t\t\t\t\tvarying float vLineDistance;\n\n\t\t\t\t#endif\n\n\t\t\t\tvoid trimSegment( const in vec4 start, inout vec4 end ) {\n\n\t\t\t\t\t// trim end segment so it terminates between the camera plane and the near plane\n\n\t\t\t\t\t// conservative estimate of the near plane\n\t\t\t\t\tfloat a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column\n\t\t\t\t\tfloat b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column\n\t\t\t\t\tfloat nearEstimate = - 0.5 * b / a;\n\n\t\t\t\t\tfloat alpha = ( nearEstimate - start.z ) / ( end.z - start.z );\n\n\t\t\t\t\tend.xyz = mix( start.xyz, end.xyz, alpha );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#ifdef USE_COLOR\n\n\t\t\t\t\t\tvLineColor = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;\n\t\t\t\t\t\tvUv = uv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tfloat aspect = resolution.x / resolution.y;\n\n\t\t\t\t\t// camera space\n\t\t\t\t\tvec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );\n\t\t\t\t\tvec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\tworldStart = start.xyz;\n\t\t\t\t\t\tworldEnd = end.xyz;\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tvUv = uv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\t// special case for perspective projection, and segments that terminate either in, or behind, the camera plane\n\t\t\t\t\t// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space\n\t\t\t\t\t// but we need to perform ndc-space calculations in the shader, so we must address this issue directly\n\t\t\t\t\t// perhaps there is a more elegant solution -- WestLangley\n\n\t\t\t\t\tbool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column\n\n\t\t\t\t\tif ( perspective ) {\n\n\t\t\t\t\t\tif ( start.z < 0.0 && end.z >= 0.0 ) {\n\n\t\t\t\t\t\t\ttrimSegment( start, end );\n\n\t\t\t\t\t\t} else if ( end.z < 0.0 && start.z >= 0.0 ) {\n\n\t\t\t\t\t\t\ttrimSegment( end, start );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// clip space\n\t\t\t\t\tvec4 clipStart = projectionMatrix * start;\n\t\t\t\t\tvec4 clipEnd = projectionMatrix * end;\n\n\t\t\t\t\t// ndc space\n\t\t\t\t\tvec3 ndcStart = clipStart.xyz / clipStart.w;\n\t\t\t\t\tvec3 ndcEnd = clipEnd.xyz / clipEnd.w;\n\n\t\t\t\t\t// direction\n\t\t\t\t\tvec2 dir = ndcEnd.xy - ndcStart.xy;\n\n\t\t\t\t\t// account for clip-space aspect ratio\n\t\t\t\t\tdir.x *= aspect;\n\t\t\t\t\tdir = normalize( dir );\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\t// get the offset direction as perpendicular to the view vector\n\t\t\t\t\t\tvec3 worldDir = normalize( end.xyz - start.xyz );\n\t\t\t\t\t\tvec3 offset;\n\t\t\t\t\t\tif ( position.y < 0.5 ) {\n\n\t\t\t\t\t\t\toffset = normalize( cross( start.xyz, worldDir ) );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\toffset = normalize( cross( end.xyz, worldDir ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// sign flip\n\t\t\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t\t\tfloat forwardOffset = dot( worldDir, vec3( 0.0, 0.0, 1.0 ) );\n\n\t\t\t\t\t\t// don't extend the line if we're rendering dashes because we\n\t\t\t\t\t\t// won't be rendering the endcaps\n\t\t\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t\t\t// extend the line bounds to encompass  endcaps\n\t\t\t\t\t\t\tstart.xyz += - worldDir * linewidth * 0.5;\n\t\t\t\t\t\t\tend.xyz += worldDir * linewidth * 0.5;\n\n\t\t\t\t\t\t\t// shift the position of the quad so it hugs the forward edge of the line\n\t\t\t\t\t\t\toffset.xy -= dir * forwardOffset;\n\t\t\t\t\t\t\toffset.z += 0.5;\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t\t// endcaps\n\t\t\t\t\t\tif ( position.y > 1.0 || position.y < 0.0 ) {\n\n\t\t\t\t\t\t\toffset.xy += dir * 2.0 * forwardOffset;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// adjust for linewidth\n\t\t\t\t\t\toffset *= linewidth * 0.5;\n\n\t\t\t\t\t\t// set the world position\n\t\t\t\t\t\tworldPos = ( position.y < 0.5 ) ? start : end;\n\t\t\t\t\t\tworldPos.xyz += offset;\n\n\t\t\t\t\t\t// project the worldpos\n\t\t\t\t\t\tvec4 clip = projectionMatrix * worldPos;\n\n\t\t\t\t\t\t// shift the depth of the projected points so the line\n\t\t\t\t\t\t// segments overlap neatly\n\t\t\t\t\t\tvec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;\n\t\t\t\t\t\tclip.z = clipPose.z * clip.w;\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tvec2 offset = vec2( dir.y, - dir.x );\n\t\t\t\t\t\t// undo aspect ratio adjustment\n\t\t\t\t\t\tdir.x /= aspect;\n\t\t\t\t\t\toffset.x /= aspect;\n\n\t\t\t\t\t\t// sign flip\n\t\t\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t\t\t// endcaps\n\t\t\t\t\t\tif ( position.y < 0.0 ) {\n\n\t\t\t\t\t\t\toffset += - dir;\n\n\t\t\t\t\t\t} else if ( position.y > 1.0 ) {\n\n\t\t\t\t\t\t\toffset += dir;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// adjust for linewidth\n\t\t\t\t\t\toffset *= linewidth;\n\n\t\t\t\t\t\t// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...\n\t\t\t\t\t\toffset /= resolution.y;\n\n\t\t\t\t\t\t// select end\n\t\t\t\t\t\tvec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;\n\n\t\t\t\t\t\t// back to clip space\n\t\t\t\t\t\toffset *= clip.w;\n\n\t\t\t\t\t\tclip.xy += offset;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tgl_Position = clip;\n\n\t\t\t\t\tvec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation\n\n\t\t\t\t\t#include <logdepthbuf_vertex>\n\t\t\t\t\t#include <clipping_planes_vertex>\n\t\t\t\t\t#include <fog_vertex>\n\n\t\t\t\t}\n\t\t\t`,\n      fragmentShader: /* glsl */ `\n\t\t\t\tuniform vec3 diffuse;\n\t\t\t\tuniform float opacity;\n\t\t\t\tuniform float linewidth;\n\n\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\tuniform float dashOffset;\n\t\t\t\t\tuniform float dashSize;\n\t\t\t\t\tuniform float gapSize;\n\n\t\t\t\t#endif\n\n\t\t\t\tvarying float vLineDistance;\n\n\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\tvarying vec4 worldPos;\n\t\t\t\t\tvarying vec3 worldStart;\n\t\t\t\t\tvarying vec3 worldEnd;\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#else\n\n\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t#endif\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_fragment>\n\t\t\t\t#include <logdepthbuf_pars_fragment>\n\t\t\t\t#include <clipping_planes_pars_fragment>\n\n\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\tvarying vec4 vLineColor;\n\t\t\t\t\t#else\n\t\t\t\t\t\tvarying vec3 vLineColor;\n\t\t\t\t\t#endif\n\t\t\t\t#endif\n\n\t\t\t\tvec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {\n\n\t\t\t\t\tfloat mua;\n\t\t\t\t\tfloat mub;\n\n\t\t\t\t\tvec3 p13 = p1 - p3;\n\t\t\t\t\tvec3 p43 = p4 - p3;\n\n\t\t\t\t\tvec3 p21 = p2 - p1;\n\n\t\t\t\t\tfloat d1343 = dot( p13, p43 );\n\t\t\t\t\tfloat d4321 = dot( p43, p21 );\n\t\t\t\t\tfloat d1321 = dot( p13, p21 );\n\t\t\t\t\tfloat d4343 = dot( p43, p43 );\n\t\t\t\t\tfloat d2121 = dot( p21, p21 );\n\n\t\t\t\t\tfloat denom = d2121 * d4343 - d4321 * d4321;\n\n\t\t\t\t\tfloat numer = d1343 * d4321 - d1321 * d4343;\n\n\t\t\t\t\tmua = numer / denom;\n\t\t\t\t\tmua = clamp( mua, 0.0, 1.0 );\n\t\t\t\t\tmub = ( d1343 + d4321 * ( mua ) ) / d4343;\n\t\t\t\t\tmub = clamp( mub, 0.0, 1.0 );\n\n\t\t\t\t\treturn vec2( mua, mub );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#include <clipping_planes_fragment>\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tif ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps\n\n\t\t\t\t\t\tif ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tfloat alpha = opacity;\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\t// Find the closest points on the view ray and the line segment\n\t\t\t\t\t\tvec3 rayEnd = normalize( worldPos.xyz ) * 1e5;\n\t\t\t\t\t\tvec3 lineDir = worldEnd - worldStart;\n\t\t\t\t\t\tvec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );\n\n\t\t\t\t\t\tvec3 p1 = worldStart + lineDir * params.x;\n\t\t\t\t\t\tvec3 p2 = rayEnd * params.y;\n\t\t\t\t\t\tvec3 delta = p1 - p2;\n\t\t\t\t\t\tfloat len = length( delta );\n\t\t\t\t\t\tfloat norm = len / linewidth;\n\n\t\t\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\t\t\tfloat dnorm = fwidth( norm );\n\t\t\t\t\t\t\t\talpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );\n\n\t\t\t\t\t\t\t#else\n\n\t\t\t\t\t\t\t\tif ( norm > 0.5 ) {\n\n\t\t\t\t\t\t\t\t\tdiscard;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t#endif\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\t\t// artifacts appear on some hardware if a derivative is taken within a conditional\n\t\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\t\t\t\t\t\t\tfloat dlen = fwidth( len2 );\n\n\t\t\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\t\t\talpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t#else\n\n\t\t\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\n\t\t\t\t\t\t\t\tif ( len2 > 1.0 ) discard;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tvec4 diffuseColor = vec4( diffuse, alpha );\n\t\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\t\tdiffuseColor *= vLineColor;\n\t\t\t\t\t\t#else\n\t\t\t\t\t\t\tdiffuseColor.rgb *= vLineColor;\n\t\t\t\t\t\t#endif\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\t\t\tgl_FragColor = diffuseColor;\n\n\t\t\t\t\t#include <tonemapping_fragment>\n\t\t\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t\t\t#include <fog_fragment>\n\t\t\t\t\t#include <premultiplied_alpha_fragment>\n\n\t\t\t\t}\n\t\t\t`,\n      clipping: true, // required for clipping support\n    })\n\n    this.isLineMaterial = true\n\n    this.onBeforeCompile = function () {\n      if (this.transparent) {\n        this.defines.USE_LINE_COLOR_ALPHA = '1'\n      } else {\n        delete this.defines.USE_LINE_COLOR_ALPHA\n      }\n    }\n\n    Object.defineProperties(this, {\n      color: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.diffuse.value\n        },\n\n        set: function (value) {\n          this.uniforms.diffuse.value = value\n        },\n      },\n\n      worldUnits: {\n        enumerable: true,\n\n        get: function () {\n          return 'WORLD_UNITS' in this.defines\n        },\n\n        set: function (value) {\n          if (value === true) {\n            this.defines.WORLD_UNITS = ''\n          } else {\n            delete this.defines.WORLD_UNITS\n          }\n        },\n      },\n\n      linewidth: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.linewidth.value\n        },\n\n        set: function (value) {\n          this.uniforms.linewidth.value = value\n        },\n      },\n\n      dashed: {\n        enumerable: true,\n\n        get: function () {\n          return Boolean('USE_DASH' in this.defines)\n        },\n\n        set(value) {\n          if (Boolean(value) !== Boolean('USE_DASH' in this.defines)) {\n            this.needsUpdate = true\n          }\n\n          if (value === true) {\n            this.defines.USE_DASH = ''\n          } else {\n            delete this.defines.USE_DASH\n          }\n        },\n      },\n\n      dashScale: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashScale.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashScale.value = value\n        },\n      },\n\n      dashSize: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashSize.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashSize.value = value\n        },\n      },\n\n      dashOffset: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashOffset.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashOffset.value = value\n        },\n      },\n\n      gapSize: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.gapSize.value\n        },\n\n        set: function (value) {\n          this.uniforms.gapSize.value = value\n        },\n      },\n\n      opacity: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.opacity.value\n        },\n\n        set: function (value) {\n          this.uniforms.opacity.value = value\n        },\n      },\n\n      resolution: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.resolution.value\n        },\n\n        set: function (value) {\n          this.uniforms.resolution.value.copy(value)\n        },\n      },\n\n      alphaToCoverage: {\n        enumerable: true,\n\n        get: function () {\n          return Boolean('USE_ALPHA_TO_COVERAGE' in this.defines)\n        },\n\n        set: function (value) {\n          if (Boolean(value) !== Boolean('USE_ALPHA_TO_COVERAGE' in this.defines)) {\n            this.needsUpdate = true\n          }\n\n          if (value === true) {\n            this.defines.USE_ALPHA_TO_COVERAGE = ''\n            this.extensions.derivatives = true\n          } else {\n            delete this.defines.USE_ALPHA_TO_COVERAGE\n            this.extensions.derivatives = false\n          }\n        },\n      },\n    })\n\n    this.setValues(parameters)\n  }\n}\n\nexport { LineMaterial }\n"], "names": [], "mappings": ";;AAeA,MAAM,qBAAqB,eAAe;AAAA,EACxC,YAAY,YAAY;AACtB,UAAM;AAAA,MACJ,MAAM;AAAA,MAEN,UAAU,cAAc;AAAA,QACtB,cAAc,MAAM;AAAA,UAClB,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ;AAAA,YACE,YAAY,EAAE,OAAO,EAAG;AAAA,YACxB,WAAW,EAAE,OAAO,EAAG;AAAA,YACvB,YAAY,EAAE,OAAO,IAAI,QAAQ,GAAG,CAAC,EAAG;AAAA,YACxC,YAAY,EAAE,OAAO,EAAG;AAAA,YACxB,WAAW,EAAE,OAAO,EAAG;AAAA,YACvB,UAAU,EAAE,OAAO,EAAG;AAAA,YACtB,SAAS,EAAE,OAAO,EAAG;AAAA;AAAA,UACtB;AAAA,QACX,CAAS;AAAA,MACF;AAAA,MAED;AAAA;AAAA,QAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6OzB;AAAA;AAAA,QAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAuKhB,WAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMpD,UAAU;AAAA;AAAA,IAChB,CAAK;AAED,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,WAAY;AACjC,UAAI,KAAK,aAAa;AACpB,aAAK,QAAQ,uBAAuB;AAAA,MAC5C,OAAa;AACL,eAAO,KAAK,QAAQ;AAAA,MACrB;AAAA,IACF;AAED,WAAO,iBAAiB,MAAM;AAAA,MAC5B,OAAO;AAAA,QACL,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,QAAQ;AAAA,QAC9B;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,QAAQ,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA,MAED,YAAY;AAAA,QACV,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,iBAAiB,KAAK;AAAA,QAC9B;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,cAAI,UAAU,MAAM;AAClB,iBAAK,QAAQ,cAAc;AAAA,UACvC,OAAiB;AACL,mBAAO,KAAK,QAAQ;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MAED,WAAW;AAAA,QACT,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,UAAU;AAAA,QAChC;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,UAAU,QAAQ;AAAA,QACjC;AAAA,MACF;AAAA,MAED,QAAQ;AAAA,QACN,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,QAAQ,cAAc,KAAK,OAAO;AAAA,QAC1C;AAAA,QAED,IAAI,OAAO;AACT,cAAI,QAAQ,KAAK,MAAM,QAAQ,cAAc,KAAK,OAAO,GAAG;AAC1D,iBAAK,cAAc;AAAA,UACpB;AAED,cAAI,UAAU,MAAM;AAClB,iBAAK,QAAQ,WAAW;AAAA,UACpC,OAAiB;AACL,mBAAO,KAAK,QAAQ;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MAED,WAAW;AAAA,QACT,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,UAAU;AAAA,QAChC;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,UAAU,QAAQ;AAAA,QACjC;AAAA,MACF;AAAA,MAED,UAAU;AAAA,QACR,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,SAAS;AAAA,QAC/B;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,SAAS,QAAQ;AAAA,QAChC;AAAA,MACF;AAAA,MAED,YAAY;AAAA,QACV,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,WAAW;AAAA,QACjC;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,WAAW,QAAQ;AAAA,QAClC;AAAA,MACF;AAAA,MAED,SAAS;AAAA,QACP,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,QAAQ;AAAA,QAC9B;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,QAAQ,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA,MAED,SAAS;AAAA,QACP,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,QAAQ;AAAA,QAC9B;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,QAAQ,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA,MAED,YAAY;AAAA,QACV,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,KAAK,SAAS,WAAW;AAAA,QACjC;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,eAAK,SAAS,WAAW,MAAM,KAAK,KAAK;AAAA,QAC1C;AAAA,MACF;AAAA,MAED,iBAAiB;AAAA,QACf,YAAY;AAAA,QAEZ,KAAK,WAAY;AACf,iBAAO,QAAQ,2BAA2B,KAAK,OAAO;AAAA,QACvD;AAAA,QAED,KAAK,SAAU,OAAO;AACpB,cAAI,QAAQ,KAAK,MAAM,QAAQ,2BAA2B,KAAK,OAAO,GAAG;AACvE,iBAAK,cAAc;AAAA,UACpB;AAED,cAAI,UAAU,MAAM;AAClB,iBAAK,QAAQ,wBAAwB;AACrC,iBAAK,WAAW,cAAc;AAAA,UAC1C,OAAiB;AACL,mBAAO,KAAK,QAAQ;AACpB,iBAAK,WAAW,cAAc;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACP,CAAK;AAED,SAAK,UAAU,UAAU;AAAA,EAC1B;AACH;"}