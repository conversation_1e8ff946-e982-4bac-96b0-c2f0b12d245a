{"version": 3, "file": "PLYExporter.cjs", "sources": ["../../src/exporters/PLYExporter.ts"], "sourcesContent": ["import { BufferGeometry, Matrix3, Mesh, Object3D, Vector3 } from 'three'\n\n/**\n * https://github.com/gkjo<PERSON>son/ply-exporter-js\n *\n * Usage:\n *  const exporter = new PLYExporter();\n *\n *  // second argument is a list of options\n *  exporter.parse(mesh, data => console.log(data), { binary: true, excludeAttributes: [ 'color' ], littleEndian: true });\n *\n * Format Definition:\n * http://paulbourke.net/dataformats/ply/\n */\n\nexport interface PLYExporterOptions {\n  binary?: boolean\n  excludeAttributes?: string[]\n  littleEndian?: boolean\n}\n\nclass PLYExporter {\n  public parse(\n    object: Object3D,\n    onDone: ((res: string) => void) | undefined,\n    options: PLYExporterOptions,\n  ): string | ArrayBuffer | null {\n    if (onDone && typeof onDone === 'object') {\n      console.warn(\n        'THREE.PLYExporter: The options parameter is now the third argument to the \"parse\" function. See the documentation for the new API.',\n      )\n      options = onDone\n      onDone = undefined\n    }\n\n    // Default options\n    const defaultOptions = {\n      binary: false,\n      excludeAttributes: [], // normal, uv, color, index\n      littleEndian: false,\n    }\n\n    options = Object.assign(defaultOptions, options)\n\n    const excludeAttributes = options.excludeAttributes\n    let includeNormals = false\n    let includeColors = false\n    let includeUVs = false\n\n    // count the vertices, check which properties are used,\n    // and cache the BufferGeometry\n    let vertexCount = 0\n    let faceCount = 0\n    object.traverse(function (child) {\n      if (child instanceof Mesh && child.isMesh) {\n        const mesh = child\n        const geometry = mesh.geometry\n\n        if (!geometry.isBufferGeometry) {\n          throw new Error('THREE.PLYExporter: Geometry is not of type THREE.BufferGeometry.')\n        }\n\n        const vertices = geometry.getAttribute('position')\n        const normals = geometry.getAttribute('normal')\n        const uvs = geometry.getAttribute('uv')\n        const colors = geometry.getAttribute('color')\n        const indices = geometry.getIndex()\n\n        if (vertices === undefined) {\n          return\n        }\n\n        vertexCount += vertices.count\n        faceCount += indices ? indices.count / 3 : vertices.count / 3\n\n        if (normals !== undefined) includeNormals = true\n\n        if (uvs !== undefined) includeUVs = true\n\n        if (colors !== undefined) includeColors = true\n      }\n    })\n\n    const includeIndices = excludeAttributes?.indexOf('index') === -1\n    includeNormals = includeNormals && excludeAttributes?.indexOf('normal') === -1\n    includeColors = includeColors && excludeAttributes?.indexOf('color') === -1\n    includeUVs = includeUVs && excludeAttributes?.indexOf('uv') === -1\n\n    if (includeIndices && faceCount !== Math.floor(faceCount)) {\n      // point cloud meshes will not have an index array and may not have a\n      // number of vertices that is divisble by 3 (and therefore representable\n      // as triangles)\n      console.error(\n        'PLYExporter: Failed to generate a valid PLY file with triangle indices because the ' +\n          'number of indices is not divisible by 3.',\n      )\n\n      return null\n    }\n\n    const indexByteCount = 4\n\n    let header =\n      'ply\\n' +\n      `format ${\n        options.binary ? (options.littleEndian ? 'binary_little_endian' : 'binary_big_endian') : 'ascii'\n      } 1.0\\n` +\n      `element vertex ${vertexCount}\\n` +\n      // position\n      'property float x\\n' +\n      'property float y\\n' +\n      'property float z\\n'\n\n    if (includeNormals) {\n      // normal\n      header += 'property float nx\\n' + 'property float ny\\n' + 'property float nz\\n'\n    }\n\n    if (includeUVs) {\n      // uvs\n      header += 'property float s\\n' + 'property float t\\n'\n    }\n\n    if (includeColors) {\n      // colors\n      header += 'property uchar red\\n' + 'property uchar green\\n' + 'property uchar blue\\n'\n    }\n\n    if (includeIndices) {\n      // faces\n      header += `${`element face ${faceCount}\\n`}property list uchar int vertex_index\\n`\n    }\n\n    header += 'end_header\\n'\n\n    // Generate attribute data\n    const vertex = new Vector3()\n    const normalMatrixWorld = new Matrix3()\n    let result: string | ArrayBuffer | null = null\n\n    if (options.binary) {\n      // Binary File Generation\n      const headerBin = new TextEncoder().encode(header)\n\n      // 3 position values at 4 bytes\n      // 3 normal values at 4 bytes\n      // 3 color channels with 1 byte\n      // 2 uv values at 4 bytes\n      const vertexListLength =\n        vertexCount * (4 * 3 + (includeNormals ? 4 * 3 : 0) + (includeColors ? 3 : 0) + (includeUVs ? 4 * 2 : 0))\n\n      // 1 byte shape desciptor\n      // 3 vertex indices at ${indexByteCount} bytes\n      const faceListLength = includeIndices ? faceCount * (indexByteCount * 3 + 1) : 0\n      const output = new DataView(new ArrayBuffer(headerBin.length + vertexListLength + faceListLength))\n      new Uint8Array(output.buffer).set(headerBin, 0)\n\n      let vOffset = headerBin.length\n      let fOffset = headerBin.length + vertexListLength\n      let writtenVertices = 0\n      this.traverseMeshes(object, function (mesh, geometry) {\n        const vertices = geometry.getAttribute('position')\n        const normals = geometry.getAttribute('normal')\n        const uvs = geometry.getAttribute('uv')\n        const colors = geometry.getAttribute('color')\n        const indices = geometry.getIndex()\n\n        normalMatrixWorld.getNormalMatrix(mesh.matrixWorld)\n\n        for (let i = 0, l = vertices.count; i < l; i++) {\n          vertex.x = vertices.getX(i)\n          vertex.y = vertices.getY(i)\n          vertex.z = vertices.getZ(i)\n\n          vertex.applyMatrix4(mesh.matrixWorld)\n\n          // Position information\n          output.setFloat32(vOffset, vertex.x, options.littleEndian)\n          vOffset += 4\n\n          output.setFloat32(vOffset, vertex.y, options.littleEndian)\n          vOffset += 4\n\n          output.setFloat32(vOffset, vertex.z, options.littleEndian)\n          vOffset += 4\n\n          // Normal information\n          if (includeNormals) {\n            if (normals != null) {\n              vertex.x = normals.getX(i)\n              vertex.y = normals.getY(i)\n              vertex.z = normals.getZ(i)\n\n              vertex.applyMatrix3(normalMatrixWorld).normalize()\n\n              output.setFloat32(vOffset, vertex.x, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, vertex.y, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, vertex.z, options.littleEndian)\n              vOffset += 4\n            } else {\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n            }\n          }\n\n          // UV information\n          if (includeUVs) {\n            if (uvs != null) {\n              output.setFloat32(vOffset, uvs.getX(i), options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, uvs.getY(i), options.littleEndian)\n              vOffset += 4\n            } else if (!includeUVs) {\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n\n              output.setFloat32(vOffset, 0, options.littleEndian)\n              vOffset += 4\n            }\n          }\n\n          // Color information\n          if (includeColors) {\n            if (colors != null) {\n              output.setUint8(vOffset, Math.floor(colors.getX(i) * 255))\n              vOffset += 1\n\n              output.setUint8(vOffset, Math.floor(colors.getY(i) * 255))\n              vOffset += 1\n\n              output.setUint8(vOffset, Math.floor(colors.getZ(i) * 255))\n              vOffset += 1\n            } else {\n              output.setUint8(vOffset, 255)\n              vOffset += 1\n\n              output.setUint8(vOffset, 255)\n              vOffset += 1\n\n              output.setUint8(vOffset, 255)\n              vOffset += 1\n            }\n          }\n        }\n\n        if (includeIndices) {\n          // Create the face list\n\n          if (indices !== null) {\n            for (let i = 0, l = indices.count; i < l; i += 3) {\n              output.setUint8(fOffset, 3)\n              fOffset += 1\n\n              output.setUint32(fOffset, indices.getX(i + 0) + writtenVertices, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, indices.getX(i + 1) + writtenVertices, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, indices.getX(i + 2) + writtenVertices, options.littleEndian)\n              fOffset += indexByteCount\n            }\n          } else {\n            for (let i = 0, l = vertices.count; i < l; i += 3) {\n              output.setUint8(fOffset, 3)\n              fOffset += 1\n\n              output.setUint32(fOffset, writtenVertices + i, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, writtenVertices + i + 1, options.littleEndian)\n              fOffset += indexByteCount\n\n              output.setUint32(fOffset, writtenVertices + i + 2, options.littleEndian)\n              fOffset += indexByteCount\n            }\n          }\n        }\n\n        // Save the amount of verts we've already written so we can offset\n        // the face index on the next mesh\n        writtenVertices += vertices.count\n      })\n\n      result = output.buffer\n    } else {\n      // Ascii File Generation\n      // count the number of vertices\n      let writtenVertices = 0\n      let vertexList = ''\n      let faceList = ''\n\n      this.traverseMeshes(object, function (mesh, geometry) {\n        const vertices = geometry.getAttribute('position')\n        const normals = geometry.getAttribute('normal')\n        const uvs = geometry.getAttribute('uv')\n        const colors = geometry.getAttribute('color')\n        const indices = geometry.getIndex()\n\n        normalMatrixWorld.getNormalMatrix(mesh.matrixWorld)\n\n        // form each line\n        for (let i = 0, l = vertices.count; i < l; i++) {\n          vertex.x = vertices.getX(i)\n          vertex.y = vertices.getY(i)\n          vertex.z = vertices.getZ(i)\n\n          vertex.applyMatrix4(mesh.matrixWorld)\n\n          // Position information\n          let line = vertex.x + ' ' + vertex.y + ' ' + vertex.z\n\n          // Normal information\n          if (includeNormals) {\n            if (normals != null) {\n              vertex.x = normals.getX(i)\n              vertex.y = normals.getY(i)\n              vertex.z = normals.getZ(i)\n\n              vertex.applyMatrix3(normalMatrixWorld).normalize()\n\n              line += ' ' + vertex.x + ' ' + vertex.y + ' ' + vertex.z\n            } else {\n              line += ' 0 0 0'\n            }\n          }\n\n          // UV information\n          if (includeUVs) {\n            if (uvs != null) {\n              line += ' ' + uvs.getX(i) + ' ' + uvs.getY(i)\n            } else if (includeUVs) {\n              line += ' 0 0'\n            }\n          }\n\n          // Color information\n          if (includeColors) {\n            if (colors != null) {\n              line +=\n                ' ' +\n                Math.floor(colors.getX(i) * 255) +\n                ' ' +\n                Math.floor(colors.getY(i) * 255) +\n                ' ' +\n                Math.floor(colors.getZ(i) * 255)\n            } else {\n              line += ' 255 255 255'\n            }\n          }\n\n          vertexList += line + '\\n'\n        }\n\n        // Create the face list\n        if (includeIndices) {\n          if (indices !== null) {\n            for (let i = 0, l = indices.count; i < l; i += 3) {\n              faceList += `3 ${indices.getX(i + 0) + writtenVertices}`\n              faceList += ` ${indices.getX(i + 1) + writtenVertices}`\n              faceList += ` ${indices.getX(i + 2) + writtenVertices}\\n`\n            }\n          } else {\n            for (let i = 0, l = vertices.count; i < l; i += 3) {\n              faceList += `3 ${writtenVertices + i} ${writtenVertices + i + 1} ${writtenVertices + i + 2}\\n`\n            }\n          }\n\n          faceCount += indices ? indices.count / 3 : vertices.count / 3\n        }\n\n        writtenVertices += vertices.count\n      })\n\n      result = `${header}${vertexList}${includeIndices ? `${faceList}\\n` : '\\n'}`\n    }\n\n    if (typeof onDone === 'function') {\n      requestAnimationFrame(() => onDone && onDone(typeof result === 'string' ? result : ''))\n    }\n\n    return result\n  }\n\n  // Iterate over the valid meshes in the object\n  private traverseMeshes(object: Object3D, cb: (mesh: Mesh, geometry: BufferGeometry) => void): void {\n    object.traverse(function (child) {\n      if (child instanceof Mesh && child.isMesh) {\n        const mesh = child\n        const geometry = mesh.geometry\n\n        if (!geometry.isBufferGeometry) {\n          throw new Error('THREE.PLYExporter: Geometry is not of type THREE.BufferGeometry.')\n        }\n\n        if (geometry.hasAttribute('position')) {\n          cb(mesh, geometry)\n        }\n      }\n    })\n  }\n}\n\nexport { PLYExporter }\n"], "names": ["<PERSON><PERSON>", "Vector3", "Matrix3"], "mappings": ";;;AAqBA,MAAM,YAAY;AAAA,EACT,MACL,QACA,QACA,SAC6B;AACzB,QAAA,UAAU,OAAO,WAAW,UAAU;AAChC,cAAA;AAAA,QACN;AAAA,MAAA;AAEQ,gBAAA;AACD,eAAA;AAAA,IACX;AAGA,UAAM,iBAAiB;AAAA,MACrB,QAAQ;AAAA,MACR,mBAAmB,CAAC;AAAA;AAAA,MACpB,cAAc;AAAA,IAAA;AAGN,cAAA,OAAO,OAAO,gBAAgB,OAAO;AAE/C,UAAM,oBAAoB,QAAQ;AAClC,QAAI,iBAAiB;AACrB,QAAI,gBAAgB;AACpB,QAAI,aAAa;AAIjB,QAAI,cAAc;AAClB,QAAI,YAAY;AACT,WAAA,SAAS,SAAU,OAAO;AAC3B,UAAA,iBAAiBA,MAAAA,QAAQ,MAAM,QAAQ;AACzC,cAAM,OAAO;AACb,cAAM,WAAW,KAAK;AAElB,YAAA,CAAC,SAAS,kBAAkB;AACxB,gBAAA,IAAI,MAAM,kEAAkE;AAAA,QACpF;AAEM,cAAA,WAAW,SAAS,aAAa,UAAU;AAC3C,cAAA,UAAU,SAAS,aAAa,QAAQ;AACxC,cAAA,MAAM,SAAS,aAAa,IAAI;AAChC,cAAA,SAAS,SAAS,aAAa,OAAO;AACtC,cAAA,UAAU,SAAS;AAEzB,YAAI,aAAa,QAAW;AAC1B;AAAA,QACF;AAEA,uBAAe,SAAS;AACxB,qBAAa,UAAU,QAAQ,QAAQ,IAAI,SAAS,QAAQ;AAE5D,YAAI,YAAY;AAA4B,2BAAA;AAE5C,YAAI,QAAQ;AAAwB,uBAAA;AAEpC,YAAI,WAAW;AAA2B,0BAAA;AAAA,MAC5C;AAAA,IAAA,CACD;AAED,UAAM,kBAAiB,uDAAmB,QAAQ,cAAa;AAC/D,qBAAiB,mBAAkB,uDAAmB,QAAQ,eAAc;AAC5E,oBAAgB,kBAAiB,uDAAmB,QAAQ,cAAa;AACzE,iBAAa,eAAc,uDAAmB,QAAQ,WAAU;AAEhE,QAAI,kBAAkB,cAAc,KAAK,MAAM,SAAS,GAAG;AAIjD,cAAA;AAAA,QACN;AAAA,MAAA;AAIK,aAAA;AAAA,IACT;AAEA,UAAM,iBAAiB;AAEvB,QAAI,SACF;AAAA,SAEE,QAAQ,SAAU,QAAQ,eAAe,yBAAyB,sBAAuB;AAAA,iBAEzE;AAAA;AAAA;AAAA;AAAA;AAMpB,QAAI,gBAAgB;AAER,gBAAA;AAAA,IACZ;AAEA,QAAI,YAAY;AAEJ,gBAAA;AAAA,IACZ;AAEA,QAAI,eAAe;AAEP,gBAAA;AAAA,IACZ;AAEA,QAAI,gBAAgB;AAElB,gBAAU,GAAG,gBAAgB;AAAA;AAAA;AAAA,IAC/B;AAEU,cAAA;AAGJ,UAAA,SAAS,IAAIC,MAAAA;AACb,UAAA,oBAAoB,IAAIC,MAAAA;AAC9B,QAAI,SAAsC;AAE1C,QAAI,QAAQ,QAAQ;AAElB,YAAM,YAAY,IAAI,YAAY,EAAE,OAAO,MAAM;AAMjD,YAAM,mBACJ,eAAe,IAAI,KAAK,iBAAiB,IAAI,IAAI,MAAM,gBAAgB,IAAI,MAAM,aAAa,IAAI,IAAI;AAIxG,YAAM,iBAAiB,iBAAiB,aAAa,iBAAiB,IAAI,KAAK;AACzE,YAAA,SAAS,IAAI,SAAS,IAAI,YAAY,UAAU,SAAS,mBAAmB,cAAc,CAAC;AACjG,UAAI,WAAW,OAAO,MAAM,EAAE,IAAI,WAAW,CAAC;AAE9C,UAAI,UAAU,UAAU;AACpB,UAAA,UAAU,UAAU,SAAS;AACjC,UAAI,kBAAkB;AACtB,WAAK,eAAe,QAAQ,SAAU,MAAM,UAAU;AAC9C,cAAA,WAAW,SAAS,aAAa,UAAU;AAC3C,cAAA,UAAU,SAAS,aAAa,QAAQ;AACxC,cAAA,MAAM,SAAS,aAAa,IAAI;AAChC,cAAA,SAAS,SAAS,aAAa,OAAO;AACtC,cAAA,UAAU,SAAS;AAEP,0BAAA,gBAAgB,KAAK,WAAW;AAElD,iBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK;AACvC,iBAAA,IAAI,SAAS,KAAK,CAAC;AACnB,iBAAA,IAAI,SAAS,KAAK,CAAC;AACnB,iBAAA,IAAI,SAAS,KAAK,CAAC;AAEnB,iBAAA,aAAa,KAAK,WAAW;AAGpC,iBAAO,WAAW,SAAS,OAAO,GAAG,QAAQ,YAAY;AAC9C,qBAAA;AAEX,iBAAO,WAAW,SAAS,OAAO,GAAG,QAAQ,YAAY;AAC9C,qBAAA;AAEX,iBAAO,WAAW,SAAS,OAAO,GAAG,QAAQ,YAAY;AAC9C,qBAAA;AAGX,cAAI,gBAAgB;AAClB,gBAAI,WAAW,MAAM;AACZ,qBAAA,IAAI,QAAQ,KAAK,CAAC;AAClB,qBAAA,IAAI,QAAQ,KAAK,CAAC;AAClB,qBAAA,IAAI,QAAQ,KAAK,CAAC;AAElB,qBAAA,aAAa,iBAAiB,EAAE,UAAU;AAEjD,qBAAO,WAAW,SAAS,OAAO,GAAG,QAAQ,YAAY;AAC9C,yBAAA;AAEX,qBAAO,WAAW,SAAS,OAAO,GAAG,QAAQ,YAAY;AAC9C,yBAAA;AAEX,qBAAO,WAAW,SAAS,OAAO,GAAG,QAAQ,YAAY;AAC9C,yBAAA;AAAA,YAAA,OACN;AACL,qBAAO,WAAW,SAAS,GAAG,QAAQ,YAAY;AACvC,yBAAA;AAEX,qBAAO,WAAW,SAAS,GAAG,QAAQ,YAAY;AACvC,yBAAA;AAEX,qBAAO,WAAW,SAAS,GAAG,QAAQ,YAAY;AACvC,yBAAA;AAAA,YACb;AAAA,UACF;AAGA,cAAI,YAAY;AACd,gBAAI,OAAO,MAAM;AACf,qBAAO,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,QAAQ,YAAY;AACjD,yBAAA;AAEX,qBAAO,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,QAAQ,YAAY;AACjD,yBAAA;AAAA,YAAA,WACF,CAAC,YAAY;AACtB,qBAAO,WAAW,SAAS,GAAG,QAAQ,YAAY;AACvC,yBAAA;AAEX,qBAAO,WAAW,SAAS,GAAG,QAAQ,YAAY;AACvC,yBAAA;AAAA,YACb;AAAA,UACF;AAGA,cAAI,eAAe;AACjB,gBAAI,UAAU,MAAM;AACX,qBAAA,SAAS,SAAS,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,CAAC;AAC9C,yBAAA;AAEJ,qBAAA,SAAS,SAAS,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,CAAC;AAC9C,yBAAA;AAEJ,qBAAA,SAAS,SAAS,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,CAAC;AAC9C,yBAAA;AAAA,YAAA,OACN;AACE,qBAAA,SAAS,SAAS,GAAG;AACjB,yBAAA;AAEJ,qBAAA,SAAS,SAAS,GAAG;AACjB,yBAAA;AAEJ,qBAAA,SAAS,SAAS,GAAG;AACjB,yBAAA;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAEA,YAAI,gBAAgB;AAGlB,cAAI,YAAY,MAAM;AACX,qBAAA,IAAI,GAAG,IAAI,QAAQ,OAAO,IAAI,GAAG,KAAK,GAAG;AACzC,qBAAA,SAAS,SAAS,CAAC;AACf,yBAAA;AAEJ,qBAAA,UAAU,SAAS,QAAQ,KAAK,IAAI,CAAC,IAAI,iBAAiB,QAAQ,YAAY;AAC1E,yBAAA;AAEJ,qBAAA,UAAU,SAAS,QAAQ,KAAK,IAAI,CAAC,IAAI,iBAAiB,QAAQ,YAAY;AAC1E,yBAAA;AAEJ,qBAAA,UAAU,SAAS,QAAQ,KAAK,IAAI,CAAC,IAAI,iBAAiB,QAAQ,YAAY;AAC1E,yBAAA;AAAA,YACb;AAAA,UAAA,OACK;AACI,qBAAA,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,GAAG;AAC1C,qBAAA,SAAS,SAAS,CAAC;AACf,yBAAA;AAEX,qBAAO,UAAU,SAAS,kBAAkB,GAAG,QAAQ,YAAY;AACxD,yBAAA;AAEX,qBAAO,UAAU,SAAS,kBAAkB,IAAI,GAAG,QAAQ,YAAY;AAC5D,yBAAA;AAEX,qBAAO,UAAU,SAAS,kBAAkB,IAAI,GAAG,QAAQ,YAAY;AAC5D,yBAAA;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAIA,2BAAmB,SAAS;AAAA,MAAA,CAC7B;AAED,eAAS,OAAO;AAAA,IAAA,OACX;AAGL,UAAI,kBAAkB;AACtB,UAAI,aAAa;AACjB,UAAI,WAAW;AAEf,WAAK,eAAe,QAAQ,SAAU,MAAM,UAAU;AAC9C,cAAA,WAAW,SAAS,aAAa,UAAU;AAC3C,cAAA,UAAU,SAAS,aAAa,QAAQ;AACxC,cAAA,MAAM,SAAS,aAAa,IAAI;AAChC,cAAA,SAAS,SAAS,aAAa,OAAO;AACtC,cAAA,UAAU,SAAS;AAEP,0BAAA,gBAAgB,KAAK,WAAW;AAGlD,iBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK;AACvC,iBAAA,IAAI,SAAS,KAAK,CAAC;AACnB,iBAAA,IAAI,SAAS,KAAK,CAAC;AACnB,iBAAA,IAAI,SAAS,KAAK,CAAC;AAEnB,iBAAA,aAAa,KAAK,WAAW;AAGpC,cAAI,OAAO,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO;AAGpD,cAAI,gBAAgB;AAClB,gBAAI,WAAW,MAAM;AACZ,qBAAA,IAAI,QAAQ,KAAK,CAAC;AAClB,qBAAA,IAAI,QAAQ,KAAK,CAAC;AAClB,qBAAA,IAAI,QAAQ,KAAK,CAAC;AAElB,qBAAA,aAAa,iBAAiB,EAAE,UAAU;AAEjD,sBAAQ,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO;AAAA,YAAA,OAClD;AACG,sBAAA;AAAA,YACV;AAAA,UACF;AAGA,cAAI,YAAY;AACd,gBAAI,OAAO,MAAM;AACP,sBAAA,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,uBACnC,YAAY;AACb,sBAAA;AAAA,YACV;AAAA,UACF;AAGA,cAAI,eAAe;AACjB,gBAAI,UAAU,MAAM;AAEhB,sBAAA,MACA,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,IAC/B,MACA,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,IAC/B,MACA,KAAK,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG;AAAA,YAAA,OAC5B;AACG,sBAAA;AAAA,YACV;AAAA,UACF;AAEA,wBAAc,OAAO;AAAA,QACvB;AAGA,YAAI,gBAAgB;AAClB,cAAI,YAAY,MAAM;AACX,qBAAA,IAAI,GAAG,IAAI,QAAQ,OAAO,IAAI,GAAG,KAAK,GAAG;AAChD,0BAAY,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI;AACvC,0BAAY,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AACtC,0BAAY,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI;AAAA;AAAA,YACxC;AAAA,UAAA,OACK;AACI,qBAAA,IAAI,GAAG,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,GAAG;AACjD,0BAAY,KAAK,kBAAkB,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,IAAI;AAAA;AAAA,YAC3F;AAAA,UACF;AAEA,uBAAa,UAAU,QAAQ,QAAQ,IAAI,SAAS,QAAQ;AAAA,QAC9D;AAEA,2BAAmB,SAAS;AAAA,MAAA,CAC7B;AAED,eAAS,GAAG,SAAS,aAAa,iBAAiB,GAAG;AAAA,IAAe;AAAA,IACvE;AAEI,QAAA,OAAO,WAAW,YAAY;AACV,4BAAA,MAAM,UAAU,OAAO,OAAO,WAAW,WAAW,SAAS,EAAE,CAAC;AAAA,IACxF;AAEO,WAAA;AAAA,EACT;AAAA;AAAA,EAGQ,eAAe,QAAkB,IAA0D;AAC1F,WAAA,SAAS,SAAU,OAAO;AAC3B,UAAA,iBAAiBF,MAAAA,QAAQ,MAAM,QAAQ;AACzC,cAAM,OAAO;AACb,cAAM,WAAW,KAAK;AAElB,YAAA,CAAC,SAAS,kBAAkB;AACxB,gBAAA,IAAI,MAAM,kEAAkE;AAAA,QACpF;AAEI,YAAA,SAAS,aAAa,UAAU,GAAG;AACrC,aAAG,MAAM,QAAQ;AAAA,QACnB;AAAA,MACF;AAAA,IAAA,CACD;AAAA,EACH;AACF;;"}