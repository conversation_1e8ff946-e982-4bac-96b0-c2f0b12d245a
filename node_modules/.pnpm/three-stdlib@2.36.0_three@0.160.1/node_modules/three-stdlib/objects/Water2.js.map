{"version": 3, "file": "Water2.js", "sources": ["../../src/objects/Water2.js"], "sourcesContent": ["import {\n  Clock,\n  Color,\n  Matrix4,\n  Mesh,\n  RepeatWrapping,\n  ShaderMaterial,\n  UniformsLib,\n  UniformsUtils,\n  Vector2,\n  Vector4,\n} from 'three'\nimport { Reflector } from './Reflector'\nimport { Refractor } from './Refractor'\nimport { version } from '../_polyfill/constants'\n\n/**\n * References:\n *\thttp://www.valvesoftware.com/publications/2010/siggraph2010_vlachos_waterflow.pdf\n * \thttp://graphicsrunner.blogspot.de/2010/08/water-using-flow-maps.html\n *\n */\n\nconst Water2 = /* @__PURE__ */ (() => {\n  class Water2 extends Mesh {\n    static WaterShader = {\n      uniforms: {\n        color: {\n          value: null,\n        },\n\n        reflectivity: {\n          value: 0,\n        },\n\n        tReflectionMap: {\n          value: null,\n        },\n\n        tRefractionMap: {\n          value: null,\n        },\n\n        tNormalMap0: {\n          value: null,\n        },\n\n        tNormalMap1: {\n          value: null,\n        },\n\n        textureMatrix: {\n          value: null,\n        },\n\n        config: {\n          value: /* @__PURE__ */ new Vector4(),\n        },\n      },\n\n      vertexShader: /* glsl */ `\n\n\t\t#include <common>\n\t\t#include <fog_pars_vertex>\n\t\t#include <logdepthbuf_pars_vertex>\n\n\t\tuniform mat4 textureMatrix;\n\n\t\tvarying vec4 vCoord;\n\t\tvarying vec2 vUv;\n\t\tvarying vec3 vToEye;\n\n\t\tvoid main() {\n\n\t\t\tvUv = uv;\n\t\t\tvCoord = textureMatrix * vec4( position, 1.0 );\n\n\t\t\tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n\t\t\tvToEye = cameraPosition - worldPosition.xyz;\n\n\t\t\tvec4 mvPosition =  viewMatrix * worldPosition; // used in fog_vertex\n\t\t\tgl_Position = projectionMatrix * mvPosition;\n\n\t\t\t#include <logdepthbuf_vertex>\n\t\t\t#include <fog_vertex>\n\n\t\t}`,\n\n      fragmentShader: /* glsl */ `\n\n\t\t#include <common>\n\t\t#include <fog_pars_fragment>\n\t\t#include <logdepthbuf_pars_fragment>\n\n\t\tuniform sampler2D tReflectionMap;\n\t\tuniform sampler2D tRefractionMap;\n\t\tuniform sampler2D tNormalMap0;\n\t\tuniform sampler2D tNormalMap1;\n\n\t\t#ifdef USE_FLOWMAP\n\t\t\tuniform sampler2D tFlowMap;\n\t\t#else\n\t\t\tuniform vec2 flowDirection;\n\t\t#endif\n\n\t\tuniform vec3 color;\n\t\tuniform float reflectivity;\n\t\tuniform vec4 config;\n\n\t\tvarying vec4 vCoord;\n\t\tvarying vec2 vUv;\n\t\tvarying vec3 vToEye;\n\n\t\tvoid main() {\n\n\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\tfloat flowMapOffset0 = config.x;\n\t\t\tfloat flowMapOffset1 = config.y;\n\t\t\tfloat halfCycle = config.z;\n\t\t\tfloat scale = config.w;\n\n\t\t\tvec3 toEye = normalize( vToEye );\n\n\t\t\t// determine flow direction\n\t\t\tvec2 flow;\n\t\t\t#ifdef USE_FLOWMAP\n\t\t\t\tflow = texture2D( tFlowMap, vUv ).rg * 2.0 - 1.0;\n\t\t\t#else\n\t\t\t\tflow = flowDirection;\n\t\t\t#endif\n\t\t\tflow.x *= - 1.0;\n\n\t\t\t// sample normal maps (distort uvs with flowdata)\n\t\t\tvec4 normalColor0 = texture2D( tNormalMap0, ( vUv * scale ) + flow * flowMapOffset0 );\n\t\t\tvec4 normalColor1 = texture2D( tNormalMap1, ( vUv * scale ) + flow * flowMapOffset1 );\n\n\t\t\t// linear interpolate to get the final normal color\n\t\t\tfloat flowLerp = abs( halfCycle - flowMapOffset0 ) / halfCycle;\n\t\t\tvec4 normalColor = mix( normalColor0, normalColor1, flowLerp );\n\n\t\t\t// calculate normal vector\n\t\t\tvec3 normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n\n\t\t\t// calculate the fresnel term to blend reflection and refraction maps\n\t\t\tfloat theta = max( dot( toEye, normal ), 0.0 );\n\t\t\tfloat reflectance = reflectivity + ( 1.0 - reflectivity ) * pow( ( 1.0 - theta ), 5.0 );\n\n\t\t\t// calculate final uv coords\n\t\t\tvec3 coord = vCoord.xyz / vCoord.w;\n\t\t\tvec2 uv = coord.xy + coord.z * normal.xz * 0.05;\n\n\t\t\tvec4 reflectColor = texture2D( tReflectionMap, vec2( 1.0 - uv.x, uv.y ) );\n\t\t\tvec4 refractColor = texture2D( tRefractionMap, uv );\n\n\t\t\t// multiply water color with the mix of both textures\n\t\t\tgl_FragColor = vec4( color, 1.0 ) * mix( refractColor, reflectColor, reflectance );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t#include <fog_fragment>\n\n\t\t}`,\n    }\n\n    constructor(geometry, options = {}) {\n      super(geometry)\n\n      this.isWater = true\n\n      this.type = 'Water'\n\n      const scope = this\n\n      const color = options.color !== undefined ? new Color(options.color) : new Color(0xffffff)\n      const textureWidth = options.textureWidth || 512\n      const textureHeight = options.textureHeight || 512\n      const clipBias = options.clipBias || 0\n      const flowDirection = options.flowDirection || new Vector2(1, 0)\n      const flowSpeed = options.flowSpeed || 0.03\n      const reflectivity = options.reflectivity || 0.02\n      const scale = options.scale || 1\n      const shader = options.shader || Water2.WaterShader\n      const encoding = options.encoding !== undefined ? options.encoding : 3000\n\n      const flowMap = options.flowMap || undefined\n      const normalMap0 = options.normalMap0\n      const normalMap1 = options.normalMap1\n\n      const cycle = 0.15 // a cycle of a flow map phase\n      const halfCycle = cycle * 0.5\n      const textureMatrix = new Matrix4()\n      const clock = new Clock()\n\n      // internal components\n\n      if (Reflector === undefined) {\n        console.error('THREE.Water: Required component Reflector not found.')\n        return\n      }\n\n      if (Refractor === undefined) {\n        console.error('THREE.Water: Required component Refractor not found.')\n        return\n      }\n\n      const reflector = new Reflector(geometry, {\n        textureWidth: textureWidth,\n        textureHeight: textureHeight,\n        clipBias: clipBias,\n        encoding: encoding,\n      })\n\n      const refractor = new Refractor(geometry, {\n        textureWidth: textureWidth,\n        textureHeight: textureHeight,\n        clipBias: clipBias,\n        encoding: encoding,\n      })\n\n      reflector.matrixAutoUpdate = false\n      refractor.matrixAutoUpdate = false\n\n      // material\n\n      this.material = new ShaderMaterial({\n        uniforms: UniformsUtils.merge([UniformsLib['fog'], shader.uniforms]),\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n        transparent: true,\n        fog: true,\n      })\n\n      if (flowMap !== undefined) {\n        this.material.defines.USE_FLOWMAP = ''\n        this.material.uniforms['tFlowMap'] = {\n          type: 't',\n          value: flowMap,\n        }\n      } else {\n        this.material.uniforms['flowDirection'] = {\n          type: 'v2',\n          value: flowDirection,\n        }\n      }\n\n      // maps\n\n      normalMap0.wrapS = normalMap0.wrapT = RepeatWrapping\n      normalMap1.wrapS = normalMap1.wrapT = RepeatWrapping\n\n      this.material.uniforms['tReflectionMap'].value = reflector.getRenderTarget().texture\n      this.material.uniforms['tRefractionMap'].value = refractor.getRenderTarget().texture\n      this.material.uniforms['tNormalMap0'].value = normalMap0\n      this.material.uniforms['tNormalMap1'].value = normalMap1\n\n      // water\n\n      this.material.uniforms['color'].value = color\n      this.material.uniforms['reflectivity'].value = reflectivity\n      this.material.uniforms['textureMatrix'].value = textureMatrix\n\n      // inital values\n\n      this.material.uniforms['config'].value.x = 0 // flowMapOffset0\n      this.material.uniforms['config'].value.y = halfCycle // flowMapOffset1\n      this.material.uniforms['config'].value.z = halfCycle // halfCycle\n      this.material.uniforms['config'].value.w = scale // scale\n\n      // functions\n\n      function updateTextureMatrix(camera) {\n        textureMatrix.set(0.5, 0.0, 0.0, 0.5, 0.0, 0.5, 0.0, 0.5, 0.0, 0.0, 0.5, 0.5, 0.0, 0.0, 0.0, 1.0)\n\n        textureMatrix.multiply(camera.projectionMatrix)\n        textureMatrix.multiply(camera.matrixWorldInverse)\n        textureMatrix.multiply(scope.matrixWorld)\n      }\n\n      function updateFlow() {\n        const delta = clock.getDelta()\n        const config = scope.material.uniforms['config']\n\n        config.value.x += flowSpeed * delta // flowMapOffset0\n        config.value.y = config.value.x + halfCycle // flowMapOffset1\n\n        // Important: The distance between offsets should be always the value of \"halfCycle\".\n        // Moreover, both offsets should be in the range of [ 0, cycle ].\n        // This approach ensures a smooth water flow and avoids \"reset\" effects.\n\n        if (config.value.x >= cycle) {\n          config.value.x = 0\n          config.value.y = halfCycle\n        } else if (config.value.y >= cycle) {\n          config.value.y = config.value.y - cycle\n        }\n      }\n\n      //\n\n      this.onBeforeRender = function (renderer, scene, camera) {\n        updateTextureMatrix(camera)\n        updateFlow()\n\n        scope.visible = false\n\n        reflector.matrixWorld.copy(scope.matrixWorld)\n        refractor.matrixWorld.copy(scope.matrixWorld)\n\n        reflector.onBeforeRender(renderer, scene, camera)\n        refractor.onBeforeRender(renderer, scene, camera)\n\n        scope.visible = true\n      }\n    }\n  }\n\n  return Water2\n})()\n\nexport { Water2 }\n"], "names": ["Water2"], "mappings": ";;;;;;;;;;AAuBK,MAAC,SAA0B,uBAAM;AACpC,QAAM,UAAN,cAAqB,KAAK;AAAA,IA6IxB,YAAY,UAAU,UAAU,IAAI;AAClC,YAAM,QAAQ;AAEd,WAAK,UAAU;AAEf,WAAK,OAAO;AAEZ,YAAM,QAAQ;AAEd,YAAM,QAAQ,QAAQ,UAAU,SAAY,IAAI,MAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,QAAQ;AACzF,YAAM,eAAe,QAAQ,gBAAgB;AAC7C,YAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,YAAM,WAAW,QAAQ,YAAY;AACrC,YAAM,gBAAgB,QAAQ,iBAAiB,IAAI,QAAQ,GAAG,CAAC;AAC/D,YAAM,YAAY,QAAQ,aAAa;AACvC,YAAM,eAAe,QAAQ,gBAAgB;AAC7C,YAAM,QAAQ,QAAQ,SAAS;AAC/B,YAAM,SAAS,QAAQ,UAAU,QAAO;AACxC,YAAM,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAErE,YAAM,UAAU,QAAQ,WAAW;AACnC,YAAM,aAAa,QAAQ;AAC3B,YAAM,aAAa,QAAQ;AAE3B,YAAM,QAAQ;AACd,YAAM,YAAY,QAAQ;AAC1B,YAAM,gBAAgB,IAAI,QAAS;AACnC,YAAM,QAAQ,IAAI,MAAO;AAIzB,UAAI,cAAc,QAAW;AAC3B,gBAAQ,MAAM,sDAAsD;AACpE;AAAA,MACD;AAED,UAAI,cAAc,QAAW;AAC3B,gBAAQ,MAAM,sDAAsD;AACpE;AAAA,MACD;AAED,YAAM,YAAY,IAAI,UAAU,UAAU;AAAA,QACxC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACR,CAAO;AAED,YAAM,YAAY,IAAI,UAAU,UAAU;AAAA,QACxC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACR,CAAO;AAED,gBAAU,mBAAmB;AAC7B,gBAAU,mBAAmB;AAI7B,WAAK,WAAW,IAAI,eAAe;AAAA,QACjC,UAAU,cAAc,MAAM,CAAC,YAAY,KAAK,GAAG,OAAO,QAAQ,CAAC;AAAA,QACnE,cAAc,OAAO;AAAA,QACrB,gBAAgB,OAAO;AAAA,QACvB,aAAa;AAAA,QACb,KAAK;AAAA,MACb,CAAO;AAED,UAAI,YAAY,QAAW;AACzB,aAAK,SAAS,QAAQ,cAAc;AACpC,aAAK,SAAS,SAAS,UAAU,IAAI;AAAA,UACnC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACT,OAAa;AACL,aAAK,SAAS,SAAS,eAAe,IAAI;AAAA,UACxC,MAAM;AAAA,UACN,OAAO;AAAA,QACR;AAAA,MACF;AAID,iBAAW,QAAQ,WAAW,QAAQ;AACtC,iBAAW,QAAQ,WAAW,QAAQ;AAEtC,WAAK,SAAS,SAAS,gBAAgB,EAAE,QAAQ,UAAU,gBAAe,EAAG;AAC7E,WAAK,SAAS,SAAS,gBAAgB,EAAE,QAAQ,UAAU,gBAAe,EAAG;AAC7E,WAAK,SAAS,SAAS,aAAa,EAAE,QAAQ;AAC9C,WAAK,SAAS,SAAS,aAAa,EAAE,QAAQ;AAI9C,WAAK,SAAS,SAAS,OAAO,EAAE,QAAQ;AACxC,WAAK,SAAS,SAAS,cAAc,EAAE,QAAQ;AAC/C,WAAK,SAAS,SAAS,eAAe,EAAE,QAAQ;AAIhD,WAAK,SAAS,SAAS,QAAQ,EAAE,MAAM,IAAI;AAC3C,WAAK,SAAS,SAAS,QAAQ,EAAE,MAAM,IAAI;AAC3C,WAAK,SAAS,SAAS,QAAQ,EAAE,MAAM,IAAI;AAC3C,WAAK,SAAS,SAAS,QAAQ,EAAE,MAAM,IAAI;AAI3C,eAAS,oBAAoB,QAAQ;AACnC,sBAAc,IAAI,KAAK,GAAK,GAAK,KAAK,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,KAAK,KAAK,GAAK,GAAK,GAAK,CAAG;AAEhG,sBAAc,SAAS,OAAO,gBAAgB;AAC9C,sBAAc,SAAS,OAAO,kBAAkB;AAChD,sBAAc,SAAS,MAAM,WAAW;AAAA,MACzC;AAED,eAAS,aAAa;AACpB,cAAM,QAAQ,MAAM,SAAU;AAC9B,cAAM,SAAS,MAAM,SAAS,SAAS,QAAQ;AAE/C,eAAO,MAAM,KAAK,YAAY;AAC9B,eAAO,MAAM,IAAI,OAAO,MAAM,IAAI;AAMlC,YAAI,OAAO,MAAM,KAAK,OAAO;AAC3B,iBAAO,MAAM,IAAI;AACjB,iBAAO,MAAM,IAAI;AAAA,QAClB,WAAU,OAAO,MAAM,KAAK,OAAO;AAClC,iBAAO,MAAM,IAAI,OAAO,MAAM,IAAI;AAAA,QACnC;AAAA,MACF;AAID,WAAK,iBAAiB,SAAU,UAAU,OAAO,QAAQ;AACvD,4BAAoB,MAAM;AAC1B,mBAAY;AAEZ,cAAM,UAAU;AAEhB,kBAAU,YAAY,KAAK,MAAM,WAAW;AAC5C,kBAAU,YAAY,KAAK,MAAM,WAAW;AAE5C,kBAAU,eAAe,UAAU,OAAO,MAAM;AAChD,kBAAU,eAAe,UAAU,OAAO,MAAM;AAEhD,cAAM,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAnSD,MAAMA,UAAN;AACE,gBADIA,SACG,eAAc;AAAA,IACnB,UAAU;AAAA,MACR,OAAO;AAAA,QACL,OAAO;AAAA,MACR;AAAA,MAED,cAAc;AAAA,QACZ,OAAO;AAAA,MACR;AAAA,MAED,gBAAgB;AAAA,QACd,OAAO;AAAA,MACR;AAAA,MAED,gBAAgB;AAAA,QACd,OAAO;AAAA,MACR;AAAA,MAED,aAAa;AAAA,QACX,OAAO;AAAA,MACR;AAAA,MAED,aAAa;AAAA,QACX,OAAO;AAAA,MACR;AAAA,MAED,eAAe;AAAA,QACb,OAAO;AAAA,MACR;AAAA,MAED,QAAQ;AAAA,QACN,OAAuB,oBAAI,QAAS;AAAA,MACrC;AAAA,IACF;AAAA,IAED;AAAA;AAAA,MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA4BzB;AAAA;AAAA,MAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAuElB,WAAW,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAInD;AA0JH,SAAOA;AACT,GAAC;"}