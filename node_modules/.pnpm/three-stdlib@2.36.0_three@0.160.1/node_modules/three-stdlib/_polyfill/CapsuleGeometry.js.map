{"version": 3, "file": "CapsuleGeometry.js", "sources": ["../../src/_polyfill/CapsuleGeometry.js"], "sourcesContent": ["import { Path, LatheGeometry } from 'three'\n\nconst CapsuleGeometry = /* @__PURE__ */ (() => {\n  class CapsuleGeometry extends LatheGeometry {\n    constructor(radius = 1, length = 1, capSegments = 4, radialSegments = 8) {\n      const path = new Path()\n      path.absarc(0, -length / 2, radius, Math.PI * 1.5, 0)\n      path.absarc(0, length / 2, radius, 0, Math.PI * 0.5)\n\n      super(path.getPoints(capSegments), radialSegments)\n\n      this.type = 'CapsuleGeometry'\n\n      this.parameters = {\n        radius: radius,\n        height: length,\n        capSegments: capSegments,\n        radialSegments: radialSegments,\n      }\n    }\n\n    static fromJSON(data) {\n      return new CapsuleGeometry(data.radius, data.length, data.capSegments, data.radialSegments)\n    }\n  }\n\n  return CapsuleGeometry\n})()\n\nexport { CapsuleGeometry }\n"], "names": ["CapsuleGeometry"], "mappings": ";AAEK,MAAC,kBAAmC,uBAAM;AAC7C,QAAMA,yBAAwB,cAAc;AAAA,IAC1C,YAAY,SAAS,GAAG,SAAS,GAAG,cAAc,GAAG,iBAAiB,GAAG;AACvE,YAAM,OAAO,IAAI,KAAM;AACvB,WAAK,OAAO,GAAG,CAAC,SAAS,GAAG,QAAQ,KAAK,KAAK,KAAK,CAAC;AACpD,WAAK,OAAO,GAAG,SAAS,GAAG,QAAQ,GAAG,KAAK,KAAK,GAAG;AAEnD,YAAM,KAAK,UAAU,WAAW,GAAG,cAAc;AAEjD,WAAK,OAAO;AAEZ,WAAK,aAAa;AAAA,QAChB;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,IAED,OAAO,SAAS,MAAM;AACpB,aAAO,IAAIA,iBAAgB,KAAK,QAAQ,KAAK,QAAQ,KAAK,aAAa,KAAK,cAAc;AAAA,IAC3F;AAAA,EACF;AAED,SAAOA;AACT,GAAC;"}