{"version": 3, "file": "RoundedBoxGeometry.js", "sources": ["../../src/geometries/RoundedBoxGeometry.js"], "sourcesContent": ["import { BoxGeometry, Vector3 } from 'three'\n\nconst tempNormal = /* @__PURE__ */ new Vector3()\n\nfunction getUv(faceDirVector, normal, uvAxis, projectionAxis, radius, sideLength) {\n  const totArcLength = (2 * Math.PI * radius) / 4\n\n  // length of the planes between the arcs on each axis\n  const centerLength = Math.max(sideLength - 2 * radius, 0)\n  const halfArc = Math.PI / 4\n\n  // Get the vector projected onto the Y plane\n  tempNormal.copy(normal)\n  tempNormal[projectionAxis] = 0\n  tempNormal.normalize()\n\n  // total amount of UV space alloted to a single arc\n  const arcUvRatio = (0.5 * totArcLength) / (totArcLength + centerLength)\n\n  // the distance along one arc the point is at\n  const arcAngleRatio = 1.0 - tempNormal.angleTo(faceDirVector) / halfArc\n\n  if (Math.sign(tempNormal[uvAxis]) === 1) {\n    return arcAngleRatio * arcUvRatio\n  } else {\n    // total amount of UV space alloted to the plane between the arcs\n    const lenUv = centerLength / (totArcLength + centerLength)\n    return lenUv + arcUvRatio + arcUvRatio * (1.0 - arcAngleRatio)\n  }\n}\n\nclass RoundedBoxGeometry extends BoxGeometry {\n  constructor(width = 1, height = 1, depth = 1, segments = 2, radius = 0.1) {\n    // ensure segments is odd so we have a plane connecting the rounded corners\n    segments = segments * 2 + 1\n\n    // ensure radius isn't bigger than shortest side\n    radius = Math.min(width / 2, height / 2, depth / 2, radius)\n\n    super(1, 1, 1, segments, segments, segments)\n\n    // if we just have one segment we're the same as a regular box\n    if (segments === 1) return\n\n    const geometry2 = this.toNonIndexed()\n\n    this.index = null\n    this.attributes.position = geometry2.attributes.position\n    this.attributes.normal = geometry2.attributes.normal\n    this.attributes.uv = geometry2.attributes.uv\n\n    //\n\n    const position = new Vector3()\n    const normal = new Vector3()\n\n    const box = new Vector3(width, height, depth).divideScalar(2).subScalar(radius)\n\n    const positions = this.attributes.position.array\n    const normals = this.attributes.normal.array\n    const uvs = this.attributes.uv.array\n\n    const faceTris = positions.length / 6\n    const faceDirVector = new Vector3()\n    const halfSegmentSize = 0.5 / segments\n\n    for (let i = 0, j = 0; i < positions.length; i += 3, j += 2) {\n      position.fromArray(positions, i)\n      normal.copy(position)\n      normal.x -= Math.sign(normal.x) * halfSegmentSize\n      normal.y -= Math.sign(normal.y) * halfSegmentSize\n      normal.z -= Math.sign(normal.z) * halfSegmentSize\n      normal.normalize()\n\n      positions[i + 0] = box.x * Math.sign(position.x) + normal.x * radius\n      positions[i + 1] = box.y * Math.sign(position.y) + normal.y * radius\n      positions[i + 2] = box.z * Math.sign(position.z) + normal.z * radius\n\n      normals[i + 0] = normal.x\n      normals[i + 1] = normal.y\n      normals[i + 2] = normal.z\n\n      const side = Math.floor(i / faceTris)\n\n      switch (side) {\n        case 0: // right\n          // generate UVs along Z then Y\n          faceDirVector.set(1, 0, 0)\n          uvs[j + 0] = getUv(faceDirVector, normal, 'z', 'y', radius, depth)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'z', radius, height)\n          break\n\n        case 1: // left\n          // generate UVs along Z then Y\n          faceDirVector.set(-1, 0, 0)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'z', 'y', radius, depth)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'z', radius, height)\n          break\n\n        case 2: // top\n          // generate UVs along X then Z\n          faceDirVector.set(0, 1, 0)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'x', 'z', radius, width)\n          uvs[j + 1] = getUv(faceDirVector, normal, 'z', 'x', radius, depth)\n          break\n\n        case 3: // bottom\n          // generate UVs along X then Z\n          faceDirVector.set(0, -1, 0)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'x', 'z', radius, width)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'z', 'x', radius, depth)\n          break\n\n        case 4: // front\n          // generate UVs along X then Y\n          faceDirVector.set(0, 0, 1)\n          uvs[j + 0] = 1.0 - getUv(faceDirVector, normal, 'x', 'y', radius, width)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'x', radius, height)\n          break\n\n        case 5: // back\n          // generate UVs along X then Y\n          faceDirVector.set(0, 0, -1)\n          uvs[j + 0] = getUv(faceDirVector, normal, 'x', 'y', radius, width)\n          uvs[j + 1] = 1.0 - getUv(faceDirVector, normal, 'y', 'x', radius, height)\n          break\n      }\n    }\n  }\n}\n\nexport { RoundedBoxGeometry }\n"], "names": [], "mappings": ";AAEA,MAAM,aAA6B,oBAAI,QAAS;AAEhD,SAAS,MAAM,eAAe,QAAQ,QAAQ,gBAAgB,QAAQ,YAAY;AAChF,QAAM,eAAgB,IAAI,KAAK,KAAK,SAAU;AAG9C,QAAM,eAAe,KAAK,IAAI,aAAa,IAAI,QAAQ,CAAC;AACxD,QAAM,UAAU,KAAK,KAAK;AAG1B,aAAW,KAAK,MAAM;AACtB,aAAW,cAAc,IAAI;AAC7B,aAAW,UAAW;AAGtB,QAAM,aAAc,MAAM,gBAAiB,eAAe;AAG1D,QAAM,gBAAgB,IAAM,WAAW,QAAQ,aAAa,IAAI;AAEhE,MAAI,KAAK,KAAK,WAAW,MAAM,CAAC,MAAM,GAAG;AACvC,WAAO,gBAAgB;AAAA,EAC3B,OAAS;AAEL,UAAM,QAAQ,gBAAgB,eAAe;AAC7C,WAAO,QAAQ,aAAa,cAAc,IAAM;AAAA,EACjD;AACH;AAEA,MAAM,2BAA2B,YAAY;AAAA,EAC3C,YAAY,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,WAAW,GAAG,SAAS,KAAK;AAExE,eAAW,WAAW,IAAI;AAG1B,aAAS,KAAK,IAAI,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,MAAM;AAE1D,UAAM,GAAG,GAAG,GAAG,UAAU,UAAU,QAAQ;AAG3C,QAAI,aAAa;AAAG;AAEpB,UAAM,YAAY,KAAK,aAAc;AAErC,SAAK,QAAQ;AACb,SAAK,WAAW,WAAW,UAAU,WAAW;AAChD,SAAK,WAAW,SAAS,UAAU,WAAW;AAC9C,SAAK,WAAW,KAAK,UAAU,WAAW;AAI1C,UAAM,WAAW,IAAI,QAAS;AAC9B,UAAM,SAAS,IAAI,QAAS;AAE5B,UAAM,MAAM,IAAI,QAAQ,OAAO,QAAQ,KAAK,EAAE,aAAa,CAAC,EAAE,UAAU,MAAM;AAE9E,UAAM,YAAY,KAAK,WAAW,SAAS;AAC3C,UAAM,UAAU,KAAK,WAAW,OAAO;AACvC,UAAM,MAAM,KAAK,WAAW,GAAG;AAE/B,UAAM,WAAW,UAAU,SAAS;AACpC,UAAM,gBAAgB,IAAI,QAAS;AACnC,UAAM,kBAAkB,MAAM;AAE9B,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,GAAG,KAAK,GAAG;AAC3D,eAAS,UAAU,WAAW,CAAC;AAC/B,aAAO,KAAK,QAAQ;AACpB,aAAO,KAAK,KAAK,KAAK,OAAO,CAAC,IAAI;AAClC,aAAO,KAAK,KAAK,KAAK,OAAO,CAAC,IAAI;AAClC,aAAO,KAAK,KAAK,KAAK,OAAO,CAAC,IAAI;AAClC,aAAO,UAAW;AAElB,gBAAU,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,IAAI,OAAO,IAAI;AAC9D,gBAAU,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,IAAI,OAAO,IAAI;AAC9D,gBAAU,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,IAAI,OAAO,IAAI;AAE9D,cAAQ,IAAI,CAAC,IAAI,OAAO;AACxB,cAAQ,IAAI,CAAC,IAAI,OAAO;AACxB,cAAQ,IAAI,CAAC,IAAI,OAAO;AAExB,YAAM,OAAO,KAAK,MAAM,IAAI,QAAQ;AAEpC,cAAQ,MAAI;AAAA,QACV,KAAK;AAEH,wBAAc,IAAI,GAAG,GAAG,CAAC;AACzB,cAAI,IAAI,CAAC,IAAI,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACjE,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,MAAM;AACxE;AAAA,QAEF,KAAK;AAEH,wBAAc,IAAI,IAAI,GAAG,CAAC;AAC1B,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACvE,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,MAAM;AACxE;AAAA,QAEF,KAAK;AAEH,wBAAc,IAAI,GAAG,GAAG,CAAC;AACzB,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACvE,cAAI,IAAI,CAAC,IAAI,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACjE;AAAA,QAEF,KAAK;AAEH,wBAAc,IAAI,GAAG,IAAI,CAAC;AAC1B,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACvE,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACvE;AAAA,QAEF,KAAK;AAEH,wBAAc,IAAI,GAAG,GAAG,CAAC;AACzB,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACvE,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,MAAM;AACxE;AAAA,QAEF,KAAK;AAEH,wBAAc,IAAI,GAAG,GAAG,EAAE;AAC1B,cAAI,IAAI,CAAC,IAAI,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,KAAK;AACjE,cAAI,IAAI,CAAC,IAAI,IAAM,MAAM,eAAe,QAAQ,KAAK,KAAK,QAAQ,MAAM;AACxE;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACH;"}