{"version": 3, "file": "RectAreaLightHelper.js", "sources": ["../../src/helpers/RectAreaLightHelper.js"], "sourcesContent": ["import {\n  BackSide,\n  BufferGeometry,\n  Float32BufferAttribute,\n  Line,\n  LineBasicMaterial,\n  Mesh,\n  MeshBasicMaterial,\n} from 'three'\n\n/**\n *  This helper must be added as a child of the light\n */\n\nclass RectAreaLightHelper extends Line {\n  constructor(light, color) {\n    const positions = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 1, 1, 0]\n\n    const geometry = new BufferGeometry()\n    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    geometry.computeBoundingSphere()\n\n    const material = new LineBasicMaterial({ fog: false })\n\n    super(geometry, material)\n\n    this.light = light\n    this.color = color // optional hardwired color for the helper\n    this.type = 'RectAreaLightHelper'\n\n    //\n\n    const positions2 = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0]\n\n    const geometry2 = new BufferGeometry()\n    geometry2.setAttribute('position', new Float32BufferAttribute(positions2, 3))\n    geometry2.computeBoundingSphere()\n\n    this.add(new Mesh(geometry2, new MeshBasicMaterial({ side: BackSide, fog: false })))\n  }\n\n  updateMatrixWorld() {\n    this.scale.set(0.5 * this.light.width, 0.5 * this.light.height, 1)\n\n    if (this.color !== undefined) {\n      this.material.color.set(this.color)\n      this.children[0].material.color.set(this.color)\n    } else {\n      this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity)\n\n      // prevent hue shift\n      const c = this.material.color\n      const max = Math.max(c.r, c.g, c.b)\n      if (max > 1) c.multiplyScalar(1 / max)\n\n      this.children[0].material.color.copy(this.material.color)\n    }\n\n    // ignore world scale on light\n    this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld)\n\n    this.children[0].matrixWorld.copy(this.matrixWorld)\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n    this.children[0].geometry.dispose()\n    this.children[0].material.dispose()\n  }\n}\n\nexport { RectAreaLightHelper }\n"], "names": [], "mappings": ";AAcA,MAAM,4BAA4B,KAAK;AAAA,EACrC,YAAY,OAAO,OAAO;AACxB,UAAM,YAAY,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AAElE,UAAM,WAAW,IAAI,eAAgB;AACrC,aAAS,aAAa,YAAY,IAAI,uBAAuB,WAAW,CAAC,CAAC;AAC1E,aAAS,sBAAuB;AAEhC,UAAM,WAAW,IAAI,kBAAkB,EAAE,KAAK,MAAK,CAAE;AAErD,UAAM,UAAU,QAAQ;AAExB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AAIZ,UAAM,aAAa,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC;AAE9E,UAAM,YAAY,IAAI,eAAgB;AACtC,cAAU,aAAa,YAAY,IAAI,uBAAuB,YAAY,CAAC,CAAC;AAC5E,cAAU,sBAAuB;AAEjC,SAAK,IAAI,IAAI,KAAK,WAAW,IAAI,kBAAkB,EAAE,MAAM,UAAU,KAAK,MAAK,CAAE,CAAC,CAAC;AAAA,EACpF;AAAA,EAED,oBAAoB;AAClB,SAAK,MAAM,IAAI,MAAM,KAAK,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQ,CAAC;AAEjE,QAAI,KAAK,UAAU,QAAW;AAC5B,WAAK,SAAS,MAAM,IAAI,KAAK,KAAK;AAClC,WAAK,SAAS,CAAC,EAAE,SAAS,MAAM,IAAI,KAAK,KAAK;AAAA,IACpD,OAAW;AACL,WAAK,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,eAAe,KAAK,MAAM,SAAS;AAG9E,YAAM,IAAI,KAAK,SAAS;AACxB,YAAM,MAAM,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClC,UAAI,MAAM;AAAG,UAAE,eAAe,IAAI,GAAG;AAErC,WAAK,SAAS,CAAC,EAAE,SAAS,MAAM,KAAK,KAAK,SAAS,KAAK;AAAA,IACzD;AAGD,SAAK,YAAY,gBAAgB,KAAK,MAAM,WAAW,EAAE,MAAM,KAAK,KAAK,EAAE,aAAa,KAAK,MAAM,WAAW;AAE9G,SAAK,SAAS,CAAC,EAAE,YAAY,KAAK,KAAK,WAAW;AAAA,EACnD;AAAA,EAED,UAAU;AACR,SAAK,SAAS,QAAS;AACvB,SAAK,SAAS,QAAS;AACvB,SAAK,SAAS,CAAC,EAAE,SAAS,QAAS;AACnC,SAAK,SAAS,CAAC,EAAE,SAAS,QAAS;AAAA,EACpC;AACH;"}