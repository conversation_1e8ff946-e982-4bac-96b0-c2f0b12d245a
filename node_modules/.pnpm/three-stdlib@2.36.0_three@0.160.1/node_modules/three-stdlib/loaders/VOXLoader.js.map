{"version": 3, "file": "VOXLoader.js", "sources": ["../../src/loaders/VOXLoader.js"], "sourcesContent": ["import {\n  <PERSON>ufferGeometry,\n  FileLoader,\n  Float32BufferAttribute,\n  Loader,\n  LinearFilter,\n  Mesh,\n  MeshStandardMaterial,\n  NearestFilter,\n  RedFormat,\n} from 'three'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nclass VOXLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer) {\n    const data = new DataView(buffer)\n\n    const id = data.getUint32(0, true)\n    const version = data.getUint32(4, true)\n\n    if (id !== 542658390 || version !== 150) {\n      console.error('Not a valid VOX file')\n      return\n    }\n\n    const DEFAULT_PALETTE = [\n      0x00000000,\n      0xffffffff,\n      0xffccffff,\n      0xff99ffff,\n      0xff66ffff,\n      0xff33ffff,\n      0xff00ffff,\n      0xffffccff,\n      0xffccccff,\n      0xff99ccff,\n      0xff66ccff,\n      0xff33ccff,\n      0xff00ccff,\n      0xffff99ff,\n      0xffcc99ff,\n      0xff9999ff,\n      0xff6699ff,\n      0xff3399ff,\n      0xff0099ff,\n      0xffff66ff,\n      0xffcc66ff,\n      0xff9966ff,\n      0xff6666ff,\n      0xff3366ff,\n      0xff0066ff,\n      0xffff33ff,\n      0xffcc33ff,\n      0xff9933ff,\n      0xff6633ff,\n      0xff3333ff,\n      0xff0033ff,\n      0xffff00ff,\n      0xffcc00ff,\n      0xff9900ff,\n      0xff6600ff,\n      0xff3300ff,\n      0xff0000ff,\n      0xffffffcc,\n      0xffccffcc,\n      0xff99ffcc,\n      0xff66ffcc,\n      0xff33ffcc,\n      0xff00ffcc,\n      0xffffcccc,\n      0xffcccccc,\n      0xff99cccc,\n      0xff66cccc,\n      0xff33cccc,\n      0xff00cccc,\n      0xffff99cc,\n      0xffcc99cc,\n      0xff9999cc,\n      0xff6699cc,\n      0xff3399cc,\n      0xff0099cc,\n      0xffff66cc,\n      0xffcc66cc,\n      0xff9966cc,\n      0xff6666cc,\n      0xff3366cc,\n      0xff0066cc,\n      0xffff33cc,\n      0xffcc33cc,\n      0xff9933cc,\n      0xff6633cc,\n      0xff3333cc,\n      0xff0033cc,\n      0xffff00cc,\n      0xffcc00cc,\n      0xff9900cc,\n      0xff6600cc,\n      0xff3300cc,\n      0xff0000cc,\n      0xffffff99,\n      0xffccff99,\n      0xff99ff99,\n      0xff66ff99,\n      0xff33ff99,\n      0xff00ff99,\n      0xffffcc99,\n      0xffcccc99,\n      0xff99cc99,\n      0xff66cc99,\n      0xff33cc99,\n      0xff00cc99,\n      0xffff9999,\n      0xffcc9999,\n      0xff999999,\n      0xff669999,\n      0xff339999,\n      0xff009999,\n      0xffff6699,\n      0xffcc6699,\n      0xff996699,\n      0xff666699,\n      0xff336699,\n      0xff006699,\n      0xffff3399,\n      0xffcc3399,\n      0xff993399,\n      0xff663399,\n      0xff333399,\n      0xff003399,\n      0xffff0099,\n      0xffcc0099,\n      0xff990099,\n      0xff660099,\n      0xff330099,\n      0xff000099,\n      0xffffff66,\n      0xffccff66,\n      0xff99ff66,\n      0xff66ff66,\n      0xff33ff66,\n      0xff00ff66,\n      0xffffcc66,\n      0xffcccc66,\n      0xff99cc66,\n      0xff66cc66,\n      0xff33cc66,\n      0xff00cc66,\n      0xffff9966,\n      0xffcc9966,\n      0xff999966,\n      0xff669966,\n      0xff339966,\n      0xff009966,\n      0xffff6666,\n      0xffcc6666,\n      0xff996666,\n      0xff666666,\n      0xff336666,\n      0xff006666,\n      0xffff3366,\n      0xffcc3366,\n      0xff993366,\n      0xff663366,\n      0xff333366,\n      0xff003366,\n      0xffff0066,\n      0xffcc0066,\n      0xff990066,\n      0xff660066,\n      0xff330066,\n      0xff000066,\n      0xffffff33,\n      0xffccff33,\n      0xff99ff33,\n      0xff66ff33,\n      0xff33ff33,\n      0xff00ff33,\n      0xffffcc33,\n      0xffcccc33,\n      0xff99cc33,\n      0xff66cc33,\n      0xff33cc33,\n      0xff00cc33,\n      0xffff9933,\n      0xffcc9933,\n      0xff999933,\n      0xff669933,\n      0xff339933,\n      0xff009933,\n      0xffff6633,\n      0xffcc6633,\n      0xff996633,\n      0xff666633,\n      0xff336633,\n      0xff006633,\n      0xffff3333,\n      0xffcc3333,\n      0xff993333,\n      0xff663333,\n      0xff333333,\n      0xff003333,\n      0xffff0033,\n      0xffcc0033,\n      0xff990033,\n      0xff660033,\n      0xff330033,\n      0xff000033,\n      0xffffff00,\n      0xffccff00,\n      0xff99ff00,\n      0xff66ff00,\n      0xff33ff00,\n      0xff00ff00,\n      0xffffcc00,\n      0xffcccc00,\n      0xff99cc00,\n      0xff66cc00,\n      0xff33cc00,\n      0xff00cc00,\n      0xffff9900,\n      0xffcc9900,\n      0xff999900,\n      0xff669900,\n      0xff339900,\n      0xff009900,\n      0xffff6600,\n      0xffcc6600,\n      0xff996600,\n      0xff666600,\n      0xff336600,\n      0xff006600,\n      0xffff3300,\n      0xffcc3300,\n      0xff993300,\n      0xff663300,\n      0xff333300,\n      0xff003300,\n      0xffff0000,\n      0xffcc0000,\n      0xff990000,\n      0xff660000,\n      0xff330000,\n      0xff0000ee,\n      0xff0000dd,\n      0xff0000bb,\n      0xff0000aa,\n      0xff000088,\n      0xff000077,\n      0xff000055,\n      0xff000044,\n      0xff000022,\n      0xff000011,\n      0xff00ee00,\n      0xff00dd00,\n      0xff00bb00,\n      0xff00aa00,\n      0xff008800,\n      0xff007700,\n      0xff005500,\n      0xff004400,\n      0xff002200,\n      0xff001100,\n      0xffee0000,\n      0xffdd0000,\n      0xffbb0000,\n      0xffaa0000,\n      0xff880000,\n      0xff770000,\n      0xff550000,\n      0xff440000,\n      0xff220000,\n      0xff110000,\n      0xffeeeeee,\n      0xffdddddd,\n      0xffbbbbbb,\n      0xffaaaaaa,\n      0xff888888,\n      0xff777777,\n      0xff555555,\n      0xff444444,\n      0xff222222,\n      0xff111111,\n    ]\n\n    let i = 8\n\n    let chunk\n    const chunks = []\n\n    while (i < data.byteLength) {\n      let id = ''\n\n      for (let j = 0; j < 4; j++) {\n        id += String.fromCharCode(data.getUint8(i++))\n      }\n\n      const chunkSize = data.getUint32(i, true)\n      i += 4\n      i += 4 // childChunks\n\n      if (id === 'SIZE') {\n        const x = data.getUint32(i, true)\n        i += 4\n        const y = data.getUint32(i, true)\n        i += 4\n        const z = data.getUint32(i, true)\n        i += 4\n\n        chunk = {\n          palette: DEFAULT_PALETTE,\n          size: { x: x, y: y, z: z },\n        }\n\n        chunks.push(chunk)\n\n        i += chunkSize - 3 * 4\n      } else if (id === 'XYZI') {\n        const numVoxels = data.getUint32(i, true)\n        i += 4\n        chunk.data = new Uint8Array(buffer, i, numVoxels * 4)\n\n        i += numVoxels * 4\n      } else if (id === 'RGBA') {\n        const palette = [0]\n\n        for (let j = 0; j < 256; j++) {\n          palette[j + 1] = data.getUint32(i, true)\n          i += 4\n        }\n\n        chunk.palette = palette\n      } else {\n        // console.log( id, chunkSize, childChunks );\n\n        i += chunkSize\n      }\n    }\n\n    return chunks\n  }\n}\n\nclass VOXMesh extends Mesh {\n  constructor(chunk) {\n    const data = chunk.data\n    const size = chunk.size\n    const palette = chunk.palette\n\n    //\n\n    const vertices = []\n    const colors = []\n\n    const nx = [0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1]\n    const px = [1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0]\n    const py = [0, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1]\n    const ny = [0, 0, 0, 0, 1, 0, 1, 0, 0, 1, 1, 0, 1, 0, 0, 0, 1, 0]\n    const nz = [0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0, 0]\n    const pz = [0, 1, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 1, 1]\n\n    function add(tile, x, y, z, r, g, b) {\n      x -= size.x / 2\n      y -= size.z / 2\n      z += size.y / 2\n\n      for (let i = 0; i < 18; i += 3) {\n        vertices.push(tile[i + 0] + x, tile[i + 1] + y, tile[i + 2] + z)\n        colors.push(r, g, b)\n      }\n    }\n\n    // Store data in a volume for sampling\n\n    const offsety = size.x\n    const offsetz = size.x * size.y\n\n    const array = new Uint8Array(size.x * size.y * size.z)\n\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0]\n      const y = data[j + 1]\n      const z = data[j + 2]\n\n      const index = x + y * offsety + z * offsetz\n\n      array[index] = 255\n    }\n\n    // Construct geometry\n\n    let hasColors = false\n\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0]\n      const y = data[j + 1]\n      const z = data[j + 2]\n      const c = data[j + 3]\n\n      const hex = palette[c]\n      const r = ((hex >> 0) & 0xff) / 0xff\n      const g = ((hex >> 8) & 0xff) / 0xff\n      const b = ((hex >> 16) & 0xff) / 0xff\n\n      if (r > 0 || g > 0 || b > 0) hasColors = true\n\n      const index = x + y * offsety + z * offsetz\n\n      if (array[index + 1] === 0 || x === size.x - 1) add(px, x, z, -y, r, g, b)\n      if (array[index - 1] === 0 || x === 0) add(nx, x, z, -y, r, g, b)\n      if (array[index + offsety] === 0 || y === size.y - 1) add(ny, x, z, -y, r, g, b)\n      if (array[index - offsety] === 0 || y === 0) add(py, x, z, -y, r, g, b)\n      if (array[index + offsetz] === 0 || z === size.z - 1) add(pz, x, z, -y, r, g, b)\n      if (array[index - offsetz] === 0 || z === 0) add(nz, x, z, -y, r, g, b)\n    }\n\n    const geometry = new BufferGeometry()\n    geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    geometry.computeVertexNormals()\n\n    const material = new MeshStandardMaterial()\n\n    if (hasColors) {\n      geometry.setAttribute('color', new Float32BufferAttribute(colors, 3))\n      material.vertexColors = true\n    }\n\n    super(geometry, material)\n  }\n}\n\nclass VOXData3DTexture extends Data3DTexture {\n  constructor(chunk) {\n    const data = chunk.data\n    const size = chunk.size\n\n    const offsety = size.x\n    const offsetz = size.x * size.y\n\n    const array = new Uint8Array(size.x * size.y * size.z)\n\n    for (let j = 0; j < data.length; j += 4) {\n      const x = data[j + 0]\n      const y = data[j + 1]\n      const z = data[j + 2]\n\n      const index = x + y * offsety + z * offsetz\n\n      array[index] = 255\n    }\n\n    super(array, size.x, size.y, size.z)\n\n    this.format = RedFormat\n    this.minFilter = NearestFilter\n    this.magFilter = LinearFilter\n    this.unpackAlignment = 1\n    this.needsUpdate = true\n  }\n}\n\nexport { VOXLoader, VOXMesh, VOXData3DTexture }\n"], "names": ["id"], "mappings": ";;AAaA,MAAM,kBAAkB,OAAO;AAAA,EAC7B,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,QAAQ;AAChB,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,CAAC;AAAA,QAC3B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,QAAQ;AACZ,UAAM,OAAO,IAAI,SAAS,MAAM;AAEhC,UAAM,KAAK,KAAK,UAAU,GAAG,IAAI;AACjC,UAAM,UAAU,KAAK,UAAU,GAAG,IAAI;AAEtC,QAAI,OAAO,aAAa,YAAY,KAAK;AACvC,cAAQ,MAAM,sBAAsB;AACpC;AAAA,IACD;AAED,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAED,QAAI,IAAI;AAER,QAAI;AACJ,UAAM,SAAS,CAAE;AAEjB,WAAO,IAAI,KAAK,YAAY;AAC1B,UAAIA,MAAK;AAET,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAAA,OAAM,OAAO,aAAa,KAAK,SAAS,GAAG,CAAC;AAAA,MAC7C;AAED,YAAM,YAAY,KAAK,UAAU,GAAG,IAAI;AACxC,WAAK;AACL,WAAK;AAEL,UAAIA,QAAO,QAAQ;AACjB,cAAM,IAAI,KAAK,UAAU,GAAG,IAAI;AAChC,aAAK;AACL,cAAM,IAAI,KAAK,UAAU,GAAG,IAAI;AAChC,aAAK;AACL,cAAM,IAAI,KAAK,UAAU,GAAG,IAAI;AAChC,aAAK;AAEL,gBAAQ;AAAA,UACN,SAAS;AAAA,UACT,MAAM,EAAE,GAAM,GAAM,EAAM;AAAA,QAC3B;AAED,eAAO,KAAK,KAAK;AAEjB,aAAK,YAAY,IAAI;AAAA,MAC7B,WAAiBA,QAAO,QAAQ;AACxB,cAAM,YAAY,KAAK,UAAU,GAAG,IAAI;AACxC,aAAK;AACL,cAAM,OAAO,IAAI,WAAW,QAAQ,GAAG,YAAY,CAAC;AAEpD,aAAK,YAAY;AAAA,MACzB,WAAiBA,QAAO,QAAQ;AACxB,cAAM,UAAU,CAAC,CAAC;AAElB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAQ,IAAI,CAAC,IAAI,KAAK,UAAU,GAAG,IAAI;AACvC,eAAK;AAAA,QACN;AAED,cAAM,UAAU;AAAA,MACxB,OAAa;AAGL,aAAK;AAAA,MACN;AAAA,IACF;AAED,WAAO;AAAA,EACR;AACH;AAEA,MAAM,gBAAgB,KAAK;AAAA,EACzB,YAAY,OAAO;AACjB,UAAM,OAAO,MAAM;AACnB,UAAM,OAAO,MAAM;AACnB,UAAM,UAAU,MAAM;AAItB,UAAM,WAAW,CAAE;AACnB,UAAM,SAAS,CAAE;AAEjB,UAAM,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChE,UAAM,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChE,UAAM,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChE,UAAM,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChE,UAAM,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChE,UAAM,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEhE,aAAS,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACnC,WAAK,KAAK,IAAI;AACd,WAAK,KAAK,IAAI;AACd,WAAK,KAAK,IAAI;AAEd,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,iBAAS,KAAK,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC;AAC/D,eAAO,KAAK,GAAG,GAAG,CAAC;AAAA,MACpB;AAAA,IACF;AAID,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK,IAAI,KAAK;AAE9B,UAAM,QAAQ,IAAI,WAAW,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAErD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AAEpB,YAAM,QAAQ,IAAI,IAAI,UAAU,IAAI;AAEpC,YAAM,KAAK,IAAI;AAAA,IAChB;AAID,QAAI,YAAY;AAEhB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AAEpB,YAAM,MAAM,QAAQ,CAAC;AACrB,YAAM,KAAM,OAAO,IAAK,OAAQ;AAChC,YAAM,KAAM,OAAO,IAAK,OAAQ;AAChC,YAAM,KAAM,OAAO,KAAM,OAAQ;AAEjC,UAAI,IAAI,KAAK,IAAI,KAAK,IAAI;AAAG,oBAAY;AAEzC,YAAM,QAAQ,IAAI,IAAI,UAAU,IAAI;AAEpC,UAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,IAAI;AAAG,YAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AACzE,UAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,MAAM;AAAG,YAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AAChE,UAAI,MAAM,QAAQ,OAAO,MAAM,KAAK,MAAM,KAAK,IAAI;AAAG,YAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AAC/E,UAAI,MAAM,QAAQ,OAAO,MAAM,KAAK,MAAM;AAAG,YAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AACtE,UAAI,MAAM,QAAQ,OAAO,MAAM,KAAK,MAAM,KAAK,IAAI;AAAG,YAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AAC/E,UAAI,MAAM,QAAQ,OAAO,MAAM,KAAK,MAAM;AAAG,YAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACvE;AAED,UAAM,WAAW,IAAI,eAAgB;AACrC,aAAS,aAAa,YAAY,IAAI,uBAAuB,UAAU,CAAC,CAAC;AACzE,aAAS,qBAAsB;AAE/B,UAAM,WAAW,IAAI,qBAAsB;AAE3C,QAAI,WAAW;AACb,eAAS,aAAa,SAAS,IAAI,uBAAuB,QAAQ,CAAC,CAAC;AACpE,eAAS,eAAe;AAAA,IACzB;AAED,UAAM,UAAU,QAAQ;AAAA,EACzB;AACH;AAEA,MAAM,yBAAyB,cAAc;AAAA,EAC3C,YAAY,OAAO;AACjB,UAAM,OAAO,MAAM;AACnB,UAAM,OAAO,MAAM;AAEnB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,KAAK,IAAI,KAAK;AAE9B,UAAM,QAAQ,IAAI,WAAW,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAErD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,KAAK,IAAI,CAAC;AAEpB,YAAM,QAAQ,IAAI,IAAI,UAAU,IAAI;AAEpC,YAAM,KAAK,IAAI;AAAA,IAChB;AAED,UAAM,OAAO,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAEnC,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AAAA,EACpB;AACH;"}