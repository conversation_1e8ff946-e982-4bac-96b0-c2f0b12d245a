{"version": 3, "file": "ColladaLoader.js", "sources": ["../../src/loaders/ColladaLoader.js"], "sourcesContent": ["import {\n  AmbientLight,\n  AnimationClip,\n  Bone,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  DirectionalLight,\n  DoubleSide,\n  Euler,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Group,\n  Line,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  LoaderUtils,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PointLight,\n  Quaternion,\n  QuaternionKeyframeTrack,\n  RepeatWrapping,\n  Scene,\n  Skeleton,\n  SkinnedMesh,\n  SpotLight,\n  TextureLoader,\n  Vector2,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\nimport { TGALoader } from '../loaders/TGALoader'\nimport { UV1 } from '../_polyfill/uv1'\n\nclass ColladaLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(text, path) {\n    function getElementsByTagName(xml, name) {\n      // Non recursive xml.getElementsByTagName() ...\n\n      const array = []\n      const childNodes = xml.childNodes\n\n      for (let i = 0, l = childNodes.length; i < l; i++) {\n        const child = childNodes[i]\n\n        if (child.nodeName === name) {\n          array.push(child)\n        }\n      }\n\n      return array\n    }\n\n    function parseStrings(text) {\n      if (text.length === 0) return []\n\n      const parts = text.trim().split(/\\s+/)\n      const array = new Array(parts.length)\n\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parts[i]\n      }\n\n      return array\n    }\n\n    function parseFloats(text) {\n      if (text.length === 0) return []\n\n      const parts = text.trim().split(/\\s+/)\n      const array = new Array(parts.length)\n\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parseFloat(parts[i])\n      }\n\n      return array\n    }\n\n    function parseInts(text) {\n      if (text.length === 0) return []\n\n      const parts = text.trim().split(/\\s+/)\n      const array = new Array(parts.length)\n\n      for (let i = 0, l = parts.length; i < l; i++) {\n        array[i] = parseInt(parts[i])\n      }\n\n      return array\n    }\n\n    function parseId(text) {\n      return text.substring(1)\n    }\n\n    function generateId() {\n      return 'three_default_' + count++\n    }\n\n    function isEmpty(object) {\n      return Object.keys(object).length === 0\n    }\n\n    // asset\n\n    function parseAsset(xml) {\n      return {\n        unit: parseAssetUnit(getElementsByTagName(xml, 'unit')[0]),\n        upAxis: parseAssetUpAxis(getElementsByTagName(xml, 'up_axis')[0]),\n      }\n    }\n\n    function parseAssetUnit(xml) {\n      if (xml !== undefined && xml.hasAttribute('meter') === true) {\n        return parseFloat(xml.getAttribute('meter'))\n      } else {\n        return 1 // default 1 meter\n      }\n    }\n\n    function parseAssetUpAxis(xml) {\n      return xml !== undefined ? xml.textContent : 'Y_UP'\n    }\n\n    // library\n\n    function parseLibrary(xml, libraryName, nodeName, parser) {\n      const library = getElementsByTagName(xml, libraryName)[0]\n\n      if (library !== undefined) {\n        const elements = getElementsByTagName(library, nodeName)\n\n        for (let i = 0; i < elements.length; i++) {\n          parser(elements[i])\n        }\n      }\n    }\n\n    function buildLibrary(data, builder) {\n      for (const name in data) {\n        const object = data[name]\n        object.build = builder(data[name])\n      }\n    }\n\n    // get\n\n    function getBuild(data, builder) {\n      if (data.build !== undefined) return data.build\n\n      data.build = builder(data)\n\n      return data.build\n    }\n\n    // animation\n\n    function parseAnimation(xml) {\n      const data = {\n        sources: {},\n        samplers: {},\n        channels: {},\n      }\n\n      let hasChildren = false\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        let id\n\n        switch (child.nodeName) {\n          case 'source':\n            id = child.getAttribute('id')\n            data.sources[id] = parseSource(child)\n            break\n\n          case 'sampler':\n            id = child.getAttribute('id')\n            data.samplers[id] = parseAnimationSampler(child)\n            break\n\n          case 'channel':\n            id = child.getAttribute('target')\n            data.channels[id] = parseAnimationChannel(child)\n            break\n\n          case 'animation':\n            // hierarchy of related animations\n            parseAnimation(child)\n            hasChildren = true\n            break\n\n          default:\n            console.log(child)\n        }\n      }\n\n      if (hasChildren === false) {\n        // since 'id' attributes can be optional, it's necessary to generate a UUID for unqiue assignment\n\n        library.animations[xml.getAttribute('id') || MathUtils.generateUUID()] = data\n      }\n    }\n\n    function parseAnimationSampler(xml) {\n      const data = {\n        inputs: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const id = parseId(child.getAttribute('source'))\n            const semantic = child.getAttribute('semantic')\n            data.inputs[semantic] = id\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseAnimationChannel(xml) {\n      const data = {}\n\n      const target = xml.getAttribute('target')\n\n      // parsing SID Addressing Syntax\n\n      let parts = target.split('/')\n\n      const id = parts.shift()\n      let sid = parts.shift()\n\n      // check selection syntax\n\n      const arraySyntax = sid.indexOf('(') !== -1\n      const memberSyntax = sid.indexOf('.') !== -1\n\n      if (memberSyntax) {\n        //  member selection access\n\n        parts = sid.split('.')\n        sid = parts.shift()\n        data.member = parts.shift()\n      } else if (arraySyntax) {\n        // array-access syntax. can be used to express fields in one-dimensional vectors or two-dimensional matrices.\n\n        const indices = sid.split('(')\n        sid = indices.shift()\n\n        for (let i = 0; i < indices.length; i++) {\n          indices[i] = parseInt(indices[i].replace(/\\)/, ''))\n        }\n\n        data.indices = indices\n      }\n\n      data.id = id\n      data.sid = sid\n\n      data.arraySyntax = arraySyntax\n      data.memberSyntax = memberSyntax\n\n      data.sampler = parseId(xml.getAttribute('source'))\n\n      return data\n    }\n\n    function buildAnimation(data) {\n      const tracks = []\n\n      const channels = data.channels\n      const samplers = data.samplers\n      const sources = data.sources\n\n      for (const target in channels) {\n        if (channels.hasOwnProperty(target)) {\n          const channel = channels[target]\n          const sampler = samplers[channel.sampler]\n\n          const inputId = sampler.inputs.INPUT\n          const outputId = sampler.inputs.OUTPUT\n\n          const inputSource = sources[inputId]\n          const outputSource = sources[outputId]\n\n          const animation = buildAnimationChannel(channel, inputSource, outputSource)\n\n          createKeyframeTracks(animation, tracks)\n        }\n      }\n\n      return tracks\n    }\n\n    function getAnimation(id) {\n      return getBuild(library.animations[id], buildAnimation)\n    }\n\n    function buildAnimationChannel(channel, inputSource, outputSource) {\n      const node = library.nodes[channel.id]\n      const object3D = getNode(node.id)\n\n      const transform = node.transforms[channel.sid]\n      const defaultMatrix = node.matrix.clone().transpose()\n\n      let time, stride\n      let i, il, j, jl\n\n      const data = {}\n\n      // the collada spec allows the animation of data in various ways.\n      // depending on the transform type (matrix, translate, rotate, scale), we execute different logic\n\n      switch (transform) {\n        case 'matrix':\n          for (i = 0, il = inputSource.array.length; i < il; i++) {\n            time = inputSource.array[i]\n            stride = i * outputSource.stride\n\n            if (data[time] === undefined) data[time] = {}\n\n            if (channel.arraySyntax === true) {\n              const value = outputSource.array[stride]\n              const index = channel.indices[0] + 4 * channel.indices[1]\n\n              data[time][index] = value\n            } else {\n              for (j = 0, jl = outputSource.stride; j < jl; j++) {\n                data[time][j] = outputSource.array[stride + j]\n              }\n            }\n          }\n\n          break\n\n        case 'translate':\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform)\n          break\n\n        case 'rotate':\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform)\n          break\n\n        case 'scale':\n          console.warn('THREE.ColladaLoader: Animation transform type \"%s\" not yet implemented.', transform)\n          break\n      }\n\n      const keyframes = prepareAnimationData(data, defaultMatrix)\n\n      const animation = {\n        name: object3D.uuid,\n        keyframes: keyframes,\n      }\n\n      return animation\n    }\n\n    function prepareAnimationData(data, defaultMatrix) {\n      const keyframes = []\n\n      // transfer data into a sortable array\n\n      for (const time in data) {\n        keyframes.push({ time: parseFloat(time), value: data[time] })\n      }\n\n      // ensure keyframes are sorted by time\n\n      keyframes.sort(ascending)\n\n      // now we clean up all animation data, so we can use them for keyframe tracks\n\n      for (let i = 0; i < 16; i++) {\n        transformAnimationData(keyframes, i, defaultMatrix.elements[i])\n      }\n\n      return keyframes\n\n      // array sort function\n\n      function ascending(a, b) {\n        return a.time - b.time\n      }\n    }\n\n    const position = new Vector3()\n    const scale = new Vector3()\n    const quaternion = new Quaternion()\n\n    function createKeyframeTracks(animation, tracks) {\n      const keyframes = animation.keyframes\n      const name = animation.name\n\n      const times = []\n      const positionData = []\n      const quaternionData = []\n      const scaleData = []\n\n      for (let i = 0, l = keyframes.length; i < l; i++) {\n        const keyframe = keyframes[i]\n\n        const time = keyframe.time\n        const value = keyframe.value\n\n        matrix.fromArray(value).transpose()\n        matrix.decompose(position, quaternion, scale)\n\n        times.push(time)\n        positionData.push(position.x, position.y, position.z)\n        quaternionData.push(quaternion.x, quaternion.y, quaternion.z, quaternion.w)\n        scaleData.push(scale.x, scale.y, scale.z)\n      }\n\n      if (positionData.length > 0) tracks.push(new VectorKeyframeTrack(name + '.position', times, positionData))\n      if (quaternionData.length > 0) {\n        tracks.push(new QuaternionKeyframeTrack(name + '.quaternion', times, quaternionData))\n      }\n      if (scaleData.length > 0) tracks.push(new VectorKeyframeTrack(name + '.scale', times, scaleData))\n\n      return tracks\n    }\n\n    function transformAnimationData(keyframes, property, defaultValue) {\n      let keyframe\n\n      let empty = true\n      let i, l\n\n      // check, if values of a property are missing in our keyframes\n\n      for (i = 0, l = keyframes.length; i < l; i++) {\n        keyframe = keyframes[i]\n\n        if (keyframe.value[property] === undefined) {\n          keyframe.value[property] = null // mark as missing\n        } else {\n          empty = false\n        }\n      }\n\n      if (empty === true) {\n        // no values at all, so we set a default value\n\n        for (i = 0, l = keyframes.length; i < l; i++) {\n          keyframe = keyframes[i]\n\n          keyframe.value[property] = defaultValue\n        }\n      } else {\n        // filling gaps\n\n        createMissingKeyframes(keyframes, property)\n      }\n    }\n\n    function createMissingKeyframes(keyframes, property) {\n      let prev, next\n\n      for (let i = 0, l = keyframes.length; i < l; i++) {\n        const keyframe = keyframes[i]\n\n        if (keyframe.value[property] === null) {\n          prev = getPrev(keyframes, i, property)\n          next = getNext(keyframes, i, property)\n\n          if (prev === null) {\n            keyframe.value[property] = next.value[property]\n            continue\n          }\n\n          if (next === null) {\n            keyframe.value[property] = prev.value[property]\n            continue\n          }\n\n          interpolate(keyframe, prev, next, property)\n        }\n      }\n    }\n\n    function getPrev(keyframes, i, property) {\n      while (i >= 0) {\n        const keyframe = keyframes[i]\n\n        if (keyframe.value[property] !== null) return keyframe\n\n        i--\n      }\n\n      return null\n    }\n\n    function getNext(keyframes, i, property) {\n      while (i < keyframes.length) {\n        const keyframe = keyframes[i]\n\n        if (keyframe.value[property] !== null) return keyframe\n\n        i++\n      }\n\n      return null\n    }\n\n    function interpolate(key, prev, next, property) {\n      if (next.time - prev.time === 0) {\n        key.value[property] = prev.value[property]\n        return\n      }\n\n      key.value[property] =\n        ((key.time - prev.time) * (next.value[property] - prev.value[property])) / (next.time - prev.time) +\n        prev.value[property]\n    }\n\n    // animation clips\n\n    function parseAnimationClip(xml) {\n      const data = {\n        name: xml.getAttribute('id') || 'default',\n        start: parseFloat(xml.getAttribute('start') || 0),\n        end: parseFloat(xml.getAttribute('end') || 0),\n        animations: [],\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'instance_animation':\n            data.animations.push(parseId(child.getAttribute('url')))\n            break\n        }\n      }\n\n      library.clips[xml.getAttribute('id')] = data\n    }\n\n    function buildAnimationClip(data) {\n      const tracks = []\n\n      const name = data.name\n      const duration = data.end - data.start || -1\n      const animations = data.animations\n\n      for (let i = 0, il = animations.length; i < il; i++) {\n        const animationTracks = getAnimation(animations[i])\n\n        for (let j = 0, jl = animationTracks.length; j < jl; j++) {\n          tracks.push(animationTracks[j])\n        }\n      }\n\n      return new AnimationClip(name, duration, tracks)\n    }\n\n    function getAnimationClip(id) {\n      return getBuild(library.clips[id], buildAnimationClip)\n    }\n\n    // controller\n\n    function parseController(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'skin':\n            // there is exactly one skin per controller\n            data.id = parseId(child.getAttribute('source'))\n            data.skin = parseSkin(child)\n            break\n\n          case 'morph':\n            data.id = parseId(child.getAttribute('source'))\n            console.warn('THREE.ColladaLoader: Morph target animation not supported yet.')\n            break\n        }\n      }\n\n      library.controllers[xml.getAttribute('id')] = data\n    }\n\n    function parseSkin(xml) {\n      const data = {\n        sources: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'bind_shape_matrix':\n            data.bindShapeMatrix = parseFloats(child.textContent)\n            break\n\n          case 'source':\n            const id = child.getAttribute('id')\n            data.sources[id] = parseSource(child)\n            break\n\n          case 'joints':\n            data.joints = parseJoints(child)\n            break\n\n          case 'vertex_weights':\n            data.vertexWeights = parseVertexWeights(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseJoints(xml) {\n      const data = {\n        inputs: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const semantic = child.getAttribute('semantic')\n            const id = parseId(child.getAttribute('source'))\n            data.inputs[semantic] = id\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseVertexWeights(xml) {\n      const data = {\n        inputs: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const semantic = child.getAttribute('semantic')\n            const id = parseId(child.getAttribute('source'))\n            const offset = parseInt(child.getAttribute('offset'))\n            data.inputs[semantic] = { id: id, offset: offset }\n            break\n\n          case 'vcount':\n            data.vcount = parseInts(child.textContent)\n            break\n\n          case 'v':\n            data.v = parseInts(child.textContent)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildController(data) {\n      const build = {\n        id: data.id,\n      }\n\n      const geometry = library.geometries[build.id]\n\n      if (data.skin !== undefined) {\n        build.skin = buildSkin(data.skin)\n\n        // we enhance the 'sources' property of the corresponding geometry with our skin data\n\n        geometry.sources.skinIndices = build.skin.indices\n        geometry.sources.skinWeights = build.skin.weights\n      }\n\n      return build\n    }\n\n    function buildSkin(data) {\n      const BONE_LIMIT = 4\n\n      const build = {\n        joints: [], // this must be an array to preserve the joint order\n        indices: {\n          array: [],\n          stride: BONE_LIMIT,\n        },\n        weights: {\n          array: [],\n          stride: BONE_LIMIT,\n        },\n      }\n\n      const sources = data.sources\n      const vertexWeights = data.vertexWeights\n\n      const vcount = vertexWeights.vcount\n      const v = vertexWeights.v\n      const jointOffset = vertexWeights.inputs.JOINT.offset\n      const weightOffset = vertexWeights.inputs.WEIGHT.offset\n\n      const jointSource = data.sources[data.joints.inputs.JOINT]\n      const inverseSource = data.sources[data.joints.inputs.INV_BIND_MATRIX]\n\n      const weights = sources[vertexWeights.inputs.WEIGHT.id].array\n      let stride = 0\n\n      let i, j, l\n\n      // procces skin data for each vertex\n\n      for (i = 0, l = vcount.length; i < l; i++) {\n        const jointCount = vcount[i] // this is the amount of joints that affect a single vertex\n        const vertexSkinData = []\n\n        for (j = 0; j < jointCount; j++) {\n          const skinIndex = v[stride + jointOffset]\n          const weightId = v[stride + weightOffset]\n          const skinWeight = weights[weightId]\n\n          vertexSkinData.push({ index: skinIndex, weight: skinWeight })\n\n          stride += 2\n        }\n\n        // we sort the joints in descending order based on the weights.\n        // this ensures, we only procced the most important joints of the vertex\n\n        vertexSkinData.sort(descending)\n\n        // now we provide for each vertex a set of four index and weight values.\n        // the order of the skin data matches the order of vertices\n\n        for (j = 0; j < BONE_LIMIT; j++) {\n          const d = vertexSkinData[j]\n\n          if (d !== undefined) {\n            build.indices.array.push(d.index)\n            build.weights.array.push(d.weight)\n          } else {\n            build.indices.array.push(0)\n            build.weights.array.push(0)\n          }\n        }\n      }\n\n      // setup bind matrix\n\n      if (data.bindShapeMatrix) {\n        build.bindMatrix = new Matrix4().fromArray(data.bindShapeMatrix).transpose()\n      } else {\n        build.bindMatrix = new Matrix4().identity()\n      }\n\n      // process bones and inverse bind matrix data\n\n      for (i = 0, l = jointSource.array.length; i < l; i++) {\n        const name = jointSource.array[i]\n        const boneInverse = new Matrix4().fromArray(inverseSource.array, i * inverseSource.stride).transpose()\n\n        build.joints.push({ name: name, boneInverse: boneInverse })\n      }\n\n      return build\n\n      // array sort function\n\n      function descending(a, b) {\n        return b.weight - a.weight\n      }\n    }\n\n    function getController(id) {\n      return getBuild(library.controllers[id], buildController)\n    }\n\n    // image\n\n    function parseImage(xml) {\n      const data = {\n        init_from: getElementsByTagName(xml, 'init_from')[0].textContent,\n      }\n\n      library.images[xml.getAttribute('id')] = data\n    }\n\n    function buildImage(data) {\n      if (data.build !== undefined) return data.build\n\n      return data.init_from\n    }\n\n    function getImage(id) {\n      const data = library.images[id]\n\n      if (data !== undefined) {\n        return getBuild(data, buildImage)\n      }\n\n      console.warn(\"THREE.ColladaLoader: Couldn't find image with ID:\", id)\n\n      return null\n    }\n\n    // effect\n\n    function parseEffect(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'profile_COMMON':\n            data.profile = parseEffectProfileCOMMON(child)\n            break\n        }\n      }\n\n      library.effects[xml.getAttribute('id')] = data\n    }\n\n    function parseEffectProfileCOMMON(xml) {\n      const data = {\n        surfaces: {},\n        samplers: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'newparam':\n            parseEffectNewparam(child, data)\n            break\n\n          case 'technique':\n            data.technique = parseEffectTechnique(child)\n            break\n\n          case 'extra':\n            data.extra = parseEffectExtra(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectNewparam(xml, data) {\n      const sid = xml.getAttribute('sid')\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'surface':\n            data.surfaces[sid] = parseEffectSurface(child)\n            break\n\n          case 'sampler2D':\n            data.samplers[sid] = parseEffectSampler(child)\n            break\n        }\n      }\n    }\n\n    function parseEffectSurface(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'init_from':\n            data.init_from = child.textContent\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectSampler(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'source':\n            data.source = child.textContent\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectTechnique(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'constant':\n          case 'lambert':\n          case 'blinn':\n          case 'phong':\n            data.type = child.nodeName\n            data.parameters = parseEffectParameters(child)\n            break\n\n          case 'extra':\n            data.extra = parseEffectExtra(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameters(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'emission':\n          case 'diffuse':\n          case 'specular':\n          case 'bump':\n          case 'ambient':\n          case 'shininess':\n          case 'transparency':\n            data[child.nodeName] = parseEffectParameter(child)\n            break\n          case 'transparent':\n            data[child.nodeName] = {\n              opaque: child.hasAttribute('opaque') ? child.getAttribute('opaque') : 'A_ONE',\n              data: parseEffectParameter(child),\n            }\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameter(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'color':\n            data[child.nodeName] = parseFloats(child.textContent)\n            break\n\n          case 'float':\n            data[child.nodeName] = parseFloat(child.textContent)\n            break\n\n          case 'texture':\n            data[child.nodeName] = { id: child.getAttribute('texture'), extra: parseEffectParameterTexture(child) }\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameterTexture(xml) {\n      const data = {\n        technique: {},\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'extra':\n            parseEffectParameterTextureExtra(child, data)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectParameterTextureExtra(xml, data) {\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique':\n            parseEffectParameterTextureExtraTechnique(child, data)\n            break\n        }\n      }\n    }\n\n    function parseEffectParameterTextureExtraTechnique(xml, data) {\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'repeatU':\n          case 'repeatV':\n          case 'offsetU':\n          case 'offsetV':\n            data.technique[child.nodeName] = parseFloat(child.textContent)\n            break\n\n          case 'wrapU':\n          case 'wrapV':\n            // some files have values for wrapU/wrapV which become NaN via parseInt\n\n            if (child.textContent.toUpperCase() === 'TRUE') {\n              data.technique[child.nodeName] = 1\n            } else if (child.textContent.toUpperCase() === 'FALSE') {\n              data.technique[child.nodeName] = 0\n            } else {\n              data.technique[child.nodeName] = parseInt(child.textContent)\n            }\n\n            break\n\n          case 'bump':\n            data[child.nodeName] = parseEffectExtraTechniqueBump(child)\n            break\n        }\n      }\n    }\n\n    function parseEffectExtra(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique':\n            data.technique = parseEffectExtraTechnique(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectExtraTechnique(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'double_sided':\n            data[child.nodeName] = parseInt(child.textContent)\n            break\n\n          case 'bump':\n            data[child.nodeName] = parseEffectExtraTechniqueBump(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseEffectExtraTechniqueBump(xml) {\n      var data = {}\n\n      for (var i = 0, l = xml.childNodes.length; i < l; i++) {\n        var child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'texture':\n            data[child.nodeName] = {\n              id: child.getAttribute('texture'),\n              texcoord: child.getAttribute('texcoord'),\n              extra: parseEffectParameterTexture(child),\n            }\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildEffect(data) {\n      return data\n    }\n\n    function getEffect(id) {\n      return getBuild(library.effects[id], buildEffect)\n    }\n\n    // material\n\n    function parseMaterial(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'instance_effect':\n            data.url = parseId(child.getAttribute('url'))\n            break\n        }\n      }\n\n      library.materials[xml.getAttribute('id')] = data\n    }\n\n    function getTextureLoader(image) {\n      let loader\n\n      let extension = image.slice(((image.lastIndexOf('.') - 1) >>> 0) + 2) // http://www.jstips.co/en/javascript/get-file-extension/\n      extension = extension.toLowerCase()\n\n      switch (extension) {\n        case 'tga':\n          loader = tgaLoader\n          break\n\n        default:\n          loader = textureLoader\n      }\n\n      return loader\n    }\n\n    function buildMaterial(data) {\n      const effect = getEffect(data.url)\n      const technique = effect.profile.technique\n\n      let material\n\n      switch (technique.type) {\n        case 'phong':\n        case 'blinn':\n          material = new MeshPhongMaterial()\n          break\n\n        case 'lambert':\n          material = new MeshLambertMaterial()\n          break\n\n        default:\n          material = new MeshBasicMaterial()\n          break\n      }\n\n      material.name = data.name || ''\n\n      function getTexture(textureObject) {\n        const sampler = effect.profile.samplers[textureObject.id]\n        let image = null\n\n        // get image\n\n        if (sampler !== undefined) {\n          const surface = effect.profile.surfaces[sampler.source]\n          image = getImage(surface.init_from)\n        } else {\n          console.warn('THREE.ColladaLoader: Undefined sampler. Access image directly (see #12530).')\n          image = getImage(textureObject.id)\n        }\n\n        // create texture if image is avaiable\n\n        if (image !== null) {\n          const loader = getTextureLoader(image)\n\n          if (loader !== undefined) {\n            const texture = loader.load(image)\n\n            const extra = textureObject.extra\n\n            if (extra !== undefined && extra.technique !== undefined && isEmpty(extra.technique) === false) {\n              const technique = extra.technique\n\n              texture.wrapS = technique.wrapU ? RepeatWrapping : ClampToEdgeWrapping\n              texture.wrapT = technique.wrapV ? RepeatWrapping : ClampToEdgeWrapping\n\n              texture.offset.set(technique.offsetU || 0, technique.offsetV || 0)\n              texture.repeat.set(technique.repeatU || 1, technique.repeatV || 1)\n            } else {\n              texture.wrapS = RepeatWrapping\n              texture.wrapT = RepeatWrapping\n            }\n\n            return texture\n          } else {\n            console.warn('THREE.ColladaLoader: Loader for texture %s not found.', image)\n\n            return null\n          }\n        } else {\n          console.warn(\"THREE.ColladaLoader: Couldn't create texture with ID:\", textureObject.id)\n\n          return null\n        }\n      }\n\n      const parameters = technique.parameters\n\n      for (const key in parameters) {\n        const parameter = parameters[key]\n\n        switch (key) {\n          case 'diffuse':\n            if (parameter.color) material.color.fromArray(parameter.color)\n            if (parameter.texture) material.map = getTexture(parameter.texture)\n            break\n          case 'specular':\n            if (parameter.color && material.specular) material.specular.fromArray(parameter.color)\n            if (parameter.texture) material.specularMap = getTexture(parameter.texture)\n            break\n          case 'bump':\n            if (parameter.texture) material.normalMap = getTexture(parameter.texture)\n            break\n          case 'ambient':\n            if (parameter.texture) material.lightMap = getTexture(parameter.texture)\n            break\n          case 'shininess':\n            if (parameter.float && material.shininess) material.shininess = parameter.float\n            break\n          case 'emission':\n            if (parameter.color && material.emissive) material.emissive.fromArray(parameter.color)\n            if (parameter.texture) material.emissiveMap = getTexture(parameter.texture)\n            break\n        }\n      }\n\n      //\n\n      let transparent = parameters['transparent']\n      let transparency = parameters['transparency']\n\n      // <transparency> does not exist but <transparent>\n\n      if (transparency === undefined && transparent) {\n        transparency = {\n          float: 1,\n        }\n      }\n\n      // <transparent> does not exist but <transparency>\n\n      if (transparent === undefined && transparency) {\n        transparent = {\n          opaque: 'A_ONE',\n          data: {\n            color: [1, 1, 1, 1],\n          },\n        }\n      }\n\n      if (transparent && transparency) {\n        // handle case if a texture exists but no color\n\n        if (transparent.data.texture) {\n          // we do not set an alpha map (see #13792)\n\n          material.transparent = true\n        } else {\n          const color = transparent.data.color\n\n          switch (transparent.opaque) {\n            case 'A_ONE':\n              material.opacity = color[3] * transparency.float\n              break\n            case 'RGB_ZERO':\n              material.opacity = 1 - color[0] * transparency.float\n              break\n            case 'A_ZERO':\n              material.opacity = 1 - color[3] * transparency.float\n              break\n            case 'RGB_ONE':\n              material.opacity = color[0] * transparency.float\n              break\n            default:\n              console.warn('THREE.ColladaLoader: Invalid opaque type \"%s\" of transparent tag.', transparent.opaque)\n          }\n\n          if (material.opacity < 1) material.transparent = true\n        }\n      }\n\n      //\n\n      if (technique.extra !== undefined && technique.extra.technique !== undefined) {\n        const techniques = technique.extra.technique\n\n        for (const k in techniques) {\n          const v = techniques[k]\n\n          switch (k) {\n            case 'double_sided':\n              material.side = v === 1 ? DoubleSide : FrontSide\n              break\n\n            case 'bump':\n              material.normalMap = getTexture(v.texture)\n              material.normalScale = new Vector2(1, 1)\n              break\n          }\n        }\n      }\n\n      return material\n    }\n\n    function getMaterial(id) {\n      return getBuild(library.materials[id], buildMaterial)\n    }\n\n    // camera\n\n    function parseCamera(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'optics':\n            data.optics = parseCameraOptics(child)\n            break\n        }\n      }\n\n      library.cameras[xml.getAttribute('id')] = data\n    }\n\n    function parseCameraOptics(xml) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            return parseCameraTechnique(child)\n        }\n      }\n\n      return {}\n    }\n\n    function parseCameraTechnique(xml) {\n      const data = {}\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'perspective':\n          case 'orthographic':\n            data.technique = child.nodeName\n            data.parameters = parseCameraParameters(child)\n\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseCameraParameters(xml) {\n      const data = {}\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'xfov':\n          case 'yfov':\n          case 'xmag':\n          case 'ymag':\n          case 'znear':\n          case 'zfar':\n          case 'aspect_ratio':\n            data[child.nodeName] = parseFloat(child.textContent)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildCamera(data) {\n      let camera\n\n      switch (data.optics.technique) {\n        case 'perspective':\n          camera = new PerspectiveCamera(\n            data.optics.parameters.yfov,\n            data.optics.parameters.aspect_ratio,\n            data.optics.parameters.znear,\n            data.optics.parameters.zfar,\n          )\n          break\n\n        case 'orthographic':\n          let ymag = data.optics.parameters.ymag\n          let xmag = data.optics.parameters.xmag\n          const aspectRatio = data.optics.parameters.aspect_ratio\n\n          xmag = xmag === undefined ? ymag * aspectRatio : xmag\n          ymag = ymag === undefined ? xmag / aspectRatio : ymag\n\n          xmag *= 0.5\n          ymag *= 0.5\n\n          camera = new OrthographicCamera(\n            -xmag,\n            xmag,\n            ymag,\n            -ymag, // left, right, top, bottom\n            data.optics.parameters.znear,\n            data.optics.parameters.zfar,\n          )\n          break\n\n        default:\n          camera = new PerspectiveCamera()\n          break\n      }\n\n      camera.name = data.name || ''\n\n      return camera\n    }\n\n    function getCamera(id) {\n      const data = library.cameras[id]\n\n      if (data !== undefined) {\n        return getBuild(data, buildCamera)\n      }\n\n      console.warn(\"THREE.ColladaLoader: Couldn't find camera with ID:\", id)\n\n      return null\n    }\n\n    // light\n\n    function parseLight(xml) {\n      let data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            data = parseLightTechnique(child)\n            break\n        }\n      }\n\n      library.lights[xml.getAttribute('id')] = data\n    }\n\n    function parseLightTechnique(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'directional':\n          case 'point':\n          case 'spot':\n          case 'ambient':\n            data.technique = child.nodeName\n            data.parameters = parseLightParameters(child)\n        }\n      }\n\n      return data\n    }\n\n    function parseLightParameters(xml) {\n      const data = {}\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'color':\n            const array = parseFloats(child.textContent)\n            data.color = new Color().fromArray(array)\n            break\n\n          case 'falloff_angle':\n            data.falloffAngle = parseFloat(child.textContent)\n            break\n\n          case 'quadratic_attenuation':\n            const f = parseFloat(child.textContent)\n            data.distance = f ? Math.sqrt(1 / f) : 0\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildLight(data) {\n      let light\n\n      switch (data.technique) {\n        case 'directional':\n          light = new DirectionalLight()\n          break\n\n        case 'point':\n          light = new PointLight()\n          break\n\n        case 'spot':\n          light = new SpotLight()\n          break\n\n        case 'ambient':\n          light = new AmbientLight()\n          break\n      }\n\n      if (data.parameters.color) light.color.copy(data.parameters.color)\n      if (data.parameters.distance) light.distance = data.parameters.distance\n\n      return light\n    }\n\n    function getLight(id) {\n      const data = library.lights[id]\n\n      if (data !== undefined) {\n        return getBuild(data, buildLight)\n      }\n\n      console.warn(\"THREE.ColladaLoader: Couldn't find light with ID:\", id)\n\n      return null\n    }\n\n    // geometry\n\n    function parseGeometry(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n        sources: {},\n        vertices: {},\n        primitives: [],\n      }\n\n      const mesh = getElementsByTagName(xml, 'mesh')[0]\n\n      // the following tags inside geometry are not supported yet (see https://github.com/mrdoob/three.js/pull/12606): convex_mesh, spline, brep\n      if (mesh === undefined) return\n\n      for (let i = 0; i < mesh.childNodes.length; i++) {\n        const child = mesh.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        const id = child.getAttribute('id')\n\n        switch (child.nodeName) {\n          case 'source':\n            data.sources[id] = parseSource(child)\n            break\n\n          case 'vertices':\n            // data.sources[ id ] = data.sources[ parseId( getElementsByTagName( child, 'input' )[ 0 ].getAttribute( 'source' ) ) ];\n            data.vertices = parseGeometryVertices(child)\n            break\n\n          case 'polygons':\n            console.warn('THREE.ColladaLoader: Unsupported primitive type: ', child.nodeName)\n            break\n\n          case 'lines':\n          case 'linestrips':\n          case 'polylist':\n          case 'triangles':\n            data.primitives.push(parseGeometryPrimitive(child))\n            break\n\n          default:\n            console.log(child)\n        }\n      }\n\n      library.geometries[xml.getAttribute('id')] = data\n    }\n\n    function parseSource(xml) {\n      const data = {\n        array: [],\n        stride: 3,\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'float_array':\n            data.array = parseFloats(child.textContent)\n            break\n\n          case 'Name_array':\n            data.array = parseStrings(child.textContent)\n            break\n\n          case 'technique_common':\n            const accessor = getElementsByTagName(child, 'accessor')[0]\n\n            if (accessor !== undefined) {\n              data.stride = parseInt(accessor.getAttribute('stride'))\n            }\n\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseGeometryVertices(xml) {\n      const data = {}\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        data[child.getAttribute('semantic')] = parseId(child.getAttribute('source'))\n      }\n\n      return data\n    }\n\n    function parseGeometryPrimitive(xml) {\n      const primitive = {\n        type: xml.nodeName,\n        material: xml.getAttribute('material'),\n        count: parseInt(xml.getAttribute('count')),\n        inputs: {},\n        stride: 0,\n        hasUV: false,\n      }\n\n      for (let i = 0, l = xml.childNodes.length; i < l; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'input':\n            const id = parseId(child.getAttribute('source'))\n            const semantic = child.getAttribute('semantic')\n            const offset = parseInt(child.getAttribute('offset'))\n            const set = parseInt(child.getAttribute('set'))\n            const inputname = set > 0 ? semantic + set : semantic\n            primitive.inputs[inputname] = { id: id, offset: offset }\n            primitive.stride = Math.max(primitive.stride, offset + 1)\n            if (semantic === 'TEXCOORD') primitive.hasUV = true\n            break\n\n          case 'vcount':\n            primitive.vcount = parseInts(child.textContent)\n            break\n\n          case 'p':\n            primitive.p = parseInts(child.textContent)\n            break\n        }\n      }\n\n      return primitive\n    }\n\n    function groupPrimitives(primitives) {\n      const build = {}\n\n      for (let i = 0; i < primitives.length; i++) {\n        const primitive = primitives[i]\n\n        if (build[primitive.type] === undefined) build[primitive.type] = []\n\n        build[primitive.type].push(primitive)\n      }\n\n      return build\n    }\n\n    function checkUVCoordinates(primitives) {\n      let count = 0\n\n      for (let i = 0, l = primitives.length; i < l; i++) {\n        const primitive = primitives[i]\n\n        if (primitive.hasUV === true) {\n          count++\n        }\n      }\n\n      if (count > 0 && count < primitives.length) {\n        primitives.uvsNeedsFix = true\n      }\n    }\n\n    function buildGeometry(data) {\n      const build = {}\n\n      const sources = data.sources\n      const vertices = data.vertices\n      const primitives = data.primitives\n\n      if (primitives.length === 0) return {}\n\n      // our goal is to create one buffer geometry for a single type of primitives\n      // first, we group all primitives by their type\n\n      const groupedPrimitives = groupPrimitives(primitives)\n\n      for (const type in groupedPrimitives) {\n        const primitiveType = groupedPrimitives[type]\n\n        // second, ensure consistent uv coordinates for each type of primitives (polylist,triangles or lines)\n\n        checkUVCoordinates(primitiveType)\n\n        // third, create a buffer geometry for each type of primitives\n\n        build[type] = buildGeometryType(primitiveType, sources, vertices)\n      }\n\n      return build\n    }\n\n    function buildGeometryType(primitives, sources, vertices) {\n      const build = {}\n\n      const position = { array: [], stride: 0 }\n      const normal = { array: [], stride: 0 }\n      const uv = { array: [], stride: 0 }\n      const uv1 = { array: [], stride: 0 }\n      const color = { array: [], stride: 0 }\n\n      const skinIndex = { array: [], stride: 4 }\n      const skinWeight = { array: [], stride: 4 }\n\n      const geometry = new BufferGeometry()\n\n      const materialKeys = []\n\n      let start = 0\n\n      for (let p = 0; p < primitives.length; p++) {\n        const primitive = primitives[p]\n        const inputs = primitive.inputs\n\n        // groups\n\n        let count = 0\n\n        switch (primitive.type) {\n          case 'lines':\n          case 'linestrips':\n            count = primitive.count * 2\n            break\n\n          case 'triangles':\n            count = primitive.count * 3\n            break\n\n          case 'polylist':\n            for (let g = 0; g < primitive.count; g++) {\n              const vc = primitive.vcount[g]\n\n              switch (vc) {\n                case 3:\n                  count += 3 // single triangle\n                  break\n\n                case 4:\n                  count += 6 // quad, subdivided into two triangles\n                  break\n\n                default:\n                  count += (vc - 2) * 3 // polylist with more than four vertices\n                  break\n              }\n            }\n\n            break\n\n          default:\n            console.warn('THREE.ColladaLoader: Unknow primitive type:', primitive.type)\n        }\n\n        geometry.addGroup(start, count, p)\n        start += count\n\n        // material\n\n        if (primitive.material) {\n          materialKeys.push(primitive.material)\n        }\n\n        // geometry data\n\n        for (const name in inputs) {\n          const input = inputs[name]\n\n          switch (name) {\n            case 'VERTEX':\n              for (const key in vertices) {\n                const id = vertices[key]\n\n                switch (key) {\n                  case 'POSITION':\n                    const prevLength = position.array.length\n                    buildGeometryData(primitive, sources[id], input.offset, position.array)\n                    position.stride = sources[id].stride\n\n                    if (sources.skinWeights && sources.skinIndices) {\n                      buildGeometryData(primitive, sources.skinIndices, input.offset, skinIndex.array)\n                      buildGeometryData(primitive, sources.skinWeights, input.offset, skinWeight.array)\n                    }\n\n                    // see #3803\n\n                    if (primitive.hasUV === false && primitives.uvsNeedsFix === true) {\n                      const count = (position.array.length - prevLength) / position.stride\n\n                      for (let i = 0; i < count; i++) {\n                        // fill missing uv coordinates\n\n                        uv.array.push(0, 0)\n                      }\n                    }\n\n                    break\n\n                  case 'NORMAL':\n                    buildGeometryData(primitive, sources[id], input.offset, normal.array)\n                    normal.stride = sources[id].stride\n                    break\n\n                  case 'COLOR':\n                    buildGeometryData(primitive, sources[id], input.offset, color.array)\n                    color.stride = sources[id].stride\n                    break\n\n                  case 'TEXCOORD':\n                    buildGeometryData(primitive, sources[id], input.offset, uv.array)\n                    uv.stride = sources[id].stride\n                    break\n\n                  case 'TEXCOORD1':\n                    buildGeometryData(primitive, sources[id], input.offset, uv1.array)\n                    uv.stride = sources[id].stride\n                    break\n\n                  default:\n                    console.warn('THREE.ColladaLoader: Semantic \"%s\" not handled in geometry build process.', key)\n                }\n              }\n\n              break\n\n            case 'NORMAL':\n              buildGeometryData(primitive, sources[input.id], input.offset, normal.array)\n              normal.stride = sources[input.id].stride\n              break\n\n            case 'COLOR':\n              buildGeometryData(primitive, sources[input.id], input.offset, color.array)\n              color.stride = sources[input.id].stride\n              break\n\n            case 'TEXCOORD':\n              buildGeometryData(primitive, sources[input.id], input.offset, uv.array)\n              uv.stride = sources[input.id].stride\n              break\n\n            case 'TEXCOORD1':\n              buildGeometryData(primitive, sources[input.id], input.offset, uv1.array)\n              uv1.stride = sources[input.id].stride\n              break\n          }\n        }\n      }\n\n      // build geometry\n\n      if (position.array.length > 0) {\n        geometry.setAttribute('position', new Float32BufferAttribute(position.array, position.stride))\n      }\n      if (normal.array.length > 0) {\n        geometry.setAttribute('normal', new Float32BufferAttribute(normal.array, normal.stride))\n      }\n      if (color.array.length > 0) geometry.setAttribute('color', new Float32BufferAttribute(color.array, color.stride))\n      if (uv.array.length > 0) geometry.setAttribute('uv', new Float32BufferAttribute(uv.array, uv.stride))\n      if (uv1.array.length > 0) geometry.setAttribute(UV1, new Float32BufferAttribute(uv1.array, uv1.stride))\n\n      if (skinIndex.array.length > 0) {\n        geometry.setAttribute('skinIndex', new Float32BufferAttribute(skinIndex.array, skinIndex.stride))\n      }\n      if (skinWeight.array.length > 0) {\n        geometry.setAttribute('skinWeight', new Float32BufferAttribute(skinWeight.array, skinWeight.stride))\n      }\n\n      build.data = geometry\n      build.type = primitives[0].type\n      build.materialKeys = materialKeys\n\n      return build\n    }\n\n    function buildGeometryData(primitive, source, offset, array) {\n      const indices = primitive.p\n      const stride = primitive.stride\n      const vcount = primitive.vcount\n\n      function pushVector(i) {\n        let index = indices[i + offset] * sourceStride\n        const length = index + sourceStride\n\n        for (; index < length; index++) {\n          array.push(sourceArray[index])\n        }\n      }\n\n      const sourceArray = source.array\n      const sourceStride = source.stride\n\n      if (primitive.vcount !== undefined) {\n        let index = 0\n\n        for (let i = 0, l = vcount.length; i < l; i++) {\n          const count = vcount[i]\n\n          if (count === 4) {\n            const a = index + stride * 0\n            const b = index + stride * 1\n            const c = index + stride * 2\n            const d = index + stride * 3\n\n            pushVector(a)\n            pushVector(b)\n            pushVector(d)\n            pushVector(b)\n            pushVector(c)\n            pushVector(d)\n          } else if (count === 3) {\n            const a = index + stride * 0\n            const b = index + stride * 1\n            const c = index + stride * 2\n\n            pushVector(a)\n            pushVector(b)\n            pushVector(c)\n          } else if (count > 4) {\n            for (let k = 1, kl = count - 2; k <= kl; k++) {\n              const a = index + stride * 0\n              const b = index + stride * k\n              const c = index + stride * (k + 1)\n\n              pushVector(a)\n              pushVector(b)\n              pushVector(c)\n            }\n          }\n\n          index += stride * count\n        }\n      } else {\n        for (let i = 0, l = indices.length; i < l; i += stride) {\n          pushVector(i)\n        }\n      }\n    }\n\n    function getGeometry(id) {\n      return getBuild(library.geometries[id], buildGeometry)\n    }\n\n    // kinematics\n\n    function parseKinematicsModel(xml) {\n      const data = {\n        name: xml.getAttribute('name') || '',\n        joints: {},\n        links: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            parseKinematicsTechniqueCommon(child, data)\n            break\n        }\n      }\n\n      library.kinematicsModels[xml.getAttribute('id')] = data\n    }\n\n    function buildKinematicsModel(data) {\n      if (data.build !== undefined) return data.build\n\n      return data\n    }\n\n    function getKinematicsModel(id) {\n      return getBuild(library.kinematicsModels[id], buildKinematicsModel)\n    }\n\n    function parseKinematicsTechniqueCommon(xml, data) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'joint':\n            data.joints[child.getAttribute('sid')] = parseKinematicsJoint(child)\n            break\n\n          case 'link':\n            data.links.push(parseKinematicsLink(child))\n            break\n        }\n      }\n    }\n\n    function parseKinematicsJoint(xml) {\n      let data\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'prismatic':\n          case 'revolute':\n            data = parseKinematicsJointParameter(child)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseKinematicsJointParameter(xml) {\n      const data = {\n        sid: xml.getAttribute('sid'),\n        name: xml.getAttribute('name') || '',\n        axis: new Vector3(),\n        limits: {\n          min: 0,\n          max: 0,\n        },\n        type: xml.nodeName,\n        static: false,\n        zeroPosition: 0,\n        middlePosition: 0,\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'axis':\n            const array = parseFloats(child.textContent)\n            data.axis.fromArray(array)\n            break\n          case 'limits':\n            const max = child.getElementsByTagName('max')[0]\n            const min = child.getElementsByTagName('min')[0]\n\n            data.limits.max = parseFloat(max.textContent)\n            data.limits.min = parseFloat(min.textContent)\n            break\n        }\n      }\n\n      // if min is equal to or greater than max, consider the joint static\n\n      if (data.limits.min >= data.limits.max) {\n        data.static = true\n      }\n\n      // calculate middle position\n\n      data.middlePosition = (data.limits.min + data.limits.max) / 2.0\n\n      return data\n    }\n\n    function parseKinematicsLink(xml) {\n      const data = {\n        sid: xml.getAttribute('sid'),\n        name: xml.getAttribute('name') || '',\n        attachments: [],\n        transforms: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'attachment_full':\n            data.attachments.push(parseKinematicsAttachment(child))\n            break\n\n          case 'matrix':\n          case 'translate':\n          case 'rotate':\n            data.transforms.push(parseKinematicsTransform(child))\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseKinematicsAttachment(xml) {\n      const data = {\n        joint: xml.getAttribute('joint').split('/').pop(),\n        transforms: [],\n        links: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'link':\n            data.links.push(parseKinematicsLink(child))\n            break\n\n          case 'matrix':\n          case 'translate':\n          case 'rotate':\n            data.transforms.push(parseKinematicsTransform(child))\n            break\n        }\n      }\n\n      return data\n    }\n\n    function parseKinematicsTransform(xml) {\n      const data = {\n        type: xml.nodeName,\n      }\n\n      const array = parseFloats(xml.textContent)\n\n      switch (data.type) {\n        case 'matrix':\n          data.obj = new Matrix4()\n          data.obj.fromArray(array).transpose()\n          break\n\n        case 'translate':\n          data.obj = new Vector3()\n          data.obj.fromArray(array)\n          break\n\n        case 'rotate':\n          data.obj = new Vector3()\n          data.obj.fromArray(array)\n          data.angle = MathUtils.degToRad(array[3])\n          break\n      }\n\n      return data\n    }\n\n    // physics\n\n    function parsePhysicsModel(xml) {\n      const data = {\n        name: xml.getAttribute('name') || '',\n        rigidBodies: {},\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'rigid_body':\n            data.rigidBodies[child.getAttribute('name')] = {}\n            parsePhysicsRigidBody(child, data.rigidBodies[child.getAttribute('name')])\n            break\n        }\n      }\n\n      library.physicsModels[xml.getAttribute('id')] = data\n    }\n\n    function parsePhysicsRigidBody(xml, data) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'technique_common':\n            parsePhysicsTechniqueCommon(child, data)\n            break\n        }\n      }\n    }\n\n    function parsePhysicsTechniqueCommon(xml, data) {\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'inertia':\n            data.inertia = parseFloats(child.textContent)\n            break\n\n          case 'mass':\n            data.mass = parseFloats(child.textContent)[0]\n            break\n        }\n      }\n    }\n\n    // scene\n\n    function parseKinematicsScene(xml) {\n      const data = {\n        bindJointAxis: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'bind_joint_axis':\n            data.bindJointAxis.push(parseKinematicsBindJointAxis(child))\n            break\n        }\n      }\n\n      library.kinematicsScenes[parseId(xml.getAttribute('url'))] = data\n    }\n\n    function parseKinematicsBindJointAxis(xml) {\n      const data = {\n        target: xml.getAttribute('target').split('/').pop(),\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        switch (child.nodeName) {\n          case 'axis':\n            const param = child.getElementsByTagName('param')[0]\n            data.axis = param.textContent\n            const tmpJointIndex = data.axis.split('inst_').pop().split('axis')[0]\n            data.jointIndex = tmpJointIndex.substr(0, tmpJointIndex.length - 1)\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildKinematicsScene(data) {\n      if (data.build !== undefined) return data.build\n\n      return data\n    }\n\n    function getKinematicsScene(id) {\n      return getBuild(library.kinematicsScenes[id], buildKinematicsScene)\n    }\n\n    function setupKinematics() {\n      const kinematicsModelId = Object.keys(library.kinematicsModels)[0]\n      const kinematicsSceneId = Object.keys(library.kinematicsScenes)[0]\n      const visualSceneId = Object.keys(library.visualScenes)[0]\n\n      if (kinematicsModelId === undefined || kinematicsSceneId === undefined) return\n\n      const kinematicsModel = getKinematicsModel(kinematicsModelId)\n      const kinematicsScene = getKinematicsScene(kinematicsSceneId)\n      const visualScene = getVisualScene(visualSceneId)\n\n      const bindJointAxis = kinematicsScene.bindJointAxis\n      const jointMap = {}\n\n      for (let i = 0, l = bindJointAxis.length; i < l; i++) {\n        const axis = bindJointAxis[i]\n\n        // the result of the following query is an element of type 'translate', 'rotate','scale' or 'matrix'\n\n        const targetElement = collada.querySelector('[sid=\"' + axis.target + '\"]')\n\n        if (targetElement) {\n          // get the parent of the transform element\n\n          const parentVisualElement = targetElement.parentElement\n\n          // connect the joint of the kinematics model with the element in the visual scene\n\n          connect(axis.jointIndex, parentVisualElement)\n        }\n      }\n\n      function connect(jointIndex, visualElement) {\n        const visualElementName = visualElement.getAttribute('name')\n        const joint = kinematicsModel.joints[jointIndex]\n\n        visualScene.traverse(function (object) {\n          if (object.name === visualElementName) {\n            jointMap[jointIndex] = {\n              object: object,\n              transforms: buildTransformList(visualElement),\n              joint: joint,\n              position: joint.zeroPosition,\n            }\n          }\n        })\n      }\n\n      const m0 = new Matrix4()\n\n      kinematics = {\n        joints: kinematicsModel && kinematicsModel.joints,\n\n        getJointValue: function (jointIndex) {\n          const jointData = jointMap[jointIndex]\n\n          if (jointData) {\n            return jointData.position\n          } else {\n            console.warn('THREE.ColladaLoader: Joint ' + jointIndex + \" doesn't exist.\")\n          }\n        },\n\n        setJointValue: function (jointIndex, value) {\n          const jointData = jointMap[jointIndex]\n\n          if (jointData) {\n            const joint = jointData.joint\n\n            if (value > joint.limits.max || value < joint.limits.min) {\n              console.warn(\n                'THREE.ColladaLoader: Joint ' +\n                jointIndex +\n                ' value ' +\n                value +\n                ' outside of limits (min: ' +\n                joint.limits.min +\n                ', max: ' +\n                joint.limits.max +\n                ').',\n              )\n            } else if (joint.static) {\n              console.warn('THREE.ColladaLoader: Joint ' + jointIndex + ' is static.')\n            } else {\n              const object = jointData.object\n              const axis = joint.axis\n              const transforms = jointData.transforms\n\n              matrix.identity()\n\n              // each update, we have to apply all transforms in the correct order\n\n              for (let i = 0; i < transforms.length; i++) {\n                const transform = transforms[i]\n\n                // if there is a connection of the transform node with a joint, apply the joint value\n\n                if (transform.sid && transform.sid.indexOf(jointIndex) !== -1) {\n                  switch (joint.type) {\n                    case 'revolute':\n                      matrix.multiply(m0.makeRotationAxis(axis, MathUtils.degToRad(value)))\n                      break\n\n                    case 'prismatic':\n                      matrix.multiply(m0.makeTranslation(axis.x * value, axis.y * value, axis.z * value))\n                      break\n\n                    default:\n                      console.warn('THREE.ColladaLoader: Unknown joint type: ' + joint.type)\n                      break\n                  }\n                } else {\n                  switch (transform.type) {\n                    case 'matrix':\n                      matrix.multiply(transform.obj)\n                      break\n\n                    case 'translate':\n                      matrix.multiply(m0.makeTranslation(transform.obj.x, transform.obj.y, transform.obj.z))\n                      break\n\n                    case 'scale':\n                      matrix.scale(transform.obj)\n                      break\n\n                    case 'rotate':\n                      matrix.multiply(m0.makeRotationAxis(transform.obj, transform.angle))\n                      break\n                  }\n                }\n              }\n\n              object.matrix.copy(matrix)\n              object.matrix.decompose(object.position, object.quaternion, object.scale)\n\n              jointMap[jointIndex].position = value\n            }\n          } else {\n            console.log('THREE.ColladaLoader: ' + jointIndex + ' does not exist.')\n          }\n        },\n      }\n    }\n\n    function buildTransformList(node) {\n      const transforms = []\n\n      const xml = collada.querySelector('[id=\"' + node.id + '\"]')\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        let array, vector\n\n        switch (child.nodeName) {\n          case 'matrix':\n            array = parseFloats(child.textContent)\n            const matrix = new Matrix4().fromArray(array).transpose()\n            transforms.push({\n              sid: child.getAttribute('sid'),\n              type: child.nodeName,\n              obj: matrix,\n            })\n            break\n\n          case 'translate':\n          case 'scale':\n            array = parseFloats(child.textContent)\n            vector = new Vector3().fromArray(array)\n            transforms.push({\n              sid: child.getAttribute('sid'),\n              type: child.nodeName,\n              obj: vector,\n            })\n            break\n\n          case 'rotate':\n            array = parseFloats(child.textContent)\n            vector = new Vector3().fromArray(array)\n            const angle = MathUtils.degToRad(array[3])\n            transforms.push({\n              sid: child.getAttribute('sid'),\n              type: child.nodeName,\n              obj: vector,\n              angle: angle,\n            })\n            break\n        }\n      }\n\n      return transforms\n    }\n\n    // nodes\n\n    function prepareNodes(xml) {\n      const elements = xml.getElementsByTagName('node')\n\n      // ensure all node elements have id attributes\n\n      for (let i = 0; i < elements.length; i++) {\n        const element = elements[i]\n\n        if (element.hasAttribute('id') === false) {\n          element.setAttribute('id', generateId())\n        }\n      }\n    }\n\n    const matrix = new Matrix4()\n    const vector = new Vector3()\n\n    function parseNode(xml) {\n      const data = {\n        name: xml.getAttribute('name') || '',\n        type: xml.getAttribute('type'),\n        id: xml.getAttribute('id'),\n        sid: xml.getAttribute('sid'),\n        matrix: new Matrix4(),\n        nodes: [],\n        instanceCameras: [],\n        instanceControllers: [],\n        instanceLights: [],\n        instanceGeometries: [],\n        instanceNodes: [],\n        transforms: {},\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        if (child.nodeType !== 1) continue\n\n        let array\n\n        switch (child.nodeName) {\n          case 'node':\n            data.nodes.push(child.getAttribute('id'))\n            parseNode(child)\n            break\n\n          case 'instance_camera':\n            data.instanceCameras.push(parseId(child.getAttribute('url')))\n            break\n\n          case 'instance_controller':\n            data.instanceControllers.push(parseNodeInstance(child))\n            break\n\n          case 'instance_light':\n            data.instanceLights.push(parseId(child.getAttribute('url')))\n            break\n\n          case 'instance_geometry':\n            data.instanceGeometries.push(parseNodeInstance(child))\n            break\n\n          case 'instance_node':\n            data.instanceNodes.push(parseId(child.getAttribute('url')))\n            break\n\n          case 'matrix':\n            array = parseFloats(child.textContent)\n            data.matrix.multiply(matrix.fromArray(array).transpose())\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'translate':\n            array = parseFloats(child.textContent)\n            vector.fromArray(array)\n            data.matrix.multiply(matrix.makeTranslation(vector.x, vector.y, vector.z))\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'rotate':\n            array = parseFloats(child.textContent)\n            const angle = MathUtils.degToRad(array[3])\n            data.matrix.multiply(matrix.makeRotationAxis(vector.fromArray(array), angle))\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'scale':\n            array = parseFloats(child.textContent)\n            data.matrix.scale(vector.fromArray(array))\n            data.transforms[child.getAttribute('sid')] = child.nodeName\n            break\n\n          case 'extra':\n            break\n\n          default:\n            console.log(child)\n        }\n      }\n\n      if (hasNode(data.id)) {\n        console.warn(\n          'THREE.ColladaLoader: There is already a node with ID %s. Exclude current node from further processing.',\n          data.id,\n        )\n      } else {\n        library.nodes[data.id] = data\n      }\n\n      return data\n    }\n\n    function parseNodeInstance(xml) {\n      const data = {\n        id: parseId(xml.getAttribute('url')),\n        materials: {},\n        skeletons: [],\n      }\n\n      for (let i = 0; i < xml.childNodes.length; i++) {\n        const child = xml.childNodes[i]\n\n        switch (child.nodeName) {\n          case 'bind_material':\n            const instances = child.getElementsByTagName('instance_material')\n\n            for (let j = 0; j < instances.length; j++) {\n              const instance = instances[j]\n              const symbol = instance.getAttribute('symbol')\n              const target = instance.getAttribute('target')\n\n              data.materials[symbol] = parseId(target)\n            }\n\n            break\n\n          case 'skeleton':\n            data.skeletons.push(parseId(child.textContent))\n            break\n\n          default:\n            break\n        }\n      }\n\n      return data\n    }\n\n    function buildSkeleton(skeletons, joints) {\n      const boneData = []\n      const sortedBoneData = []\n\n      let i, j, data\n\n      // a skeleton can have multiple root bones. collada expresses this\n      // situtation with multiple \"skeleton\" tags per controller instance\n\n      for (i = 0; i < skeletons.length; i++) {\n        const skeleton = skeletons[i]\n\n        let root\n\n        if (hasNode(skeleton)) {\n          root = getNode(skeleton)\n          buildBoneHierarchy(root, joints, boneData)\n        } else if (hasVisualScene(skeleton)) {\n          // handle case where the skeleton refers to the visual scene (#13335)\n\n          const visualScene = library.visualScenes[skeleton]\n          const children = visualScene.children\n\n          for (let j = 0; j < children.length; j++) {\n            const child = children[j]\n\n            if (child.type === 'JOINT') {\n              const root = getNode(child.id)\n              buildBoneHierarchy(root, joints, boneData)\n            }\n          }\n        } else {\n          console.error('THREE.ColladaLoader: Unable to find root bone of skeleton with ID:', skeleton)\n        }\n      }\n\n      // sort bone data (the order is defined in the corresponding controller)\n\n      for (i = 0; i < joints.length; i++) {\n        for (j = 0; j < boneData.length; j++) {\n          data = boneData[j]\n\n          if (data.bone.name === joints[i].name) {\n            sortedBoneData[i] = data\n            data.processed = true\n            break\n          }\n        }\n      }\n\n      // add unprocessed bone data at the end of the list\n\n      for (i = 0; i < boneData.length; i++) {\n        data = boneData[i]\n\n        if (data.processed === false) {\n          sortedBoneData.push(data)\n          data.processed = true\n        }\n      }\n\n      // setup arrays for skeleton creation\n\n      const bones = []\n      const boneInverses = []\n\n      for (i = 0; i < sortedBoneData.length; i++) {\n        data = sortedBoneData[i]\n\n        bones.push(data.bone)\n        boneInverses.push(data.boneInverse)\n      }\n\n      return new Skeleton(bones, boneInverses)\n    }\n\n    function buildBoneHierarchy(root, joints, boneData) {\n      // setup bone data from visual scene\n\n      root.traverse(function (object) {\n        if (object.isBone === true) {\n          let boneInverse\n\n          // retrieve the boneInverse from the controller data\n\n          for (let i = 0; i < joints.length; i++) {\n            const joint = joints[i]\n\n            if (joint.name === object.name) {\n              boneInverse = joint.boneInverse\n              break\n            }\n          }\n\n          if (boneInverse === undefined) {\n            // Unfortunately, there can be joints in the visual scene that are not part of the\n            // corresponding controller. In this case, we have to create a dummy boneInverse matrix\n            // for the respective bone. This bone won't affect any vertices, because there are no skin indices\n            // and weights defined for it. But we still have to add the bone to the sorted bone list in order to\n            // ensure a correct animation of the model.\n\n            boneInverse = new Matrix4()\n          }\n\n          boneData.push({ bone: object, boneInverse: boneInverse, processed: false })\n        }\n      })\n    }\n\n    function buildNode(data) {\n      const objects = []\n\n      const matrix = data.matrix\n      const nodes = data.nodes\n      const type = data.type\n      const instanceCameras = data.instanceCameras\n      const instanceControllers = data.instanceControllers\n      const instanceLights = data.instanceLights\n      const instanceGeometries = data.instanceGeometries\n      const instanceNodes = data.instanceNodes\n\n      // nodes\n\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        objects.push(getNode(nodes[i]))\n      }\n\n      // instance cameras\n\n      for (let i = 0, l = instanceCameras.length; i < l; i++) {\n        const instanceCamera = getCamera(instanceCameras[i])\n\n        if (instanceCamera !== null) {\n          objects.push(instanceCamera.clone())\n        }\n      }\n\n      // instance controllers\n\n      for (let i = 0, l = instanceControllers.length; i < l; i++) {\n        const instance = instanceControllers[i]\n        const controller = getController(instance.id)\n        const geometries = getGeometry(controller.id)\n        const newObjects = buildObjects(geometries, instance.materials)\n\n        const skeletons = instance.skeletons\n        const joints = controller.skin.joints\n\n        const skeleton = buildSkeleton(skeletons, joints)\n\n        for (let j = 0, jl = newObjects.length; j < jl; j++) {\n          const object = newObjects[j]\n\n          if (object.isSkinnedMesh) {\n            object.bind(skeleton, controller.skin.bindMatrix)\n            object.normalizeSkinWeights()\n          }\n\n          objects.push(object)\n        }\n      }\n\n      // instance lights\n\n      for (let i = 0, l = instanceLights.length; i < l; i++) {\n        const instanceLight = getLight(instanceLights[i])\n\n        if (instanceLight !== null) {\n          objects.push(instanceLight.clone())\n        }\n      }\n\n      // instance geometries\n\n      for (let i = 0, l = instanceGeometries.length; i < l; i++) {\n        const instance = instanceGeometries[i]\n\n        // a single geometry instance in collada can lead to multiple object3Ds.\n        // this is the case when primitives are combined like triangles and lines\n\n        const geometries = getGeometry(instance.id)\n        const newObjects = buildObjects(geometries, instance.materials)\n\n        for (let j = 0, jl = newObjects.length; j < jl; j++) {\n          objects.push(newObjects[j])\n        }\n      }\n\n      // instance nodes\n\n      for (let i = 0, l = instanceNodes.length; i < l; i++) {\n        objects.push(getNode(instanceNodes[i]).clone())\n      }\n\n      let object\n\n      if (nodes.length === 0 && objects.length === 1) {\n        object = objects[0]\n      } else {\n        object = type === 'JOINT' ? new Bone() : new Group()\n\n        for (let i = 0; i < objects.length; i++) {\n          object.add(objects[i])\n        }\n      }\n\n      object.name = type === 'JOINT' ? data.sid : data.name\n      object.matrix.copy(matrix)\n      object.matrix.decompose(object.position, object.quaternion, object.scale)\n\n      return object\n    }\n\n    const fallbackMaterial = new MeshBasicMaterial({ color: 0xff00ff })\n\n    function resolveMaterialBinding(keys, instanceMaterials) {\n      const materials = []\n\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const id = instanceMaterials[keys[i]]\n\n        if (id === undefined) {\n          console.warn('THREE.ColladaLoader: Material with key %s not found. Apply fallback material.', keys[i])\n          materials.push(fallbackMaterial)\n        } else {\n          materials.push(getMaterial(id))\n        }\n      }\n\n      return materials\n    }\n\n    function buildObjects(geometries, instanceMaterials) {\n      const objects = []\n\n      for (const type in geometries) {\n        const geometry = geometries[type]\n\n        const materials = resolveMaterialBinding(geometry.materialKeys, instanceMaterials)\n\n        // handle case if no materials are defined\n\n        if (materials.length === 0) {\n          if (type === 'lines' || type === 'linestrips') {\n            materials.push(new LineBasicMaterial())\n          } else {\n            materials.push(new MeshPhongMaterial())\n          }\n        }\n\n        // regard skinning\n\n        const skinning = geometry.data.attributes.skinIndex !== undefined\n\n        // choose between a single or multi materials (material array)\n\n        const material = materials.length === 1 ? materials[0] : materials\n\n        // now create a specific 3D object\n\n        let object\n\n        switch (type) {\n          case 'lines':\n            object = new LineSegments(geometry.data, material)\n            break\n\n          case 'linestrips':\n            object = new Line(geometry.data, material)\n            break\n\n          case 'triangles':\n          case 'polylist':\n            if (skinning) {\n              object = new SkinnedMesh(geometry.data, material)\n            } else {\n              object = new Mesh(geometry.data, material)\n            }\n\n            break\n        }\n\n        objects.push(object)\n      }\n\n      return objects\n    }\n\n    function hasNode(id) {\n      return library.nodes[id] !== undefined\n    }\n\n    function getNode(id) {\n      return getBuild(library.nodes[id], buildNode)\n    }\n\n    // visual scenes\n\n    function parseVisualScene(xml) {\n      const data = {\n        name: xml.getAttribute('name'),\n        children: [],\n      }\n\n      prepareNodes(xml)\n\n      const elements = getElementsByTagName(xml, 'node')\n\n      for (let i = 0; i < elements.length; i++) {\n        data.children.push(parseNode(elements[i]))\n      }\n\n      library.visualScenes[xml.getAttribute('id')] = data\n    }\n\n    function buildVisualScene(data) {\n      const group = new Group()\n      group.name = data.name\n\n      const children = data.children\n\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i]\n\n        group.add(getNode(child.id))\n      }\n\n      return group\n    }\n\n    function hasVisualScene(id) {\n      return library.visualScenes[id] !== undefined\n    }\n\n    function getVisualScene(id) {\n      return getBuild(library.visualScenes[id], buildVisualScene)\n    }\n\n    // scenes\n\n    function parseScene(xml) {\n      const instance = getElementsByTagName(xml, 'instance_visual_scene')[0]\n      return getVisualScene(parseId(instance.getAttribute('url')))\n    }\n\n    function setupAnimations() {\n      const clips = library.clips\n\n      if (isEmpty(clips) === true) {\n        if (isEmpty(library.animations) === false) {\n          // if there are animations but no clips, we create a default clip for playback\n\n          const tracks = []\n\n          for (const id in library.animations) {\n            const animationTracks = getAnimation(id)\n\n            for (let i = 0, l = animationTracks.length; i < l; i++) {\n              tracks.push(animationTracks[i])\n            }\n          }\n\n          animations.push(new AnimationClip('default', -1, tracks))\n        }\n      } else {\n        for (const id in clips) {\n          animations.push(getAnimationClip(id))\n        }\n      }\n    }\n\n    // convert the parser error element into text with each child elements text\n    // separated by new lines.\n\n    function parserErrorToText(parserError) {\n      let result = ''\n      const stack = [parserError]\n\n      while (stack.length) {\n        const node = stack.shift()\n\n        if (node.nodeType === Node.TEXT_NODE) {\n          result += node.textContent\n        } else {\n          result += '\\n'\n          stack.push.apply(stack, node.childNodes)\n        }\n      }\n\n      return result.trim()\n    }\n\n    if (text.length === 0) {\n      return { scene: new Scene() }\n    }\n\n    const xml = new DOMParser().parseFromString(text, 'application/xml')\n\n    const collada = getElementsByTagName(xml, 'COLLADA')[0]\n\n    const parserError = xml.getElementsByTagName('parsererror')[0]\n    if (parserError !== undefined) {\n      // Chrome will return parser error with a div in it\n\n      const errorElement = getElementsByTagName(parserError, 'div')[0]\n      let errorText\n\n      if (errorElement) {\n        errorText = errorElement.textContent\n      } else {\n        errorText = parserErrorToText(parserError)\n      }\n\n      console.error('THREE.ColladaLoader: Failed to parse collada file.\\n', errorText)\n\n      return null\n    }\n\n    // metadata\n\n    const version = collada.getAttribute('version')\n    console.log('THREE.ColladaLoader: File version', version)\n\n    const asset = parseAsset(getElementsByTagName(collada, 'asset')[0])\n    const textureLoader = new TextureLoader(this.manager)\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    let tgaLoader\n\n    if (TGALoader) {\n      tgaLoader = new TGALoader(this.manager)\n      tgaLoader.setPath(this.resourcePath || path)\n    }\n\n    //\n\n    const animations = []\n    let kinematics = {}\n    let count = 0\n\n    //\n\n    const library = {\n      animations: {},\n      clips: {},\n      controllers: {},\n      images: {},\n      effects: {},\n      materials: {},\n      cameras: {},\n      lights: {},\n      geometries: {},\n      nodes: {},\n      visualScenes: {},\n      kinematicsModels: {},\n      physicsModels: {},\n      kinematicsScenes: {},\n    }\n\n    parseLibrary(collada, 'library_animations', 'animation', parseAnimation)\n    parseLibrary(collada, 'library_animation_clips', 'animation_clip', parseAnimationClip)\n    parseLibrary(collada, 'library_controllers', 'controller', parseController)\n    parseLibrary(collada, 'library_images', 'image', parseImage)\n    parseLibrary(collada, 'library_effects', 'effect', parseEffect)\n    parseLibrary(collada, 'library_materials', 'material', parseMaterial)\n    parseLibrary(collada, 'library_cameras', 'camera', parseCamera)\n    parseLibrary(collada, 'library_lights', 'light', parseLight)\n    parseLibrary(collada, 'library_geometries', 'geometry', parseGeometry)\n    parseLibrary(collada, 'library_nodes', 'node', parseNode)\n    parseLibrary(collada, 'library_visual_scenes', 'visual_scene', parseVisualScene)\n    parseLibrary(collada, 'library_kinematics_models', 'kinematics_model', parseKinematicsModel)\n    parseLibrary(collada, 'library_physics_models', 'physics_model', parsePhysicsModel)\n    parseLibrary(collada, 'scene', 'instance_kinematics_scene', parseKinematicsScene)\n\n    buildLibrary(library.animations, buildAnimation)\n    buildLibrary(library.clips, buildAnimationClip)\n    buildLibrary(library.controllers, buildController)\n    buildLibrary(library.images, buildImage)\n    buildLibrary(library.effects, buildEffect)\n    buildLibrary(library.materials, buildMaterial)\n    buildLibrary(library.cameras, buildCamera)\n    buildLibrary(library.lights, buildLight)\n    buildLibrary(library.geometries, buildGeometry)\n    buildLibrary(library.visualScenes, buildVisualScene)\n\n    setupAnimations()\n    setupKinematics()\n\n    const scene = parseScene(getElementsByTagName(collada, 'scene')[0])\n    scene.animations = animations\n\n    if (asset.upAxis === 'Z_UP') {\n      scene.quaternion.setFromEuler(new Euler(-Math.PI / 2, 0, 0))\n    }\n\n    scene.scale.multiplyScalar(asset.unit)\n\n    return {\n      get animations() {\n        console.warn('THREE.ColladaLoader: Please access animations over scene.animations now.')\n        return animations\n      },\n      kinematics: kinematics,\n      library: library,\n      scene: scene,\n    }\n  }\n}\n\nexport { ColladaLoader }\n"], "names": ["xml", "text", "library", "animations", "technique", "count", "position", "vector", "matrix", "j", "root", "object", "parserE<PERSON>r"], "mappings": ";;;AA2CA,MAAM,sBAAsB,OAAO;AAAA,EACjC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,OAAO,MAAM,SAAS,KAAK,YAAY,eAAe,GAAG,IAAI,MAAM;AAEzE,UAAM,SAAS,IAAI,WAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,QAC/B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM,MAAM;AAChB,aAAS,qBAAqBA,MAAK,MAAM;AAGvC,YAAM,QAAQ,CAAE;AAChB,YAAM,aAAaA,KAAI;AAEvB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAM,QAAQ,WAAW,CAAC;AAE1B,YAAI,MAAM,aAAa,MAAM;AAC3B,gBAAM,KAAK,KAAK;AAAA,QACjB;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,aAAaC,OAAM;AAC1B,UAAIA,MAAK,WAAW;AAAG,eAAO,CAAE;AAEhC,YAAM,QAAQA,MAAK,KAAI,EAAG,MAAM,KAAK;AACrC,YAAM,QAAQ,IAAI,MAAM,MAAM,MAAM;AAEpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,CAAC,IAAI,MAAM,CAAC;AAAA,MACnB;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAYA,OAAM;AACzB,UAAIA,MAAK,WAAW;AAAG,eAAO,CAAE;AAEhC,YAAM,QAAQA,MAAK,KAAI,EAAG,MAAM,KAAK;AACrC,YAAM,QAAQ,IAAI,MAAM,MAAM,MAAM;AAEpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC;AAAA,MAC/B;AAED,aAAO;AAAA,IACR;AAED,aAAS,UAAUA,OAAM;AACvB,UAAIA,MAAK,WAAW;AAAG,eAAO,CAAE;AAEhC,YAAM,QAAQA,MAAK,KAAI,EAAG,MAAM,KAAK;AACrC,YAAM,QAAQ,IAAI,MAAM,MAAM,MAAM;AAEpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,CAAC,IAAI,SAAS,MAAM,CAAC,CAAC;AAAA,MAC7B;AAED,aAAO;AAAA,IACR;AAED,aAAS,QAAQA,OAAM;AACrB,aAAOA,MAAK,UAAU,CAAC;AAAA,IACxB;AAED,aAAS,aAAa;AACpB,aAAO,mBAAmB;AAAA,IAC3B;AAED,aAAS,QAAQ,QAAQ;AACvB,aAAO,OAAO,KAAK,MAAM,EAAE,WAAW;AAAA,IACvC;AAID,aAAS,WAAWD,MAAK;AACvB,aAAO;AAAA,QACL,MAAM,eAAe,qBAAqBA,MAAK,MAAM,EAAE,CAAC,CAAC;AAAA,QACzD,QAAQ,iBAAiB,qBAAqBA,MAAK,SAAS,EAAE,CAAC,CAAC;AAAA,MACjE;AAAA,IACF;AAED,aAAS,eAAeA,MAAK;AAC3B,UAAIA,SAAQ,UAAaA,KAAI,aAAa,OAAO,MAAM,MAAM;AAC3D,eAAO,WAAWA,KAAI,aAAa,OAAO,CAAC;AAAA,MACnD,OAAa;AACL,eAAO;AAAA,MACR;AAAA,IACF;AAED,aAAS,iBAAiBA,MAAK;AAC7B,aAAOA,SAAQ,SAAYA,KAAI,cAAc;AAAA,IAC9C;AAID,aAAS,aAAaA,MAAK,aAAa,UAAU,QAAQ;AACxD,YAAME,WAAU,qBAAqBF,MAAK,WAAW,EAAE,CAAC;AAExD,UAAIE,aAAY,QAAW;AACzB,cAAM,WAAW,qBAAqBA,UAAS,QAAQ;AAEvD,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,iBAAO,SAAS,CAAC,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAED,aAAS,aAAa,MAAM,SAAS;AACnC,iBAAW,QAAQ,MAAM;AACvB,cAAM,SAAS,KAAK,IAAI;AACxB,eAAO,QAAQ,QAAQ,KAAK,IAAI,CAAC;AAAA,MAClC;AAAA,IACF;AAID,aAAS,SAAS,MAAM,SAAS;AAC/B,UAAI,KAAK,UAAU;AAAW,eAAO,KAAK;AAE1C,WAAK,QAAQ,QAAQ,IAAI;AAEzB,aAAO,KAAK;AAAA,IACb;AAID,aAAS,eAAeF,MAAK;AAC3B,YAAM,OAAO;AAAA,QACX,SAAS,CAAE;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,UAAU,CAAE;AAAA,MACb;AAED,UAAI,cAAc;AAElB,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,YAAI;AAEJ,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,aAAa,IAAI;AAC5B,iBAAK,QAAQ,EAAE,IAAI,YAAY,KAAK;AACpC;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,aAAa,IAAI;AAC5B,iBAAK,SAAS,EAAE,IAAI,sBAAsB,KAAK;AAC/C;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,aAAa,QAAQ;AAChC,iBAAK,SAAS,EAAE,IAAI,sBAAsB,KAAK;AAC/C;AAAA,UAEF,KAAK;AAEH,2BAAe,KAAK;AACpB,0BAAc;AACd;AAAA,UAEF;AACE,oBAAQ,IAAI,KAAK;AAAA,QACpB;AAAA,MACF;AAED,UAAI,gBAAgB,OAAO;AAGzB,gBAAQ,WAAWA,KAAI,aAAa,IAAI,KAAK,UAAU,aAAc,CAAA,IAAI;AAAA,MAC1E;AAAA,IACF;AAED,aAAS,sBAAsBA,MAAK;AAClC,YAAM,OAAO;AAAA,QACX,QAAQ,CAAE;AAAA,MACX;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,KAAK,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAC/C,kBAAM,WAAW,MAAM,aAAa,UAAU;AAC9C,iBAAK,OAAO,QAAQ,IAAI;AACxB;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,sBAAsBA,MAAK;AAClC,YAAM,OAAO,CAAE;AAEf,YAAM,SAASA,KAAI,aAAa,QAAQ;AAIxC,UAAI,QAAQ,OAAO,MAAM,GAAG;AAE5B,YAAM,KAAK,MAAM,MAAO;AACxB,UAAI,MAAM,MAAM,MAAO;AAIvB,YAAM,cAAc,IAAI,QAAQ,GAAG,MAAM;AACzC,YAAM,eAAe,IAAI,QAAQ,GAAG,MAAM;AAE1C,UAAI,cAAc;AAGhB,gBAAQ,IAAI,MAAM,GAAG;AACrB,cAAM,MAAM,MAAO;AACnB,aAAK,SAAS,MAAM,MAAO;AAAA,MAC5B,WAAU,aAAa;AAGtB,cAAM,UAAU,IAAI,MAAM,GAAG;AAC7B,cAAM,QAAQ,MAAO;AAErB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,kBAAQ,CAAC,IAAI,SAAS,QAAQ,CAAC,EAAE,QAAQ,MAAM,EAAE,CAAC;AAAA,QACnD;AAED,aAAK,UAAU;AAAA,MAChB;AAED,WAAK,KAAK;AACV,WAAK,MAAM;AAEX,WAAK,cAAc;AACnB,WAAK,eAAe;AAEpB,WAAK,UAAU,QAAQA,KAAI,aAAa,QAAQ,CAAC;AAEjD,aAAO;AAAA,IACR;AAED,aAAS,eAAe,MAAM;AAC5B,YAAM,SAAS,CAAE;AAEjB,YAAM,WAAW,KAAK;AACtB,YAAM,WAAW,KAAK;AACtB,YAAM,UAAU,KAAK;AAErB,iBAAW,UAAU,UAAU;AAC7B,YAAI,SAAS,eAAe,MAAM,GAAG;AACnC,gBAAM,UAAU,SAAS,MAAM;AAC/B,gBAAM,UAAU,SAAS,QAAQ,OAAO;AAExC,gBAAM,UAAU,QAAQ,OAAO;AAC/B,gBAAM,WAAW,QAAQ,OAAO;AAEhC,gBAAM,cAAc,QAAQ,OAAO;AACnC,gBAAM,eAAe,QAAQ,QAAQ;AAErC,gBAAM,YAAY,sBAAsB,SAAS,aAAa,YAAY;AAE1E,+BAAqB,WAAW,MAAM;AAAA,QACvC;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,aAAa,IAAI;AACxB,aAAO,SAAS,QAAQ,WAAW,EAAE,GAAG,cAAc;AAAA,IACvD;AAED,aAAS,sBAAsB,SAAS,aAAa,cAAc;AACjE,YAAM,OAAO,QAAQ,MAAM,QAAQ,EAAE;AACrC,YAAM,WAAW,QAAQ,KAAK,EAAE;AAEhC,YAAM,YAAY,KAAK,WAAW,QAAQ,GAAG;AAC7C,YAAM,gBAAgB,KAAK,OAAO,MAAK,EAAG,UAAW;AAErD,UAAI,MAAM;AACV,UAAI,GAAG,IAAI,GAAG;AAEd,YAAM,OAAO,CAAE;AAKf,cAAQ,WAAS;AAAA,QACf,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,YAAY,MAAM,QAAQ,IAAI,IAAI,KAAK;AACtD,mBAAO,YAAY,MAAM,CAAC;AAC1B,qBAAS,IAAI,aAAa;AAE1B,gBAAI,KAAK,IAAI,MAAM;AAAW,mBAAK,IAAI,IAAI,CAAE;AAE7C,gBAAI,QAAQ,gBAAgB,MAAM;AAChC,oBAAM,QAAQ,aAAa,MAAM,MAAM;AACvC,oBAAM,QAAQ,QAAQ,QAAQ,CAAC,IAAI,IAAI,QAAQ,QAAQ,CAAC;AAExD,mBAAK,IAAI,EAAE,KAAK,IAAI;AAAA,YAClC,OAAmB;AACL,mBAAK,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AACjD,qBAAK,IAAI,EAAE,CAAC,IAAI,aAAa,MAAM,SAAS,CAAC;AAAA,cAC9C;AAAA,YACF;AAAA,UACF;AAED;AAAA,QAEF,KAAK;AACH,kBAAQ,KAAK,2EAA2E,SAAS;AACjG;AAAA,QAEF,KAAK;AACH,kBAAQ,KAAK,2EAA2E,SAAS;AACjG;AAAA,QAEF,KAAK;AACH,kBAAQ,KAAK,2EAA2E,SAAS;AACjG;AAAA,MACH;AAED,YAAM,YAAY,qBAAqB,MAAM,aAAa;AAE1D,YAAM,YAAY;AAAA,QAChB,MAAM,SAAS;AAAA,QACf;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAED,aAAS,qBAAqB,MAAM,eAAe;AACjD,YAAM,YAAY,CAAE;AAIpB,iBAAW,QAAQ,MAAM;AACvB,kBAAU,KAAK,EAAE,MAAM,WAAW,IAAI,GAAG,OAAO,KAAK,IAAI,GAAG;AAAA,MAC7D;AAID,gBAAU,KAAK,SAAS;AAIxB,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,+BAAuB,WAAW,GAAG,cAAc,SAAS,CAAC,CAAC;AAAA,MAC/D;AAED,aAAO;AAIP,eAAS,UAAU,GAAG,GAAG;AACvB,eAAO,EAAE,OAAO,EAAE;AAAA,MACnB;AAAA,IACF;AAED,UAAM,WAAW,IAAI,QAAS;AAC9B,UAAM,QAAQ,IAAI,QAAS;AAC3B,UAAM,aAAa,IAAI,WAAY;AAEnC,aAAS,qBAAqB,WAAW,QAAQ;AAC/C,YAAM,YAAY,UAAU;AAC5B,YAAM,OAAO,UAAU;AAEvB,YAAM,QAAQ,CAAE;AAChB,YAAM,eAAe,CAAE;AACvB,YAAM,iBAAiB,CAAE;AACzB,YAAM,YAAY,CAAE;AAEpB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,cAAM,WAAW,UAAU,CAAC;AAE5B,cAAM,OAAO,SAAS;AACtB,cAAM,QAAQ,SAAS;AAEvB,eAAO,UAAU,KAAK,EAAE,UAAW;AACnC,eAAO,UAAU,UAAU,YAAY,KAAK;AAE5C,cAAM,KAAK,IAAI;AACf,qBAAa,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AACpD,uBAAe,KAAK,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;AAC1E,kBAAU,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,MACzC;AAED,UAAI,aAAa,SAAS;AAAG,eAAO,KAAK,IAAI,oBAAoB,OAAO,aAAa,OAAO,YAAY,CAAC;AACzG,UAAI,eAAe,SAAS,GAAG;AAC7B,eAAO,KAAK,IAAI,wBAAwB,OAAO,eAAe,OAAO,cAAc,CAAC;AAAA,MACrF;AACD,UAAI,UAAU,SAAS;AAAG,eAAO,KAAK,IAAI,oBAAoB,OAAO,UAAU,OAAO,SAAS,CAAC;AAEhG,aAAO;AAAA,IACR;AAED,aAAS,uBAAuB,WAAW,UAAU,cAAc;AACjE,UAAI;AAEJ,UAAI,QAAQ;AACZ,UAAI,GAAG;AAIP,WAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC5C,mBAAW,UAAU,CAAC;AAEtB,YAAI,SAAS,MAAM,QAAQ,MAAM,QAAW;AAC1C,mBAAS,MAAM,QAAQ,IAAI;AAAA,QACrC,OAAe;AACL,kBAAQ;AAAA,QACT;AAAA,MACF;AAED,UAAI,UAAU,MAAM;AAGlB,aAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAC5C,qBAAW,UAAU,CAAC;AAEtB,mBAAS,MAAM,QAAQ,IAAI;AAAA,QAC5B;AAAA,MACT,OAAa;AAGL,+BAAuB,WAAW,QAAQ;AAAA,MAC3C;AAAA,IACF;AAED,aAAS,uBAAuB,WAAW,UAAU;AACnD,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,cAAM,WAAW,UAAU,CAAC;AAE5B,YAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;AACrC,iBAAO,QAAQ,WAAW,GAAG,QAAQ;AACrC,iBAAO,QAAQ,WAAW,GAAG,QAAQ;AAErC,cAAI,SAAS,MAAM;AACjB,qBAAS,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ;AAC9C;AAAA,UACD;AAED,cAAI,SAAS,MAAM;AACjB,qBAAS,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ;AAC9C;AAAA,UACD;AAED,sBAAY,UAAU,MAAM,MAAM,QAAQ;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAED,aAAS,QAAQ,WAAW,GAAG,UAAU;AACvC,aAAO,KAAK,GAAG;AACb,cAAM,WAAW,UAAU,CAAC;AAE5B,YAAI,SAAS,MAAM,QAAQ,MAAM;AAAM,iBAAO;AAE9C;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAED,aAAS,QAAQ,WAAW,GAAG,UAAU;AACvC,aAAO,IAAI,UAAU,QAAQ;AAC3B,cAAM,WAAW,UAAU,CAAC;AAE5B,YAAI,SAAS,MAAM,QAAQ,MAAM;AAAM,iBAAO;AAE9C;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAY,KAAK,MAAM,MAAM,UAAU;AAC9C,UAAI,KAAK,OAAO,KAAK,SAAS,GAAG;AAC/B,YAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ;AACzC;AAAA,MACD;AAED,UAAI,MAAM,QAAQ,KACd,IAAI,OAAO,KAAK,SAAS,KAAK,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,MAAO,KAAK,OAAO,KAAK,QAC7F,KAAK,MAAM,QAAQ;AAAA,IACtB;AAID,aAAS,mBAAmBA,MAAK;AAC/B,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,IAAI,KAAK;AAAA,QAChC,OAAO,WAAWA,KAAI,aAAa,OAAO,KAAK,CAAC;AAAA,QAChD,KAAK,WAAWA,KAAI,aAAa,KAAK,KAAK,CAAC;AAAA,QAC5C,YAAY,CAAE;AAAA,MACf;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,WAAW,KAAK,QAAQ,MAAM,aAAa,KAAK,CAAC,CAAC;AACvD;AAAA,QACH;AAAA,MACF;AAED,cAAQ,MAAMA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IACzC;AAED,aAAS,mBAAmB,MAAM;AAChC,YAAM,SAAS,CAAE;AAEjB,YAAM,OAAO,KAAK;AAClB,YAAM,WAAW,KAAK,MAAM,KAAK,SAAS;AAC1C,YAAMG,cAAa,KAAK;AAExB,eAAS,IAAI,GAAG,KAAKA,YAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,cAAM,kBAAkB,aAAaA,YAAW,CAAC,CAAC;AAElD,iBAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK;AACxD,iBAAO,KAAK,gBAAgB,CAAC,CAAC;AAAA,QAC/B;AAAA,MACF;AAED,aAAO,IAAI,cAAc,MAAM,UAAU,MAAM;AAAA,IAChD;AAED,aAAS,iBAAiB,IAAI;AAC5B,aAAO,SAAS,QAAQ,MAAM,EAAE,GAAG,kBAAkB;AAAA,IACtD;AAID,aAAS,gBAAgBH,MAAK;AAC5B,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAEH,iBAAK,KAAK,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAC9C,iBAAK,OAAO,UAAU,KAAK;AAC3B;AAAA,UAEF,KAAK;AACH,iBAAK,KAAK,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAC9C,oBAAQ,KAAK,gEAAgE;AAC7E;AAAA,QACH;AAAA,MACF;AAED,cAAQ,YAAYA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC/C;AAED,aAAS,UAAUA,MAAK;AACtB,YAAM,OAAO;AAAA,QACX,SAAS,CAAE;AAAA,MACZ;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,kBAAkB,YAAY,MAAM,WAAW;AACpD;AAAA,UAEF,KAAK;AACH,kBAAM,KAAK,MAAM,aAAa,IAAI;AAClC,iBAAK,QAAQ,EAAE,IAAI,YAAY,KAAK;AACpC;AAAA,UAEF,KAAK;AACH,iBAAK,SAAS,YAAY,KAAK;AAC/B;AAAA,UAEF,KAAK;AACH,iBAAK,gBAAgB,mBAAmB,KAAK;AAC7C;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAYA,MAAK;AACxB,YAAM,OAAO;AAAA,QACX,QAAQ,CAAE;AAAA,MACX;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,WAAW,MAAM,aAAa,UAAU;AAC9C,kBAAM,KAAK,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAC/C,iBAAK,OAAO,QAAQ,IAAI;AACxB;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmBA,MAAK;AAC/B,YAAM,OAAO;AAAA,QACX,QAAQ,CAAE;AAAA,MACX;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,WAAW,MAAM,aAAa,UAAU;AAC9C,kBAAM,KAAK,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAC/C,kBAAM,SAAS,SAAS,MAAM,aAAa,QAAQ,CAAC;AACpD,iBAAK,OAAO,QAAQ,IAAI,EAAE,IAAQ,OAAgB;AAClD;AAAA,UAEF,KAAK;AACH,iBAAK,SAAS,UAAU,MAAM,WAAW;AACzC;AAAA,UAEF,KAAK;AACH,iBAAK,IAAI,UAAU,MAAM,WAAW;AACpC;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,MAAM;AAC7B,YAAM,QAAQ;AAAA,QACZ,IAAI,KAAK;AAAA,MACV;AAED,YAAM,WAAW,QAAQ,WAAW,MAAM,EAAE;AAE5C,UAAI,KAAK,SAAS,QAAW;AAC3B,cAAM,OAAO,UAAU,KAAK,IAAI;AAIhC,iBAAS,QAAQ,cAAc,MAAM,KAAK;AAC1C,iBAAS,QAAQ,cAAc,MAAM,KAAK;AAAA,MAC3C;AAED,aAAO;AAAA,IACR;AAED,aAAS,UAAU,MAAM;AACvB,YAAM,aAAa;AAEnB,YAAM,QAAQ;AAAA,QACZ,QAAQ,CAAE;AAAA;AAAA,QACV,SAAS;AAAA,UACP,OAAO,CAAE;AAAA,UACT,QAAQ;AAAA,QACT;AAAA,QACD,SAAS;AAAA,UACP,OAAO,CAAE;AAAA,UACT,QAAQ;AAAA,QACT;AAAA,MACF;AAED,YAAM,UAAU,KAAK;AACrB,YAAM,gBAAgB,KAAK;AAE3B,YAAM,SAAS,cAAc;AAC7B,YAAM,IAAI,cAAc;AACxB,YAAM,cAAc,cAAc,OAAO,MAAM;AAC/C,YAAM,eAAe,cAAc,OAAO,OAAO;AAEjD,YAAM,cAAc,KAAK,QAAQ,KAAK,OAAO,OAAO,KAAK;AACzD,YAAM,gBAAgB,KAAK,QAAQ,KAAK,OAAO,OAAO,eAAe;AAErE,YAAM,UAAU,QAAQ,cAAc,OAAO,OAAO,EAAE,EAAE;AACxD,UAAI,SAAS;AAEb,UAAI,GAAG,GAAG;AAIV,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACzC,cAAM,aAAa,OAAO,CAAC;AAC3B,cAAM,iBAAiB,CAAE;AAEzB,aAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAC/B,gBAAM,YAAY,EAAE,SAAS,WAAW;AACxC,gBAAM,WAAW,EAAE,SAAS,YAAY;AACxC,gBAAM,aAAa,QAAQ,QAAQ;AAEnC,yBAAe,KAAK,EAAE,OAAO,WAAW,QAAQ,YAAY;AAE5D,oBAAU;AAAA,QACX;AAKD,uBAAe,KAAK,UAAU;AAK9B,aAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAC/B,gBAAM,IAAI,eAAe,CAAC;AAE1B,cAAI,MAAM,QAAW;AACnB,kBAAM,QAAQ,MAAM,KAAK,EAAE,KAAK;AAChC,kBAAM,QAAQ,MAAM,KAAK,EAAE,MAAM;AAAA,UAC7C,OAAiB;AACL,kBAAM,QAAQ,MAAM,KAAK,CAAC;AAC1B,kBAAM,QAAQ,MAAM,KAAK,CAAC;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAID,UAAI,KAAK,iBAAiB;AACxB,cAAM,aAAa,IAAI,QAAS,EAAC,UAAU,KAAK,eAAe,EAAE,UAAW;AAAA,MACpF,OAAa;AACL,cAAM,aAAa,IAAI,QAAO,EAAG,SAAU;AAAA,MAC5C;AAID,WAAK,IAAI,GAAG,IAAI,YAAY,MAAM,QAAQ,IAAI,GAAG,KAAK;AACpD,cAAM,OAAO,YAAY,MAAM,CAAC;AAChC,cAAM,cAAc,IAAI,QAAS,EAAC,UAAU,cAAc,OAAO,IAAI,cAAc,MAAM,EAAE,UAAW;AAEtG,cAAM,OAAO,KAAK,EAAE,MAAY,aAA0B;AAAA,MAC3D;AAED,aAAO;AAIP,eAAS,WAAW,GAAG,GAAG;AACxB,eAAO,EAAE,SAAS,EAAE;AAAA,MACrB;AAAA,IACF;AAED,aAAS,cAAc,IAAI;AACzB,aAAO,SAAS,QAAQ,YAAY,EAAE,GAAG,eAAe;AAAA,IACzD;AAID,aAAS,WAAWA,MAAK;AACvB,YAAM,OAAO;AAAA,QACX,WAAW,qBAAqBA,MAAK,WAAW,EAAE,CAAC,EAAE;AAAA,MACtD;AAED,cAAQ,OAAOA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC1C;AAED,aAAS,WAAW,MAAM;AACxB,UAAI,KAAK,UAAU;AAAW,eAAO,KAAK;AAE1C,aAAO,KAAK;AAAA,IACb;AAED,aAAS,SAAS,IAAI;AACpB,YAAM,OAAO,QAAQ,OAAO,EAAE;AAE9B,UAAI,SAAS,QAAW;AACtB,eAAO,SAAS,MAAM,UAAU;AAAA,MACjC;AAED,cAAQ,KAAK,qDAAqD,EAAE;AAEpE,aAAO;AAAA,IACR;AAID,aAAS,YAAYA,MAAK;AACxB,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,UAAU,yBAAyB,KAAK;AAC7C;AAAA,QACH;AAAA,MACF;AAED,cAAQ,QAAQA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC3C;AAED,aAAS,yBAAyBA,MAAK;AACrC,YAAM,OAAO;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,UAAU,CAAE;AAAA,MACb;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,gCAAoB,OAAO,IAAI;AAC/B;AAAA,UAEF,KAAK;AACH,iBAAK,YAAY,qBAAqB,KAAK;AAC3C;AAAA,UAEF,KAAK;AACH,iBAAK,QAAQ,iBAAiB,KAAK;AACnC;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,oBAAoBA,MAAK,MAAM;AACtC,YAAM,MAAMA,KAAI,aAAa,KAAK;AAElC,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,SAAS,GAAG,IAAI,mBAAmB,KAAK;AAC7C;AAAA,UAEF,KAAK;AACH,iBAAK,SAAS,GAAG,IAAI,mBAAmB,KAAK;AAC7C;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAED,aAAS,mBAAmBA,MAAK;AAC/B,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,YAAY,MAAM;AACvB;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmBA,MAAK;AAC/B,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,SAAS,MAAM;AACpB;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,qBAAqBA,MAAK;AACjC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,OAAO,MAAM;AAClB,iBAAK,aAAa,sBAAsB,KAAK;AAC7C;AAAA,UAEF,KAAK;AACH,iBAAK,QAAQ,iBAAiB,KAAK;AACnC;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,sBAAsBA,MAAK;AAClC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,qBAAqB,KAAK;AACjD;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI;AAAA,cACrB,QAAQ,MAAM,aAAa,QAAQ,IAAI,MAAM,aAAa,QAAQ,IAAI;AAAA,cACtE,MAAM,qBAAqB,KAAK;AAAA,YACjC;AACD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,qBAAqBA,MAAK;AACjC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,YAAY,MAAM,WAAW;AACpD;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,WAAW,MAAM,WAAW;AACnD;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,EAAE,IAAI,MAAM,aAAa,SAAS,GAAG,OAAO,4BAA4B,KAAK,EAAG;AACvG;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,4BAA4BA,MAAK;AACxC,YAAM,OAAO;AAAA,QACX,WAAW,CAAE;AAAA,MACd;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,6CAAiC,OAAO,IAAI;AAC5C;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,iCAAiCA,MAAK,MAAM;AACnD,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,sDAA0C,OAAO,IAAI;AACrD;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAED,aAAS,0CAA0CA,MAAK,MAAM;AAC5D,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,UAAU,MAAM,QAAQ,IAAI,WAAW,MAAM,WAAW;AAC7D;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AAGH,gBAAI,MAAM,YAAY,YAAW,MAAO,QAAQ;AAC9C,mBAAK,UAAU,MAAM,QAAQ,IAAI;AAAA,YAClC,WAAU,MAAM,YAAY,YAAW,MAAO,SAAS;AACtD,mBAAK,UAAU,MAAM,QAAQ,IAAI;AAAA,YAC/C,OAAmB;AACL,mBAAK,UAAU,MAAM,QAAQ,IAAI,SAAS,MAAM,WAAW;AAAA,YAC5D;AAED;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,8BAA8B,KAAK;AAC1D;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAED,aAAS,iBAAiBA,MAAK;AAC7B,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,YAAY,0BAA0B,KAAK;AAChD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,0BAA0BA,MAAK;AACtC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,SAAS,MAAM,WAAW;AACjD;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,8BAA8B,KAAK;AAC1D;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,8BAA8BA,MAAK;AAC1C,UAAI,OAAO,CAAE;AAEb,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,YAAI,QAAQA,KAAI,WAAW,CAAC;AAE5B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI;AAAA,cACrB,IAAI,MAAM,aAAa,SAAS;AAAA,cAChC,UAAU,MAAM,aAAa,UAAU;AAAA,cACvC,OAAO,4BAA4B,KAAK;AAAA,YACzC;AACD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAY,MAAM;AACzB,aAAO;AAAA,IACR;AAED,aAAS,UAAU,IAAI;AACrB,aAAO,SAAS,QAAQ,QAAQ,EAAE,GAAG,WAAW;AAAA,IACjD;AAID,aAAS,cAAcA,MAAK;AAC1B,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM;AAAA,MAC9B;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,QAAQ,MAAM,aAAa,KAAK,CAAC;AAC5C;AAAA,QACH;AAAA,MACF;AAED,cAAQ,UAAUA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC7C;AAED,aAAS,iBAAiB,OAAO;AAC/B,UAAI;AAEJ,UAAI,YAAY,MAAM,OAAQ,MAAM,YAAY,GAAG,IAAI,MAAO,KAAK,CAAC;AACpE,kBAAY,UAAU,YAAa;AAEnC,cAAQ,WAAS;AAAA,QACf,KAAK;AACH,mBAAS;AACT;AAAA,QAEF;AACE,mBAAS;AAAA,MACZ;AAED,aAAO;AAAA,IACR;AAED,aAAS,cAAc,MAAM;AAC3B,YAAM,SAAS,UAAU,KAAK,GAAG;AACjC,YAAM,YAAY,OAAO,QAAQ;AAEjC,UAAI;AAEJ,cAAQ,UAAU,MAAI;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,IAAI,kBAAmB;AAClC;AAAA,QAEF,KAAK;AACH,qBAAW,IAAI,oBAAqB;AACpC;AAAA,QAEF;AACE,qBAAW,IAAI,kBAAmB;AAClC;AAAA,MACH;AAED,eAAS,OAAO,KAAK,QAAQ;AAE7B,eAAS,WAAW,eAAe;AACjC,cAAM,UAAU,OAAO,QAAQ,SAAS,cAAc,EAAE;AACxD,YAAI,QAAQ;AAIZ,YAAI,YAAY,QAAW;AACzB,gBAAM,UAAU,OAAO,QAAQ,SAAS,QAAQ,MAAM;AACtD,kBAAQ,SAAS,QAAQ,SAAS;AAAA,QAC5C,OAAe;AACL,kBAAQ,KAAK,6EAA6E;AAC1F,kBAAQ,SAAS,cAAc,EAAE;AAAA,QAClC;AAID,YAAI,UAAU,MAAM;AAClB,gBAAM,SAAS,iBAAiB,KAAK;AAErC,cAAI,WAAW,QAAW;AACxB,kBAAM,UAAU,OAAO,KAAK,KAAK;AAEjC,kBAAM,QAAQ,cAAc;AAE5B,gBAAI,UAAU,UAAa,MAAM,cAAc,UAAa,QAAQ,MAAM,SAAS,MAAM,OAAO;AAC9F,oBAAMI,aAAY,MAAM;AAExB,sBAAQ,QAAQA,WAAU,QAAQ,iBAAiB;AACnD,sBAAQ,QAAQA,WAAU,QAAQ,iBAAiB;AAEnD,sBAAQ,OAAO,IAAIA,WAAU,WAAW,GAAGA,WAAU,WAAW,CAAC;AACjE,sBAAQ,OAAO,IAAIA,WAAU,WAAW,GAAGA,WAAU,WAAW,CAAC;AAAA,YAC/E,OAAmB;AACL,sBAAQ,QAAQ;AAChB,sBAAQ,QAAQ;AAAA,YACjB;AAED,mBAAO;AAAA,UACnB,OAAiB;AACL,oBAAQ,KAAK,yDAAyD,KAAK;AAE3E,mBAAO;AAAA,UACR;AAAA,QACX,OAAe;AACL,kBAAQ,KAAK,yDAAyD,cAAc,EAAE;AAEtF,iBAAO;AAAA,QACR;AAAA,MACF;AAED,YAAM,aAAa,UAAU;AAE7B,iBAAW,OAAO,YAAY;AAC5B,cAAM,YAAY,WAAW,GAAG;AAEhC,gBAAQ,KAAG;AAAA,UACT,KAAK;AACH,gBAAI,UAAU;AAAO,uBAAS,MAAM,UAAU,UAAU,KAAK;AAC7D,gBAAI,UAAU;AAAS,uBAAS,MAAM,WAAW,UAAU,OAAO;AAClE;AAAA,UACF,KAAK;AACH,gBAAI,UAAU,SAAS,SAAS;AAAU,uBAAS,SAAS,UAAU,UAAU,KAAK;AACrF,gBAAI,UAAU;AAAS,uBAAS,cAAc,WAAW,UAAU,OAAO;AAC1E;AAAA,UACF,KAAK;AACH,gBAAI,UAAU;AAAS,uBAAS,YAAY,WAAW,UAAU,OAAO;AACxE;AAAA,UACF,KAAK;AACH,gBAAI,UAAU;AAAS,uBAAS,WAAW,WAAW,UAAU,OAAO;AACvE;AAAA,UACF,KAAK;AACH,gBAAI,UAAU,SAAS,SAAS;AAAW,uBAAS,YAAY,UAAU;AAC1E;AAAA,UACF,KAAK;AACH,gBAAI,UAAU,SAAS,SAAS;AAAU,uBAAS,SAAS,UAAU,UAAU,KAAK;AACrF,gBAAI,UAAU;AAAS,uBAAS,cAAc,WAAW,UAAU,OAAO;AAC1E;AAAA,QACH;AAAA,MACF;AAID,UAAI,cAAc,WAAW,aAAa;AAC1C,UAAI,eAAe,WAAW,cAAc;AAI5C,UAAI,iBAAiB,UAAa,aAAa;AAC7C,uBAAe;AAAA,UACb,OAAO;AAAA,QACR;AAAA,MACF;AAID,UAAI,gBAAgB,UAAa,cAAc;AAC7C,sBAAc;AAAA,UACZ,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAED,UAAI,eAAe,cAAc;AAG/B,YAAI,YAAY,KAAK,SAAS;AAG5B,mBAAS,cAAc;AAAA,QACjC,OAAe;AACL,gBAAM,QAAQ,YAAY,KAAK;AAE/B,kBAAQ,YAAY,QAAM;AAAA,YACxB,KAAK;AACH,uBAAS,UAAU,MAAM,CAAC,IAAI,aAAa;AAC3C;AAAA,YACF,KAAK;AACH,uBAAS,UAAU,IAAI,MAAM,CAAC,IAAI,aAAa;AAC/C;AAAA,YACF,KAAK;AACH,uBAAS,UAAU,IAAI,MAAM,CAAC,IAAI,aAAa;AAC/C;AAAA,YACF,KAAK;AACH,uBAAS,UAAU,MAAM,CAAC,IAAI,aAAa;AAC3C;AAAA,YACF;AACE,sBAAQ,KAAK,qEAAqE,YAAY,MAAM;AAAA,UACvG;AAED,cAAI,SAAS,UAAU;AAAG,qBAAS,cAAc;AAAA,QAClD;AAAA,MACF;AAID,UAAI,UAAU,UAAU,UAAa,UAAU,MAAM,cAAc,QAAW;AAC5E,cAAM,aAAa,UAAU,MAAM;AAEnC,mBAAW,KAAK,YAAY;AAC1B,gBAAM,IAAI,WAAW,CAAC;AAEtB,kBAAQ,GAAC;AAAA,YACP,KAAK;AACH,uBAAS,OAAO,MAAM,IAAI,aAAa;AACvC;AAAA,YAEF,KAAK;AACH,uBAAS,YAAY,WAAW,EAAE,OAAO;AACzC,uBAAS,cAAc,IAAI,QAAQ,GAAG,CAAC;AACvC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAY,IAAI;AACvB,aAAO,SAAS,QAAQ,UAAU,EAAE,GAAG,aAAa;AAAA,IACrD;AAID,aAAS,YAAYJ,MAAK;AACxB,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM;AAAA,MAC9B;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,SAAS,kBAAkB,KAAK;AACrC;AAAA,QACH;AAAA,MACF;AAED,cAAQ,QAAQA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC3C;AAED,aAAS,kBAAkBA,MAAK;AAC9B,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,mBAAO,qBAAqB,KAAK;AAAA,QACpC;AAAA,MACF;AAED,aAAO,CAAE;AAAA,IACV;AAED,aAAS,qBAAqBA,MAAK;AACjC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,YAAY,MAAM;AACvB,iBAAK,aAAa,sBAAsB,KAAK;AAE7C;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,sBAAsBA,MAAK;AAClC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,MAAM,QAAQ,IAAI,WAAW,MAAM,WAAW;AACnD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAY,MAAM;AACzB,UAAI;AAEJ,cAAQ,KAAK,OAAO,WAAS;AAAA,QAC3B,KAAK;AACH,mBAAS,IAAI;AAAA,YACX,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,UACxB;AACD;AAAA,QAEF,KAAK;AACH,cAAI,OAAO,KAAK,OAAO,WAAW;AAClC,cAAI,OAAO,KAAK,OAAO,WAAW;AAClC,gBAAM,cAAc,KAAK,OAAO,WAAW;AAE3C,iBAAO,SAAS,SAAY,OAAO,cAAc;AACjD,iBAAO,SAAS,SAAY,OAAO,cAAc;AAEjD,kBAAQ;AACR,kBAAQ;AAER,mBAAS,IAAI;AAAA,YACX,CAAC;AAAA,YACD;AAAA,YACA;AAAA,YACA,CAAC;AAAA;AAAA,YACD,KAAK,OAAO,WAAW;AAAA,YACvB,KAAK,OAAO,WAAW;AAAA,UACxB;AACD;AAAA,QAEF;AACE,mBAAS,IAAI,kBAAmB;AAChC;AAAA,MACH;AAED,aAAO,OAAO,KAAK,QAAQ;AAE3B,aAAO;AAAA,IACR;AAED,aAAS,UAAU,IAAI;AACrB,YAAM,OAAO,QAAQ,QAAQ,EAAE;AAE/B,UAAI,SAAS,QAAW;AACtB,eAAO,SAAS,MAAM,WAAW;AAAA,MAClC;AAED,cAAQ,KAAK,sDAAsD,EAAE;AAErE,aAAO;AAAA,IACR;AAID,aAAS,WAAWA,MAAK;AACvB,UAAI,OAAO,CAAE;AAEb,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,mBAAO,oBAAoB,KAAK;AAChC;AAAA,QACH;AAAA,MACF;AAED,cAAQ,OAAOA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC1C;AAED,aAAS,oBAAoBA,MAAK;AAChC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,YAAY,MAAM;AACvB,iBAAK,aAAa,qBAAqB,KAAK;AAAA,QAC/C;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,qBAAqBA,MAAK;AACjC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,QAAQ,YAAY,MAAM,WAAW;AAC3C,iBAAK,QAAQ,IAAI,MAAK,EAAG,UAAU,KAAK;AACxC;AAAA,UAEF,KAAK;AACH,iBAAK,eAAe,WAAW,MAAM,WAAW;AAChD;AAAA,UAEF,KAAK;AACH,kBAAM,IAAI,WAAW,MAAM,WAAW;AACtC,iBAAK,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI;AACvC;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,WAAW,MAAM;AACxB,UAAI;AAEJ,cAAQ,KAAK,WAAS;AAAA,QACpB,KAAK;AACH,kBAAQ,IAAI,iBAAkB;AAC9B;AAAA,QAEF,KAAK;AACH,kBAAQ,IAAI,WAAY;AACxB;AAAA,QAEF,KAAK;AACH,kBAAQ,IAAI,UAAW;AACvB;AAAA,QAEF,KAAK;AACH,kBAAQ,IAAI,aAAc;AAC1B;AAAA,MACH;AAED,UAAI,KAAK,WAAW;AAAO,cAAM,MAAM,KAAK,KAAK,WAAW,KAAK;AACjE,UAAI,KAAK,WAAW;AAAU,cAAM,WAAW,KAAK,WAAW;AAE/D,aAAO;AAAA,IACR;AAED,aAAS,SAAS,IAAI;AACpB,YAAM,OAAO,QAAQ,OAAO,EAAE;AAE9B,UAAI,SAAS,QAAW;AACtB,eAAO,SAAS,MAAM,UAAU;AAAA,MACjC;AAED,cAAQ,KAAK,qDAAqD,EAAE;AAEpE,aAAO;AAAA,IACR;AAID,aAAS,cAAcA,MAAK;AAC1B,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM;AAAA,QAC7B,SAAS,CAAE;AAAA,QACX,UAAU,CAAE;AAAA,QACZ,YAAY,CAAE;AAAA,MACf;AAED,YAAM,OAAO,qBAAqBA,MAAK,MAAM,EAAE,CAAC;AAGhD,UAAI,SAAS;AAAW;AAExB,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,cAAM,QAAQ,KAAK,WAAW,CAAC;AAE/B,YAAI,MAAM,aAAa;AAAG;AAE1B,cAAM,KAAK,MAAM,aAAa,IAAI;AAElC,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,QAAQ,EAAE,IAAI,YAAY,KAAK;AACpC;AAAA,UAEF,KAAK;AAEH,iBAAK,WAAW,sBAAsB,KAAK;AAC3C;AAAA,UAEF,KAAK;AACH,oBAAQ,KAAK,qDAAqD,MAAM,QAAQ;AAChF;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,WAAW,KAAK,uBAAuB,KAAK,CAAC;AAClD;AAAA,UAEF;AACE,oBAAQ,IAAI,KAAK;AAAA,QACpB;AAAA,MACF;AAED,cAAQ,WAAWA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAC9C;AAED,aAAS,YAAYA,MAAK;AACxB,YAAM,OAAO;AAAA,QACX,OAAO,CAAE;AAAA,QACT,QAAQ;AAAA,MACT;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,QAAQ,YAAY,MAAM,WAAW;AAC1C;AAAA,UAEF,KAAK;AACH,iBAAK,QAAQ,aAAa,MAAM,WAAW;AAC3C;AAAA,UAEF,KAAK;AACH,kBAAM,WAAW,qBAAqB,OAAO,UAAU,EAAE,CAAC;AAE1D,gBAAI,aAAa,QAAW;AAC1B,mBAAK,SAAS,SAAS,SAAS,aAAa,QAAQ,CAAC;AAAA,YACvD;AAED;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,sBAAsBA,MAAK;AAClC,YAAM,OAAO,CAAE;AAEf,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,aAAK,MAAM,aAAa,UAAU,CAAC,IAAI,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAAA,MAC5E;AAED,aAAO;AAAA,IACR;AAED,aAAS,uBAAuBA,MAAK;AACnC,YAAM,YAAY;AAAA,QAChB,MAAMA,KAAI;AAAA,QACV,UAAUA,KAAI,aAAa,UAAU;AAAA,QACrC,OAAO,SAASA,KAAI,aAAa,OAAO,CAAC;AAAA,QACzC,QAAQ,CAAE;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,KAAK,QAAQ,MAAM,aAAa,QAAQ,CAAC;AAC/C,kBAAM,WAAW,MAAM,aAAa,UAAU;AAC9C,kBAAM,SAAS,SAAS,MAAM,aAAa,QAAQ,CAAC;AACpD,kBAAM,MAAM,SAAS,MAAM,aAAa,KAAK,CAAC;AAC9C,kBAAM,YAAY,MAAM,IAAI,WAAW,MAAM;AAC7C,sBAAU,OAAO,SAAS,IAAI,EAAE,IAAQ,OAAgB;AACxD,sBAAU,SAAS,KAAK,IAAI,UAAU,QAAQ,SAAS,CAAC;AACxD,gBAAI,aAAa;AAAY,wBAAU,QAAQ;AAC/C;AAAA,UAEF,KAAK;AACH,sBAAU,SAAS,UAAU,MAAM,WAAW;AAC9C;AAAA,UAEF,KAAK;AACH,sBAAU,IAAI,UAAU,MAAM,WAAW;AACzC;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,YAAY;AACnC,YAAM,QAAQ,CAAE;AAEhB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,cAAM,YAAY,WAAW,CAAC;AAE9B,YAAI,MAAM,UAAU,IAAI,MAAM;AAAW,gBAAM,UAAU,IAAI,IAAI,CAAE;AAEnE,cAAM,UAAU,IAAI,EAAE,KAAK,SAAS;AAAA,MACrC;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,YAAY;AACtC,UAAIK,SAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAM,YAAY,WAAW,CAAC;AAE9B,YAAI,UAAU,UAAU,MAAM;AAC5B,UAAAA;AAAA,QACD;AAAA,MACF;AAED,UAAIA,SAAQ,KAAKA,SAAQ,WAAW,QAAQ;AAC1C,mBAAW,cAAc;AAAA,MAC1B;AAAA,IACF;AAED,aAAS,cAAc,MAAM;AAC3B,YAAM,QAAQ,CAAE;AAEhB,YAAM,UAAU,KAAK;AACrB,YAAM,WAAW,KAAK;AACtB,YAAM,aAAa,KAAK;AAExB,UAAI,WAAW,WAAW;AAAG,eAAO,CAAE;AAKtC,YAAM,oBAAoB,gBAAgB,UAAU;AAEpD,iBAAW,QAAQ,mBAAmB;AACpC,cAAM,gBAAgB,kBAAkB,IAAI;AAI5C,2BAAmB,aAAa;AAIhC,cAAM,IAAI,IAAI,kBAAkB,eAAe,SAAS,QAAQ;AAAA,MACjE;AAED,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,YAAY,SAAS,UAAU;AACxD,YAAM,QAAQ,CAAE;AAEhB,YAAMC,YAAW,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AACzC,YAAM,SAAS,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AACvC,YAAM,KAAK,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AACnC,YAAM,MAAM,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AACpC,YAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AAEtC,YAAM,YAAY,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AAC1C,YAAM,aAAa,EAAE,OAAO,CAAA,GAAI,QAAQ,EAAG;AAE3C,YAAM,WAAW,IAAI,eAAgB;AAErC,YAAM,eAAe,CAAE;AAEvB,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,cAAM,YAAY,WAAW,CAAC;AAC9B,cAAM,SAAS,UAAU;AAIzB,YAAID,SAAQ;AAEZ,gBAAQ,UAAU,MAAI;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AACH,YAAAA,SAAQ,UAAU,QAAQ;AAC1B;AAAA,UAEF,KAAK;AACH,YAAAA,SAAQ,UAAU,QAAQ;AAC1B;AAAA,UAEF,KAAK;AACH,qBAAS,IAAI,GAAG,IAAI,UAAU,OAAO,KAAK;AACxC,oBAAM,KAAK,UAAU,OAAO,CAAC;AAE7B,sBAAQ,IAAE;AAAA,gBACR,KAAK;AACH,kBAAAA,UAAS;AACT;AAAA,gBAEF,KAAK;AACH,kBAAAA,UAAS;AACT;AAAA,gBAEF;AACE,kBAAAA,WAAU,KAAK,KAAK;AACpB;AAAA,cACH;AAAA,YACF;AAED;AAAA,UAEF;AACE,oBAAQ,KAAK,+CAA+C,UAAU,IAAI;AAAA,QAC7E;AAED,iBAAS,SAAS,OAAOA,QAAO,CAAC;AACjC,iBAASA;AAIT,YAAI,UAAU,UAAU;AACtB,uBAAa,KAAK,UAAU,QAAQ;AAAA,QACrC;AAID,mBAAW,QAAQ,QAAQ;AACzB,gBAAM,QAAQ,OAAO,IAAI;AAEzB,kBAAQ,MAAI;AAAA,YACV,KAAK;AACH,yBAAW,OAAO,UAAU;AAC1B,sBAAM,KAAK,SAAS,GAAG;AAEvB,wBAAQ,KAAG;AAAA,kBACT,KAAK;AACH,0BAAM,aAAaC,UAAS,MAAM;AAClC,sCAAkB,WAAW,QAAQ,EAAE,GAAG,MAAM,QAAQA,UAAS,KAAK;AACtE,oBAAAA,UAAS,SAAS,QAAQ,EAAE,EAAE;AAE9B,wBAAI,QAAQ,eAAe,QAAQ,aAAa;AAC9C,wCAAkB,WAAW,QAAQ,aAAa,MAAM,QAAQ,UAAU,KAAK;AAC/E,wCAAkB,WAAW,QAAQ,aAAa,MAAM,QAAQ,WAAW,KAAK;AAAA,oBACjF;AAID,wBAAI,UAAU,UAAU,SAAS,WAAW,gBAAgB,MAAM;AAChE,4BAAMD,UAASC,UAAS,MAAM,SAAS,cAAcA,UAAS;AAE9D,+BAAS,IAAI,GAAG,IAAID,QAAO,KAAK;AAG9B,2BAAG,MAAM,KAAK,GAAG,CAAC;AAAA,sBACnB;AAAA,oBACF;AAED;AAAA,kBAEF,KAAK;AACH,sCAAkB,WAAW,QAAQ,EAAE,GAAG,MAAM,QAAQ,OAAO,KAAK;AACpE,2BAAO,SAAS,QAAQ,EAAE,EAAE;AAC5B;AAAA,kBAEF,KAAK;AACH,sCAAkB,WAAW,QAAQ,EAAE,GAAG,MAAM,QAAQ,MAAM,KAAK;AACnE,0BAAM,SAAS,QAAQ,EAAE,EAAE;AAC3B;AAAA,kBAEF,KAAK;AACH,sCAAkB,WAAW,QAAQ,EAAE,GAAG,MAAM,QAAQ,GAAG,KAAK;AAChE,uBAAG,SAAS,QAAQ,EAAE,EAAE;AACxB;AAAA,kBAEF,KAAK;AACH,sCAAkB,WAAW,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI,KAAK;AACjE,uBAAG,SAAS,QAAQ,EAAE,EAAE;AACxB;AAAA,kBAEF;AACE,4BAAQ,KAAK,6EAA6E,GAAG;AAAA,gBAChG;AAAA,cACF;AAED;AAAA,YAEF,KAAK;AACH,gCAAkB,WAAW,QAAQ,MAAM,EAAE,GAAG,MAAM,QAAQ,OAAO,KAAK;AAC1E,qBAAO,SAAS,QAAQ,MAAM,EAAE,EAAE;AAClC;AAAA,YAEF,KAAK;AACH,gCAAkB,WAAW,QAAQ,MAAM,EAAE,GAAG,MAAM,QAAQ,MAAM,KAAK;AACzE,oBAAM,SAAS,QAAQ,MAAM,EAAE,EAAE;AACjC;AAAA,YAEF,KAAK;AACH,gCAAkB,WAAW,QAAQ,MAAM,EAAE,GAAG,MAAM,QAAQ,GAAG,KAAK;AACtE,iBAAG,SAAS,QAAQ,MAAM,EAAE,EAAE;AAC9B;AAAA,YAEF,KAAK;AACH,gCAAkB,WAAW,QAAQ,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI,KAAK;AACvE,kBAAI,SAAS,QAAQ,MAAM,EAAE,EAAE;AAC/B;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAID,UAAIC,UAAS,MAAM,SAAS,GAAG;AAC7B,iBAAS,aAAa,YAAY,IAAI,uBAAuBA,UAAS,OAAOA,UAAS,MAAM,CAAC;AAAA,MAC9F;AACD,UAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,iBAAS,aAAa,UAAU,IAAI,uBAAuB,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,MACxF;AACD,UAAI,MAAM,MAAM,SAAS;AAAG,iBAAS,aAAa,SAAS,IAAI,uBAAuB,MAAM,OAAO,MAAM,MAAM,CAAC;AAChH,UAAI,GAAG,MAAM,SAAS;AAAG,iBAAS,aAAa,MAAM,IAAI,uBAAuB,GAAG,OAAO,GAAG,MAAM,CAAC;AACpG,UAAI,IAAI,MAAM,SAAS;AAAG,iBAAS,aAAa,KAAK,IAAI,uBAAuB,IAAI,OAAO,IAAI,MAAM,CAAC;AAEtG,UAAI,UAAU,MAAM,SAAS,GAAG;AAC9B,iBAAS,aAAa,aAAa,IAAI,uBAAuB,UAAU,OAAO,UAAU,MAAM,CAAC;AAAA,MACjG;AACD,UAAI,WAAW,MAAM,SAAS,GAAG;AAC/B,iBAAS,aAAa,cAAc,IAAI,uBAAuB,WAAW,OAAO,WAAW,MAAM,CAAC;AAAA,MACpG;AAED,YAAM,OAAO;AACb,YAAM,OAAO,WAAW,CAAC,EAAE;AAC3B,YAAM,eAAe;AAErB,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,WAAW,QAAQ,QAAQ,OAAO;AAC3D,YAAM,UAAU,UAAU;AAC1B,YAAM,SAAS,UAAU;AACzB,YAAM,SAAS,UAAU;AAEzB,eAAS,WAAW,GAAG;AACrB,YAAI,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAClC,cAAM,SAAS,QAAQ;AAEvB,eAAO,QAAQ,QAAQ,SAAS;AAC9B,gBAAM,KAAK,YAAY,KAAK,CAAC;AAAA,QAC9B;AAAA,MACF;AAED,YAAM,cAAc,OAAO;AAC3B,YAAM,eAAe,OAAO;AAE5B,UAAI,UAAU,WAAW,QAAW;AAClC,YAAI,QAAQ;AAEZ,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,gBAAMD,SAAQ,OAAO,CAAC;AAEtB,cAAIA,WAAU,GAAG;AACf,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAE3B,uBAAW,CAAC;AACZ,uBAAW,CAAC;AACZ,uBAAW,CAAC;AACZ,uBAAW,CAAC;AACZ,uBAAW,CAAC;AACZ,uBAAW,CAAC;AAAA,UACxB,WAAqBA,WAAU,GAAG;AACtB,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAC3B,kBAAM,IAAI,QAAQ,SAAS;AAE3B,uBAAW,CAAC;AACZ,uBAAW,CAAC;AACZ,uBAAW,CAAC;AAAA,UACxB,WAAqBA,SAAQ,GAAG;AACpB,qBAAS,IAAI,GAAG,KAAKA,SAAQ,GAAG,KAAK,IAAI,KAAK;AAC5C,oBAAM,IAAI,QAAQ,SAAS;AAC3B,oBAAM,IAAI,QAAQ,SAAS;AAC3B,oBAAM,IAAI,QAAQ,UAAU,IAAI;AAEhC,yBAAW,CAAC;AACZ,yBAAW,CAAC;AACZ,yBAAW,CAAC;AAAA,YACb;AAAA,UACF;AAED,mBAAS,SAASA;AAAA,QACnB;AAAA,MACT,OAAa;AACL,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK,QAAQ;AACtD,qBAAW,CAAC;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAED,aAAS,YAAY,IAAI;AACvB,aAAO,SAAS,QAAQ,WAAW,EAAE,GAAG,aAAa;AAAA,IACtD;AAID,aAAS,qBAAqBL,MAAK;AACjC,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM,KAAK;AAAA,QAClC,QAAQ,CAAE;AAAA,QACV,OAAO,CAAE;AAAA,MACV;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,2CAA+B,OAAO,IAAI;AAC1C;AAAA,QACH;AAAA,MACF;AAED,cAAQ,iBAAiBA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IACpD;AAED,aAAS,qBAAqB,MAAM;AAClC,UAAI,KAAK,UAAU;AAAW,eAAO,KAAK;AAE1C,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,IAAI;AAC9B,aAAO,SAAS,QAAQ,iBAAiB,EAAE,GAAG,oBAAoB;AAAA,IACnE;AAED,aAAS,+BAA+BA,MAAK,MAAM;AACjD,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,OAAO,MAAM,aAAa,KAAK,CAAC,IAAI,qBAAqB,KAAK;AACnE;AAAA,UAEF,KAAK;AACH,iBAAK,MAAM,KAAK,oBAAoB,KAAK,CAAC;AAC1C;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAED,aAAS,qBAAqBA,MAAK;AACjC,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,8BAA8B,KAAK;AAC1C;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,8BAA8BA,MAAK;AAC1C,YAAM,OAAO;AAAA,QACX,KAAKA,KAAI,aAAa,KAAK;AAAA,QAC3B,MAAMA,KAAI,aAAa,MAAM,KAAK;AAAA,QAClC,MAAM,IAAI,QAAS;AAAA,QACnB,QAAQ;AAAA,UACN,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,QACD,MAAMA,KAAI;AAAA,QACV,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,gBAAgB;AAAA,MACjB;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,QAAQ,YAAY,MAAM,WAAW;AAC3C,iBAAK,KAAK,UAAU,KAAK;AACzB;AAAA,UACF,KAAK;AACH,kBAAM,MAAM,MAAM,qBAAqB,KAAK,EAAE,CAAC;AAC/C,kBAAM,MAAM,MAAM,qBAAqB,KAAK,EAAE,CAAC;AAE/C,iBAAK,OAAO,MAAM,WAAW,IAAI,WAAW;AAC5C,iBAAK,OAAO,MAAM,WAAW,IAAI,WAAW;AAC5C;AAAA,QACH;AAAA,MACF;AAID,UAAI,KAAK,OAAO,OAAO,KAAK,OAAO,KAAK;AACtC,aAAK,SAAS;AAAA,MACf;AAID,WAAK,kBAAkB,KAAK,OAAO,MAAM,KAAK,OAAO,OAAO;AAE5D,aAAO;AAAA,IACR;AAED,aAAS,oBAAoBA,MAAK;AAChC,YAAM,OAAO;AAAA,QACX,KAAKA,KAAI,aAAa,KAAK;AAAA,QAC3B,MAAMA,KAAI,aAAa,MAAM,KAAK;AAAA,QAClC,aAAa,CAAE;AAAA,QACf,YAAY,CAAE;AAAA,MACf;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,YAAY,KAAK,0BAA0B,KAAK,CAAC;AACtD;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,WAAW,KAAK,yBAAyB,KAAK,CAAC;AACpD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,0BAA0BA,MAAK;AACtC,YAAM,OAAO;AAAA,QACX,OAAOA,KAAI,aAAa,OAAO,EAAE,MAAM,GAAG,EAAE,IAAK;AAAA,QACjD,YAAY,CAAE;AAAA,QACd,OAAO,CAAE;AAAA,MACV;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,KAAK,oBAAoB,KAAK,CAAC;AAC1C;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,iBAAK,WAAW,KAAK,yBAAyB,KAAK,CAAC;AACpD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,yBAAyBA,MAAK;AACrC,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI;AAAA,MACX;AAED,YAAM,QAAQ,YAAYA,KAAI,WAAW;AAEzC,cAAQ,KAAK,MAAI;AAAA,QACf,KAAK;AACH,eAAK,MAAM,IAAI,QAAS;AACxB,eAAK,IAAI,UAAU,KAAK,EAAE,UAAW;AACrC;AAAA,QAEF,KAAK;AACH,eAAK,MAAM,IAAI,QAAS;AACxB,eAAK,IAAI,UAAU,KAAK;AACxB;AAAA,QAEF,KAAK;AACH,eAAK,MAAM,IAAI,QAAS;AACxB,eAAK,IAAI,UAAU,KAAK;AACxB,eAAK,QAAQ,UAAU,SAAS,MAAM,CAAC,CAAC;AACxC;AAAA,MACH;AAED,aAAO;AAAA,IACR;AAID,aAAS,kBAAkBA,MAAK;AAC9B,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM,KAAK;AAAA,QAClC,aAAa,CAAE;AAAA,MAChB;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,YAAY,MAAM,aAAa,MAAM,CAAC,IAAI,CAAE;AACjD,kCAAsB,OAAO,KAAK,YAAY,MAAM,aAAa,MAAM,CAAC,CAAC;AACzE;AAAA,QACH;AAAA,MACF;AAED,cAAQ,cAAcA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IACjD;AAED,aAAS,sBAAsBA,MAAK,MAAM;AACxC,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,wCAA4B,OAAO,IAAI;AACvC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAED,aAAS,4BAA4BA,MAAK,MAAM;AAC9C,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,UAAU,YAAY,MAAM,WAAW;AAC5C;AAAA,UAEF,KAAK;AACH,iBAAK,OAAO,YAAY,MAAM,WAAW,EAAE,CAAC;AAC5C;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAID,aAAS,qBAAqBA,MAAK;AACjC,YAAM,OAAO;AAAA,QACX,eAAe,CAAE;AAAA,MAClB;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,cAAc,KAAK,6BAA6B,KAAK,CAAC;AAC3D;AAAA,QACH;AAAA,MACF;AAED,cAAQ,iBAAiB,QAAQA,KAAI,aAAa,KAAK,CAAC,CAAC,IAAI;AAAA,IAC9D;AAED,aAAS,6BAA6BA,MAAK;AACzC,YAAM,OAAO;AAAA,QACX,QAAQA,KAAI,aAAa,QAAQ,EAAE,MAAM,GAAG,EAAE,IAAK;AAAA,MACpD;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,QAAQ,MAAM,qBAAqB,OAAO,EAAE,CAAC;AACnD,iBAAK,OAAO,MAAM;AAClB,kBAAM,gBAAgB,KAAK,KAAK,MAAM,OAAO,EAAE,MAAM,MAAM,MAAM,EAAE,CAAC;AACpE,iBAAK,aAAa,cAAc,OAAO,GAAG,cAAc,SAAS,CAAC;AAClE;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,qBAAqB,MAAM;AAClC,UAAI,KAAK,UAAU;AAAW,eAAO,KAAK;AAE1C,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,IAAI;AAC9B,aAAO,SAAS,QAAQ,iBAAiB,EAAE,GAAG,oBAAoB;AAAA,IACnE;AAED,aAAS,kBAAkB;AACzB,YAAM,oBAAoB,OAAO,KAAK,QAAQ,gBAAgB,EAAE,CAAC;AACjE,YAAM,oBAAoB,OAAO,KAAK,QAAQ,gBAAgB,EAAE,CAAC;AACjE,YAAM,gBAAgB,OAAO,KAAK,QAAQ,YAAY,EAAE,CAAC;AAEzD,UAAI,sBAAsB,UAAa,sBAAsB;AAAW;AAExE,YAAM,kBAAkB,mBAAmB,iBAAiB;AAC5D,YAAM,kBAAkB,mBAAmB,iBAAiB;AAC5D,YAAM,cAAc,eAAe,aAAa;AAEhD,YAAM,gBAAgB,gBAAgB;AACtC,YAAM,WAAW,CAAE;AAEnB,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAK;AACpD,cAAM,OAAO,cAAc,CAAC;AAI5B,cAAM,gBAAgB,QAAQ,cAAc,WAAW,KAAK,SAAS,IAAI;AAEzE,YAAI,eAAe;AAGjB,gBAAM,sBAAsB,cAAc;AAI1C,kBAAQ,KAAK,YAAY,mBAAmB;AAAA,QAC7C;AAAA,MACF;AAED,eAAS,QAAQ,YAAY,eAAe;AAC1C,cAAM,oBAAoB,cAAc,aAAa,MAAM;AAC3D,cAAM,QAAQ,gBAAgB,OAAO,UAAU;AAE/C,oBAAY,SAAS,SAAU,QAAQ;AACrC,cAAI,OAAO,SAAS,mBAAmB;AACrC,qBAAS,UAAU,IAAI;AAAA,cACrB;AAAA,cACA,YAAY,mBAAmB,aAAa;AAAA,cAC5C;AAAA,cACA,UAAU,MAAM;AAAA,YACjB;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACF;AAED,YAAM,KAAK,IAAI,QAAS;AAExB,mBAAa;AAAA,QACX,QAAQ,mBAAmB,gBAAgB;AAAA,QAE3C,eAAe,SAAU,YAAY;AACnC,gBAAM,YAAY,SAAS,UAAU;AAErC,cAAI,WAAW;AACb,mBAAO,UAAU;AAAA,UAC7B,OAAiB;AACL,oBAAQ,KAAK,gCAAgC,aAAa,iBAAiB;AAAA,UAC5E;AAAA,QACF;AAAA,QAED,eAAe,SAAU,YAAY,OAAO;AAC1C,gBAAM,YAAY,SAAS,UAAU;AAErC,cAAI,WAAW;AACb,kBAAM,QAAQ,UAAU;AAExB,gBAAI,QAAQ,MAAM,OAAO,OAAO,QAAQ,MAAM,OAAO,KAAK;AACxD,sBAAQ;AAAA,gBACN,gCACA,aACA,YACA,QACA,8BACA,MAAM,OAAO,MACb,YACA,MAAM,OAAO,MACb;AAAA,cACD;AAAA,YACf,WAAuB,MAAM,QAAQ;AACvB,sBAAQ,KAAK,gCAAgC,aAAa,aAAa;AAAA,YACrF,OAAmB;AACL,oBAAM,SAAS,UAAU;AACzB,oBAAM,OAAO,MAAM;AACnB,oBAAM,aAAa,UAAU;AAE7B,qBAAO,SAAU;AAIjB,uBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,sBAAM,YAAY,WAAW,CAAC;AAI9B,oBAAI,UAAU,OAAO,UAAU,IAAI,QAAQ,UAAU,MAAM,IAAI;AAC7D,0BAAQ,MAAM,MAAI;AAAA,oBAChB,KAAK;AACH,6BAAO,SAAS,GAAG,iBAAiB,MAAM,UAAU,SAAS,KAAK,CAAC,CAAC;AACpE;AAAA,oBAEF,KAAK;AACH,6BAAO,SAAS,GAAG,gBAAgB,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,CAAC;AAClF;AAAA,oBAEF;AACE,8BAAQ,KAAK,8CAA8C,MAAM,IAAI;AACrE;AAAA,kBACH;AAAA,gBACnB,OAAuB;AACL,0BAAQ,UAAU,MAAI;AAAA,oBACpB,KAAK;AACH,6BAAO,SAAS,UAAU,GAAG;AAC7B;AAAA,oBAEF,KAAK;AACH,6BAAO,SAAS,GAAG,gBAAgB,UAAU,IAAI,GAAG,UAAU,IAAI,GAAG,UAAU,IAAI,CAAC,CAAC;AACrF;AAAA,oBAEF,KAAK;AACH,6BAAO,MAAM,UAAU,GAAG;AAC1B;AAAA,oBAEF,KAAK;AACH,6BAAO,SAAS,GAAG,iBAAiB,UAAU,KAAK,UAAU,KAAK,CAAC;AACnE;AAAA,kBACH;AAAA,gBACF;AAAA,cACF;AAED,qBAAO,OAAO,KAAK,MAAM;AACzB,qBAAO,OAAO,UAAU,OAAO,UAAU,OAAO,YAAY,OAAO,KAAK;AAExE,uBAAS,UAAU,EAAE,WAAW;AAAA,YACjC;AAAA,UACb,OAAiB;AACL,oBAAQ,IAAI,0BAA0B,aAAa,kBAAkB;AAAA,UACtE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAED,aAAS,mBAAmB,MAAM;AAChC,YAAM,aAAa,CAAE;AAErB,YAAMA,OAAM,QAAQ,cAAc,UAAU,KAAK,KAAK,IAAI;AAE1D,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,YAAI,OAAOO;AAEX,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,kBAAMC,UAAS,IAAI,QAAO,EAAG,UAAU,KAAK,EAAE,UAAW;AACzD,uBAAW,KAAK;AAAA,cACd,KAAK,MAAM,aAAa,KAAK;AAAA,cAC7B,MAAM,MAAM;AAAA,cACZ,KAAKA;AAAA,YACnB,CAAa;AACD;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,YAAAD,UAAS,IAAI,UAAU,UAAU,KAAK;AACtC,uBAAW,KAAK;AAAA,cACd,KAAK,MAAM,aAAa,KAAK;AAAA,cAC7B,MAAM,MAAM;AAAA,cACZ,KAAKA;AAAA,YACnB,CAAa;AACD;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,YAAAA,UAAS,IAAI,UAAU,UAAU,KAAK;AACtC,kBAAM,QAAQ,UAAU,SAAS,MAAM,CAAC,CAAC;AACzC,uBAAW,KAAK;AAAA,cACd,KAAK,MAAM,aAAa,KAAK;AAAA,cAC7B,MAAM,MAAM;AAAA,cACZ,KAAKA;AAAA,cACL;AAAA,YACd,CAAa;AACD;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAID,aAAS,aAAaP,MAAK;AACzB,YAAM,WAAWA,KAAI,qBAAqB,MAAM;AAIhD,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,cAAM,UAAU,SAAS,CAAC;AAE1B,YAAI,QAAQ,aAAa,IAAI,MAAM,OAAO;AACxC,kBAAQ,aAAa,MAAM,YAAY;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAED,UAAM,SAAS,IAAI,QAAS;AAC5B,UAAM,SAAS,IAAI,QAAS;AAE5B,aAAS,UAAUA,MAAK;AACtB,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM,KAAK;AAAA,QAClC,MAAMA,KAAI,aAAa,MAAM;AAAA,QAC7B,IAAIA,KAAI,aAAa,IAAI;AAAA,QACzB,KAAKA,KAAI,aAAa,KAAK;AAAA,QAC3B,QAAQ,IAAI,QAAS;AAAA,QACrB,OAAO,CAAE;AAAA,QACT,iBAAiB,CAAE;AAAA,QACnB,qBAAqB,CAAE;AAAA,QACvB,gBAAgB,CAAE;AAAA,QAClB,oBAAoB,CAAE;AAAA,QACtB,eAAe,CAAE;AAAA,QACjB,YAAY,CAAE;AAAA,MACf;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,YAAI,MAAM,aAAa;AAAG;AAE1B,YAAI;AAEJ,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,iBAAK,MAAM,KAAK,MAAM,aAAa,IAAI,CAAC;AACxC,sBAAU,KAAK;AACf;AAAA,UAEF,KAAK;AACH,iBAAK,gBAAgB,KAAK,QAAQ,MAAM,aAAa,KAAK,CAAC,CAAC;AAC5D;AAAA,UAEF,KAAK;AACH,iBAAK,oBAAoB,KAAK,kBAAkB,KAAK,CAAC;AACtD;AAAA,UAEF,KAAK;AACH,iBAAK,eAAe,KAAK,QAAQ,MAAM,aAAa,KAAK,CAAC,CAAC;AAC3D;AAAA,UAEF,KAAK;AACH,iBAAK,mBAAmB,KAAK,kBAAkB,KAAK,CAAC;AACrD;AAAA,UAEF,KAAK;AACH,iBAAK,cAAc,KAAK,QAAQ,MAAM,aAAa,KAAK,CAAC,CAAC;AAC1D;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,iBAAK,OAAO,SAAS,OAAO,UAAU,KAAK,EAAE,WAAW;AACxD,iBAAK,WAAW,MAAM,aAAa,KAAK,CAAC,IAAI,MAAM;AACnD;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,mBAAO,UAAU,KAAK;AACtB,iBAAK,OAAO,SAAS,OAAO,gBAAgB,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC,CAAC;AACzE,iBAAK,WAAW,MAAM,aAAa,KAAK,CAAC,IAAI,MAAM;AACnD;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,kBAAM,QAAQ,UAAU,SAAS,MAAM,CAAC,CAAC;AACzC,iBAAK,OAAO,SAAS,OAAO,iBAAiB,OAAO,UAAU,KAAK,GAAG,KAAK,CAAC;AAC5E,iBAAK,WAAW,MAAM,aAAa,KAAK,CAAC,IAAI,MAAM;AACnD;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,MAAM,WAAW;AACrC,iBAAK,OAAO,MAAM,OAAO,UAAU,KAAK,CAAC;AACzC,iBAAK,WAAW,MAAM,aAAa,KAAK,CAAC,IAAI,MAAM;AACnD;AAAA,UAEF,KAAK;AACH;AAAA,UAEF;AACE,oBAAQ,IAAI,KAAK;AAAA,QACpB;AAAA,MACF;AAED,UAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,gBAAQ;AAAA,UACN;AAAA,UACA,KAAK;AAAA,QACN;AAAA,MACT,OAAa;AACL,gBAAQ,MAAM,KAAK,EAAE,IAAI;AAAA,MAC1B;AAED,aAAO;AAAA,IACR;AAED,aAAS,kBAAkBA,MAAK;AAC9B,YAAM,OAAO;AAAA,QACX,IAAI,QAAQA,KAAI,aAAa,KAAK,CAAC;AAAA,QACnC,WAAW,CAAE;AAAA,QACb,WAAW,CAAE;AAAA,MACd;AAED,eAAS,IAAI,GAAG,IAAIA,KAAI,WAAW,QAAQ,KAAK;AAC9C,cAAM,QAAQA,KAAI,WAAW,CAAC;AAE9B,gBAAQ,MAAM,UAAQ;AAAA,UACpB,KAAK;AACH,kBAAM,YAAY,MAAM,qBAAqB,mBAAmB;AAEhE,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,oBAAM,WAAW,UAAU,CAAC;AAC5B,oBAAM,SAAS,SAAS,aAAa,QAAQ;AAC7C,oBAAM,SAAS,SAAS,aAAa,QAAQ;AAE7C,mBAAK,UAAU,MAAM,IAAI,QAAQ,MAAM;AAAA,YACxC;AAED;AAAA,UAEF,KAAK;AACH,iBAAK,UAAU,KAAK,QAAQ,MAAM,WAAW,CAAC;AAC9C;AAAA,QAIH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,cAAc,WAAW,QAAQ;AACxC,YAAM,WAAW,CAAE;AACnB,YAAM,iBAAiB,CAAE;AAEzB,UAAI,GAAG,GAAG;AAKV,WAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,cAAM,WAAW,UAAU,CAAC;AAE5B,YAAI;AAEJ,YAAI,QAAQ,QAAQ,GAAG;AACrB,iBAAO,QAAQ,QAAQ;AACvB,6BAAmB,MAAM,QAAQ,QAAQ;AAAA,QACnD,WAAmB,eAAe,QAAQ,GAAG;AAGnC,gBAAM,cAAc,QAAQ,aAAa,QAAQ;AACjD,gBAAM,WAAW,YAAY;AAE7B,mBAASS,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACxC,kBAAM,QAAQ,SAASA,EAAC;AAExB,gBAAI,MAAM,SAAS,SAAS;AAC1B,oBAAMC,QAAO,QAAQ,MAAM,EAAE;AAC7B,iCAAmBA,OAAM,QAAQ,QAAQ;AAAA,YAC1C;AAAA,UACF;AAAA,QACX,OAAe;AACL,kBAAQ,MAAM,sEAAsE,QAAQ;AAAA,QAC7F;AAAA,MACF;AAID,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAClC,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACpC,iBAAO,SAAS,CAAC;AAEjB,cAAI,KAAK,KAAK,SAAS,OAAO,CAAC,EAAE,MAAM;AACrC,2BAAe,CAAC,IAAI;AACpB,iBAAK,YAAY;AACjB;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAID,WAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACpC,eAAO,SAAS,CAAC;AAEjB,YAAI,KAAK,cAAc,OAAO;AAC5B,yBAAe,KAAK,IAAI;AACxB,eAAK,YAAY;AAAA,QAClB;AAAA,MACF;AAID,YAAM,QAAQ,CAAE;AAChB,YAAM,eAAe,CAAE;AAEvB,WAAK,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC1C,eAAO,eAAe,CAAC;AAEvB,cAAM,KAAK,KAAK,IAAI;AACpB,qBAAa,KAAK,KAAK,WAAW;AAAA,MACnC;AAED,aAAO,IAAI,SAAS,OAAO,YAAY;AAAA,IACxC;AAED,aAAS,mBAAmB,MAAM,QAAQ,UAAU;AAGlD,WAAK,SAAS,SAAU,QAAQ;AAC9B,YAAI,OAAO,WAAW,MAAM;AAC1B,cAAI;AAIJ,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,QAAQ,OAAO,CAAC;AAEtB,gBAAI,MAAM,SAAS,OAAO,MAAM;AAC9B,4BAAc,MAAM;AACpB;AAAA,YACD;AAAA,UACF;AAED,cAAI,gBAAgB,QAAW;AAO7B,0BAAc,IAAI,QAAS;AAAA,UAC5B;AAED,mBAAS,KAAK,EAAE,MAAM,QAAQ,aAA0B,WAAW,OAAO;AAAA,QAC3E;AAAA,MACT,CAAO;AAAA,IACF;AAED,aAAS,UAAU,MAAM;AACvB,YAAM,UAAU,CAAE;AAElB,YAAMF,UAAS,KAAK;AACpB,YAAM,QAAQ,KAAK;AACnB,YAAM,OAAO,KAAK;AAClB,YAAM,kBAAkB,KAAK;AAC7B,YAAM,sBAAsB,KAAK;AACjC,YAAM,iBAAiB,KAAK;AAC5B,YAAM,qBAAqB,KAAK;AAChC,YAAM,gBAAgB,KAAK;AAI3B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,gBAAQ,KAAK,QAAQ,MAAM,CAAC,CAAC,CAAC;AAAA,MAC/B;AAID,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,cAAM,iBAAiB,UAAU,gBAAgB,CAAC,CAAC;AAEnD,YAAI,mBAAmB,MAAM;AAC3B,kBAAQ,KAAK,eAAe,OAAO;AAAA,QACpC;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,IAAI,GAAG,KAAK;AAC1D,cAAM,WAAW,oBAAoB,CAAC;AACtC,cAAM,aAAa,cAAc,SAAS,EAAE;AAC5C,cAAM,aAAa,YAAY,WAAW,EAAE;AAC5C,cAAM,aAAa,aAAa,YAAY,SAAS,SAAS;AAE9D,cAAM,YAAY,SAAS;AAC3B,cAAM,SAAS,WAAW,KAAK;AAE/B,cAAM,WAAW,cAAc,WAAW,MAAM;AAEhD,iBAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,gBAAMG,UAAS,WAAW,CAAC;AAE3B,cAAIA,QAAO,eAAe;AACxB,YAAAA,QAAO,KAAK,UAAU,WAAW,KAAK,UAAU;AAChD,YAAAA,QAAO,qBAAsB;AAAA,UAC9B;AAED,kBAAQ,KAAKA,OAAM;AAAA,QACpB;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,IAAI,GAAG,KAAK;AACrD,cAAM,gBAAgB,SAAS,eAAe,CAAC,CAAC;AAEhD,YAAI,kBAAkB,MAAM;AAC1B,kBAAQ,KAAK,cAAc,OAAO;AAAA,QACnC;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG,KAAK;AACzD,cAAM,WAAW,mBAAmB,CAAC;AAKrC,cAAM,aAAa,YAAY,SAAS,EAAE;AAC1C,cAAM,aAAa,aAAa,YAAY,SAAS,SAAS;AAE9D,iBAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,KAAK;AACnD,kBAAQ,KAAK,WAAW,CAAC,CAAC;AAAA,QAC3B;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAK;AACpD,gBAAQ,KAAK,QAAQ,cAAc,CAAC,CAAC,EAAE,OAAO;AAAA,MAC/C;AAED,UAAI;AAEJ,UAAI,MAAM,WAAW,KAAK,QAAQ,WAAW,GAAG;AAC9C,iBAAS,QAAQ,CAAC;AAAA,MAC1B,OAAa;AACL,iBAAS,SAAS,UAAU,IAAI,KAAM,IAAG,IAAI,MAAO;AAEpD,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,iBAAO,IAAI,QAAQ,CAAC,CAAC;AAAA,QACtB;AAAA,MACF;AAED,aAAO,OAAO,SAAS,UAAU,KAAK,MAAM,KAAK;AACjD,aAAO,OAAO,KAAKH,OAAM;AACzB,aAAO,OAAO,UAAU,OAAO,UAAU,OAAO,YAAY,OAAO,KAAK;AAExE,aAAO;AAAA,IACR;AAED,UAAM,mBAAmB,IAAI,kBAAkB,EAAE,OAAO,SAAQ,CAAE;AAElE,aAAS,uBAAuB,MAAM,mBAAmB;AACvD,YAAM,YAAY,CAAE;AAEpB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAM,KAAK,kBAAkB,KAAK,CAAC,CAAC;AAEpC,YAAI,OAAO,QAAW;AACpB,kBAAQ,KAAK,iFAAiF,KAAK,CAAC,CAAC;AACrG,oBAAU,KAAK,gBAAgB;AAAA,QACzC,OAAe;AACL,oBAAU,KAAK,YAAY,EAAE,CAAC;AAAA,QAC/B;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,aAAa,YAAY,mBAAmB;AACnD,YAAM,UAAU,CAAE;AAElB,iBAAW,QAAQ,YAAY;AAC7B,cAAM,WAAW,WAAW,IAAI;AAEhC,cAAM,YAAY,uBAAuB,SAAS,cAAc,iBAAiB;AAIjF,YAAI,UAAU,WAAW,GAAG;AAC1B,cAAI,SAAS,WAAW,SAAS,cAAc;AAC7C,sBAAU,KAAK,IAAI,mBAAmB;AAAA,UAClD,OAAiB;AACL,sBAAU,KAAK,IAAI,mBAAmB;AAAA,UACvC;AAAA,QACF;AAID,cAAM,WAAW,SAAS,KAAK,WAAW,cAAc;AAIxD,cAAM,WAAW,UAAU,WAAW,IAAI,UAAU,CAAC,IAAI;AAIzD,YAAI;AAEJ,gBAAQ,MAAI;AAAA,UACV,KAAK;AACH,qBAAS,IAAI,aAAa,SAAS,MAAM,QAAQ;AACjD;AAAA,UAEF,KAAK;AACH,qBAAS,IAAI,KAAK,SAAS,MAAM,QAAQ;AACzC;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,UAAU;AACZ,uBAAS,IAAI,YAAY,SAAS,MAAM,QAAQ;AAAA,YAC9D,OAAmB;AACL,uBAAS,IAAI,KAAK,SAAS,MAAM,QAAQ;AAAA,YAC1C;AAED;AAAA,QACH;AAED,gBAAQ,KAAK,MAAM;AAAA,MACpB;AAED,aAAO;AAAA,IACR;AAED,aAAS,QAAQ,IAAI;AACnB,aAAO,QAAQ,MAAM,EAAE,MAAM;AAAA,IAC9B;AAED,aAAS,QAAQ,IAAI;AACnB,aAAO,SAAS,QAAQ,MAAM,EAAE,GAAG,SAAS;AAAA,IAC7C;AAID,aAAS,iBAAiBR,MAAK;AAC7B,YAAM,OAAO;AAAA,QACX,MAAMA,KAAI,aAAa,MAAM;AAAA,QAC7B,UAAU,CAAE;AAAA,MACb;AAED,mBAAaA,IAAG;AAEhB,YAAM,WAAW,qBAAqBA,MAAK,MAAM;AAEjD,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAK,SAAS,KAAK,UAAU,SAAS,CAAC,CAAC,CAAC;AAAA,MAC1C;AAED,cAAQ,aAAaA,KAAI,aAAa,IAAI,CAAC,IAAI;AAAA,IAChD;AAED,aAAS,iBAAiB,MAAM;AAC9B,YAAM,QAAQ,IAAI,MAAO;AACzB,YAAM,OAAO,KAAK;AAElB,YAAM,WAAW,KAAK;AAEtB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,cAAM,QAAQ,SAAS,CAAC;AAExB,cAAM,IAAI,QAAQ,MAAM,EAAE,CAAC;AAAA,MAC5B;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,IAAI;AAC1B,aAAO,QAAQ,aAAa,EAAE,MAAM;AAAA,IACrC;AAED,aAAS,eAAe,IAAI;AAC1B,aAAO,SAAS,QAAQ,aAAa,EAAE,GAAG,gBAAgB;AAAA,IAC3D;AAID,aAAS,WAAWA,MAAK;AACvB,YAAM,WAAW,qBAAqBA,MAAK,uBAAuB,EAAE,CAAC;AACrE,aAAO,eAAe,QAAQ,SAAS,aAAa,KAAK,CAAC,CAAC;AAAA,IAC5D;AAED,aAAS,kBAAkB;AACzB,YAAM,QAAQ,QAAQ;AAEtB,UAAI,QAAQ,KAAK,MAAM,MAAM;AAC3B,YAAI,QAAQ,QAAQ,UAAU,MAAM,OAAO;AAGzC,gBAAM,SAAS,CAAE;AAEjB,qBAAW,MAAM,QAAQ,YAAY;AACnC,kBAAM,kBAAkB,aAAa,EAAE;AAEvC,qBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,qBAAO,KAAK,gBAAgB,CAAC,CAAC;AAAA,YAC/B;AAAA,UACF;AAED,qBAAW,KAAK,IAAI,cAAc,WAAW,IAAI,MAAM,CAAC;AAAA,QACzD;AAAA,MACT,OAAa;AACL,mBAAW,MAAM,OAAO;AACtB,qBAAW,KAAK,iBAAiB,EAAE,CAAC;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAKD,aAAS,kBAAkBY,cAAa;AACtC,UAAI,SAAS;AACb,YAAM,QAAQ,CAACA,YAAW;AAE1B,aAAO,MAAM,QAAQ;AACnB,cAAM,OAAO,MAAM,MAAO;AAE1B,YAAI,KAAK,aAAa,KAAK,WAAW;AACpC,oBAAU,KAAK;AAAA,QACzB,OAAe;AACL,oBAAU;AACV,gBAAM,KAAK,MAAM,OAAO,KAAK,UAAU;AAAA,QACxC;AAAA,MACF;AAED,aAAO,OAAO,KAAM;AAAA,IACrB;AAED,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO,EAAE,OAAO,IAAI,QAAS;AAAA,IAC9B;AAED,UAAM,MAAM,IAAI,UAAS,EAAG,gBAAgB,MAAM,iBAAiB;AAEnE,UAAM,UAAU,qBAAqB,KAAK,SAAS,EAAE,CAAC;AAEtD,UAAM,cAAc,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC7D,QAAI,gBAAgB,QAAW;AAG7B,YAAM,eAAe,qBAAqB,aAAa,KAAK,EAAE,CAAC;AAC/D,UAAI;AAEJ,UAAI,cAAc;AAChB,oBAAY,aAAa;AAAA,MACjC,OAAa;AACL,oBAAY,kBAAkB,WAAW;AAAA,MAC1C;AAED,cAAQ,MAAM,wDAAwD,SAAS;AAE/E,aAAO;AAAA,IACR;AAID,UAAM,UAAU,QAAQ,aAAa,SAAS;AAC9C,YAAQ,IAAI,qCAAqC,OAAO;AAExD,UAAM,QAAQ,WAAW,qBAAqB,SAAS,OAAO,EAAE,CAAC,CAAC;AAClE,UAAM,gBAAgB,IAAI,cAAc,KAAK,OAAO;AACpD,kBAAc,QAAQ,KAAK,gBAAgB,IAAI,EAAE,eAAe,KAAK,WAAW;AAEhF,QAAI;AAEJ,QAAI,WAAW;AACb,kBAAY,IAAI,UAAU,KAAK,OAAO;AACtC,gBAAU,QAAQ,KAAK,gBAAgB,IAAI;AAAA,IAC5C;AAID,UAAM,aAAa,CAAE;AACrB,QAAI,aAAa,CAAE;AACnB,QAAI,QAAQ;AAIZ,UAAM,UAAU;AAAA,MACd,YAAY,CAAE;AAAA,MACd,OAAO,CAAE;AAAA,MACT,aAAa,CAAE;AAAA,MACf,QAAQ,CAAE;AAAA,MACV,SAAS,CAAE;AAAA,MACX,WAAW,CAAE;AAAA,MACb,SAAS,CAAE;AAAA,MACX,QAAQ,CAAE;AAAA,MACV,YAAY,CAAE;AAAA,MACd,OAAO,CAAE;AAAA,MACT,cAAc,CAAE;AAAA,MAChB,kBAAkB,CAAE;AAAA,MACpB,eAAe,CAAE;AAAA,MACjB,kBAAkB,CAAE;AAAA,IACrB;AAED,iBAAa,SAAS,sBAAsB,aAAa,cAAc;AACvE,iBAAa,SAAS,2BAA2B,kBAAkB,kBAAkB;AACrF,iBAAa,SAAS,uBAAuB,cAAc,eAAe;AAC1E,iBAAa,SAAS,kBAAkB,SAAS,UAAU;AAC3D,iBAAa,SAAS,mBAAmB,UAAU,WAAW;AAC9D,iBAAa,SAAS,qBAAqB,YAAY,aAAa;AACpE,iBAAa,SAAS,mBAAmB,UAAU,WAAW;AAC9D,iBAAa,SAAS,kBAAkB,SAAS,UAAU;AAC3D,iBAAa,SAAS,sBAAsB,YAAY,aAAa;AACrE,iBAAa,SAAS,iBAAiB,QAAQ,SAAS;AACxD,iBAAa,SAAS,yBAAyB,gBAAgB,gBAAgB;AAC/E,iBAAa,SAAS,6BAA6B,oBAAoB,oBAAoB;AAC3F,iBAAa,SAAS,0BAA0B,iBAAiB,iBAAiB;AAClF,iBAAa,SAAS,SAAS,6BAA6B,oBAAoB;AAEhF,iBAAa,QAAQ,YAAY,cAAc;AAC/C,iBAAa,QAAQ,OAAO,kBAAkB;AAC9C,iBAAa,QAAQ,aAAa,eAAe;AACjD,iBAAa,QAAQ,QAAQ,UAAU;AACvC,iBAAa,QAAQ,SAAS,WAAW;AACzC,iBAAa,QAAQ,WAAW,aAAa;AAC7C,iBAAa,QAAQ,SAAS,WAAW;AACzC,iBAAa,QAAQ,QAAQ,UAAU;AACvC,iBAAa,QAAQ,YAAY,aAAa;AAC9C,iBAAa,QAAQ,cAAc,gBAAgB;AAEnD,oBAAiB;AACjB,oBAAiB;AAEjB,UAAM,QAAQ,WAAW,qBAAqB,SAAS,OAAO,EAAE,CAAC,CAAC;AAClE,UAAM,aAAa;AAEnB,QAAI,MAAM,WAAW,QAAQ;AAC3B,YAAM,WAAW,aAAa,IAAI,MAAM,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC;AAAA,IAC5D;AAED,UAAM,MAAM,eAAe,MAAM,IAAI;AAErC,WAAO;AAAA,MACL,IAAI,aAAa;AACf,gBAAQ,KAAK,0EAA0E;AACvF,eAAO;AAAA,MACR;AAAA,MACD;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACF;AACH;"}