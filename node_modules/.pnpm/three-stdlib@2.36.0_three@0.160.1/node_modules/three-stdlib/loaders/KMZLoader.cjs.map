{"version": 3, "file": "KMZLoader.cjs", "sources": ["../../src/loaders/KMZLoader.js"], "sourcesContent": ["import { FileLoader, Group, Loader, LoadingManager } from 'three'\nimport { ColladaLoader } from '../loaders/ColladaLoader'\nimport { unzipSync } from 'fflate'\n\nclass KMZLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    function findFile(url) {\n      for (const path in zip) {\n        if (path.substr(-url.length) === url) {\n          return zip[path]\n        }\n      }\n    }\n\n    const manager = new LoadingManager()\n    manager.setURLModifier(function (url) {\n      const image = findFile(url)\n\n      if (image) {\n        console.log('Loading', url)\n\n        const blob = new Blob([image.buffer], { type: 'application/octet-stream' })\n        return URL.createObjectURL(blob)\n      }\n\n      return url\n    })\n\n    //\n\n    const zip = unzipSync(new Uint8Array(data))\n\n    if (zip['doc.kml']) {\n      const xml = new DOMParser().parseFromString(fflate.strFromU8(zip['doc.kml']), 'application/xml')\n      const model = xml.querySelector('Placemark Model Link href')\n\n      if (model) {\n        const loader = new ColladaLoader(manager)\n        return loader.parse(fflate.strFromU8(zip[model.textContent]))\n      }\n    } else {\n      console.warn('KMZLoader: Missing doc.kml file.')\n\n      for (const path in zip) {\n        const extension = path.split('.').pop().toLowerCase()\n\n        if (extension === 'dae') {\n          const loader = new ColladaLoader(manager)\n          return loader.parse(fflate.strFromU8(zip[path]))\n        }\n      }\n    }\n\n    console.error(\"KMZLoader: Couldn't find .dae file.\")\n    return { scene: new Group() }\n  }\n}\n\nexport { KMZLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "LoadingManager", "unzipSync", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Group"], "mappings": ";;;;;AAIA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAIC,iBAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,IAAI,CAAC;AAAA,QACzB,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM;AACV,aAAS,SAAS,KAAK;AACrB,iBAAW,QAAQ,KAAK;AACtB,YAAI,KAAK,OAAO,CAAC,IAAI,MAAM,MAAM,KAAK;AACpC,iBAAO,IAAI,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAED,UAAM,UAAU,IAAIC,qBAAgB;AACpC,YAAQ,eAAe,SAAU,KAAK;AACpC,YAAM,QAAQ,SAAS,GAAG;AAE1B,UAAI,OAAO;AACT,gBAAQ,IAAI,WAAW,GAAG;AAE1B,cAAM,OAAO,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,MAAM,4BAA4B;AAC1E,eAAO,IAAI,gBAAgB,IAAI;AAAA,MAChC;AAED,aAAO;AAAA,IACb,CAAK;AAID,UAAM,MAAMC,SAAS,UAAC,IAAI,WAAW,IAAI,CAAC;AAE1C,QAAI,IAAI,SAAS,GAAG;AAClB,YAAM,MAAM,IAAI,UAAW,EAAC,gBAAgB,OAAO,UAAU,IAAI,SAAS,CAAC,GAAG,iBAAiB;AAC/F,YAAM,QAAQ,IAAI,cAAc,2BAA2B;AAE3D,UAAI,OAAO;AACT,cAAM,SAAS,IAAIC,cAAa,cAAC,OAAO;AACxC,eAAO,OAAO,MAAM,OAAO,UAAU,IAAI,MAAM,WAAW,CAAC,CAAC;AAAA,MAC7D;AAAA,IACP,OAAW;AACL,cAAQ,KAAK,kCAAkC;AAE/C,iBAAW,QAAQ,KAAK;AACtB,cAAM,YAAY,KAAK,MAAM,GAAG,EAAE,IAAK,EAAC,YAAa;AAErD,YAAI,cAAc,OAAO;AACvB,gBAAM,SAAS,IAAIA,cAAa,cAAC,OAAO;AACxC,iBAAO,OAAO,MAAM,OAAO,UAAU,IAAI,IAAI,CAAC,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAED,YAAQ,MAAM,qCAAqC;AACnD,WAAO,EAAE,OAAO,IAAIC,MAAAA,QAAS;AAAA,EAC9B;AACH;;"}