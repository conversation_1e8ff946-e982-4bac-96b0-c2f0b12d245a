{"version": 3, "file": "3MFLoader.cjs", "sources": ["../../src/loaders/3MFLoader.js"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshPhongMaterial,\n  MeshStandardMaterial,\n  MirroredRepeatWrapping,\n  NearestFilter,\n  RepeatWrapping,\n  TextureLoader,\n} from 'three'\nimport { unzipSync } from 'fflate'\nimport { decodeText } from '../_polyfill/LoaderUtils'\n\n/**\n *\n * 3D Manufacturing Format (3MF) specification: https://3mf.io/specification/\n *\n * The following features from the core specification are supported:\n *\n * - 3D Models\n * - Object Resources (Meshes and Components)\n * - Material Resources (Base Materials)\n *\n * 3MF Materials and Properties Extension are only partially supported.\n *\n * - Texture 2D\n * - Texture 2D Groups\n * - Color Groups (Vertex Colors)\n * - Metallic Display Properties (PBR)\n */\n\nclass ThreeMFLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n    this.availableExtensions = []\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data) {\n    const scope = this\n    const textureLoader = new TextureLoader(this.manager)\n\n    function loadDocument(data) {\n      let zip = null\n      let file = null\n\n      let relsName\n      let modelRelsName\n      const modelPartNames = []\n      const printTicketPartNames = []\n      const texturesPartNames = []\n      const otherPartNames = []\n\n      let modelRels\n      const modelParts = {}\n      const printTicketParts = {}\n      const texturesParts = {}\n      const otherParts = {}\n\n      try {\n        zip = unzipSync(new Uint8Array(data))\n      } catch (e) {\n        if (e instanceof ReferenceError) {\n          console.error('THREE.3MFLoader: fflate missing and file is compressed.')\n          return null\n        }\n      }\n\n      for (file in zip) {\n        if (file.match(/\\_rels\\/.rels$/)) {\n          relsName = file\n        } else if (file.match(/3D\\/_rels\\/.*\\.model\\.rels$/)) {\n          modelRelsName = file\n        } else if (file.match(/^3D\\/.*\\.model$/)) {\n          modelPartNames.push(file)\n        } else if (file.match(/^3D\\/Metadata\\/.*\\.xml$/)) {\n          printTicketPartNames.push(file)\n        } else if (file.match(/^3D\\/Textures?\\/.*/)) {\n          texturesPartNames.push(file)\n        } else if (file.match(/^3D\\/Other\\/.*/)) {\n          otherPartNames.push(file)\n        }\n      }\n\n      //\n\n      const relsView = zip[relsName]\n      const relsFileText = decodeText(relsView)\n      const rels = parseRelsXml(relsFileText)\n\n      //\n\n      if (modelRelsName) {\n        const relsView = zip[modelRelsName]\n        const relsFileText = decodeText(relsView)\n        modelRels = parseRelsXml(relsFileText)\n      }\n\n      //\n\n      for (let i = 0; i < modelPartNames.length; i++) {\n        const modelPart = modelPartNames[i]\n        const view = zip[modelPart]\n\n        const fileText = decodeText(view)\n        const xmlData = new DOMParser().parseFromString(fileText, 'application/xml')\n\n        if (xmlData.documentElement.nodeName.toLowerCase() !== 'model') {\n          console.error('THREE.3MFLoader: Error loading 3MF - no 3MF document found: ', modelPart)\n        }\n\n        const modelNode = xmlData.querySelector('model')\n        const extensions = {}\n\n        for (let i = 0; i < modelNode.attributes.length; i++) {\n          const attr = modelNode.attributes[i]\n          if (attr.name.match(/^xmlns:(.+)$/)) {\n            extensions[attr.value] = RegExp.$1\n          }\n        }\n\n        const modelData = parseModelNode(modelNode)\n        modelData['xml'] = modelNode\n\n        if (0 < Object.keys(extensions).length) {\n          modelData['extensions'] = extensions\n        }\n\n        modelParts[modelPart] = modelData\n      }\n\n      //\n\n      for (let i = 0; i < texturesPartNames.length; i++) {\n        const texturesPartName = texturesPartNames[i]\n        texturesParts[texturesPartName] = zip[texturesPartName].buffer\n      }\n\n      return {\n        rels: rels,\n        modelRels: modelRels,\n        model: modelParts,\n        printTicket: printTicketParts,\n        texture: texturesParts,\n        other: otherParts,\n      }\n    }\n\n    function parseRelsXml(relsFileText) {\n      const relationships = []\n\n      const relsXmlData = new DOMParser().parseFromString(relsFileText, 'application/xml')\n\n      const relsNodes = relsXmlData.querySelectorAll('Relationship')\n\n      for (let i = 0; i < relsNodes.length; i++) {\n        const relsNode = relsNodes[i]\n\n        const relationship = {\n          target: relsNode.getAttribute('Target'), //required\n          id: relsNode.getAttribute('Id'), //required\n          type: relsNode.getAttribute('Type'), //required\n        }\n\n        relationships.push(relationship)\n      }\n\n      return relationships\n    }\n\n    function parseMetadataNodes(metadataNodes) {\n      const metadataData = {}\n\n      for (let i = 0; i < metadataNodes.length; i++) {\n        const metadataNode = metadataNodes[i]\n        const name = metadataNode.getAttribute('name')\n        const validNames = [\n          'Title',\n          'Designer',\n          'Description',\n          'Copyright',\n          'LicenseTerms',\n          'Rating',\n          'CreationDate',\n          'ModificationDate',\n        ]\n\n        if (0 <= validNames.indexOf(name)) {\n          metadataData[name] = metadataNode.textContent\n        }\n      }\n\n      return metadataData\n    }\n\n    function parseBasematerialsNode(basematerialsNode) {\n      const basematerialsData = {\n        id: basematerialsNode.getAttribute('id'), // required\n        basematerials: [],\n      }\n\n      const basematerialNodes = basematerialsNode.querySelectorAll('base')\n\n      for (let i = 0; i < basematerialNodes.length; i++) {\n        const basematerialNode = basematerialNodes[i]\n        const basematerialData = parseBasematerialNode(basematerialNode)\n        basematerialData.index = i // the order and count of the material nodes form an implicit 0-based index\n        basematerialsData.basematerials.push(basematerialData)\n      }\n\n      return basematerialsData\n    }\n\n    function parseTexture2DNode(texture2DNode) {\n      const texture2dData = {\n        id: texture2DNode.getAttribute('id'), // required\n        path: texture2DNode.getAttribute('path'), // required\n        contenttype: texture2DNode.getAttribute('contenttype'), // required\n        tilestyleu: texture2DNode.getAttribute('tilestyleu'),\n        tilestylev: texture2DNode.getAttribute('tilestylev'),\n        filter: texture2DNode.getAttribute('filter'),\n      }\n\n      return texture2dData\n    }\n\n    function parseTextures2DGroupNode(texture2DGroupNode) {\n      const texture2DGroupData = {\n        id: texture2DGroupNode.getAttribute('id'), // required\n        texid: texture2DGroupNode.getAttribute('texid'), // required\n        displaypropertiesid: texture2DGroupNode.getAttribute('displaypropertiesid'),\n      }\n\n      const tex2coordNodes = texture2DGroupNode.querySelectorAll('tex2coord')\n\n      const uvs = []\n\n      for (let i = 0; i < tex2coordNodes.length; i++) {\n        const tex2coordNode = tex2coordNodes[i]\n        const u = tex2coordNode.getAttribute('u')\n        const v = tex2coordNode.getAttribute('v')\n\n        uvs.push(parseFloat(u), parseFloat(v))\n      }\n\n      texture2DGroupData['uvs'] = new Float32Array(uvs)\n\n      return texture2DGroupData\n    }\n\n    function parseColorGroupNode(colorGroupNode) {\n      const colorGroupData = {\n        id: colorGroupNode.getAttribute('id'), // required\n        displaypropertiesid: colorGroupNode.getAttribute('displaypropertiesid'),\n      }\n\n      const colorNodes = colorGroupNode.querySelectorAll('color')\n\n      const colors = []\n      const colorObject = new Color()\n\n      for (let i = 0; i < colorNodes.length; i++) {\n        const colorNode = colorNodes[i]\n        const color = colorNode.getAttribute('color')\n\n        colorObject.setStyle(color.substring(0, 7))\n        colorObject.convertSRGBToLinear() // color is in sRGB\n\n        colors.push(colorObject.r, colorObject.g, colorObject.b)\n      }\n\n      colorGroupData['colors'] = new Float32Array(colors)\n\n      return colorGroupData\n    }\n\n    function parseMetallicDisplaypropertiesNode(metallicDisplaypropetiesNode) {\n      const metallicDisplaypropertiesData = {\n        id: metallicDisplaypropetiesNode.getAttribute('id'), // required\n      }\n\n      const metallicNodes = metallicDisplaypropetiesNode.querySelectorAll('pbmetallic')\n\n      const metallicData = []\n\n      for (let i = 0; i < metallicNodes.length; i++) {\n        const metallicNode = metallicNodes[i]\n\n        metallicData.push({\n          name: metallicNode.getAttribute('name'), // required\n          metallicness: parseFloat(metallicNode.getAttribute('metallicness')), // required\n          roughness: parseFloat(metallicNode.getAttribute('roughness')), // required\n        })\n      }\n\n      metallicDisplaypropertiesData.data = metallicData\n\n      return metallicDisplaypropertiesData\n    }\n\n    function parseBasematerialNode(basematerialNode) {\n      const basematerialData = {}\n\n      basematerialData['name'] = basematerialNode.getAttribute('name') // required\n      basematerialData['displaycolor'] = basematerialNode.getAttribute('displaycolor') // required\n      basematerialData['displaypropertiesid'] = basematerialNode.getAttribute('displaypropertiesid')\n\n      return basematerialData\n    }\n\n    function parseMeshNode(meshNode) {\n      const meshData = {}\n\n      const vertices = []\n      const vertexNodes = meshNode.querySelectorAll('vertices vertex')\n\n      for (let i = 0; i < vertexNodes.length; i++) {\n        const vertexNode = vertexNodes[i]\n        const x = vertexNode.getAttribute('x')\n        const y = vertexNode.getAttribute('y')\n        const z = vertexNode.getAttribute('z')\n\n        vertices.push(parseFloat(x), parseFloat(y), parseFloat(z))\n      }\n\n      meshData['vertices'] = new Float32Array(vertices)\n\n      const triangleProperties = []\n      const triangles = []\n      const triangleNodes = meshNode.querySelectorAll('triangles triangle')\n\n      for (let i = 0; i < triangleNodes.length; i++) {\n        const triangleNode = triangleNodes[i]\n        const v1 = triangleNode.getAttribute('v1')\n        const v2 = triangleNode.getAttribute('v2')\n        const v3 = triangleNode.getAttribute('v3')\n        const p1 = triangleNode.getAttribute('p1')\n        const p2 = triangleNode.getAttribute('p2')\n        const p3 = triangleNode.getAttribute('p3')\n        const pid = triangleNode.getAttribute('pid')\n\n        const triangleProperty = {}\n\n        triangleProperty['v1'] = parseInt(v1, 10)\n        triangleProperty['v2'] = parseInt(v2, 10)\n        triangleProperty['v3'] = parseInt(v3, 10)\n\n        triangles.push(triangleProperty['v1'], triangleProperty['v2'], triangleProperty['v3'])\n\n        // optional\n\n        if (p1) {\n          triangleProperty['p1'] = parseInt(p1, 10)\n        }\n\n        if (p2) {\n          triangleProperty['p2'] = parseInt(p2, 10)\n        }\n\n        if (p3) {\n          triangleProperty['p3'] = parseInt(p3, 10)\n        }\n\n        if (pid) {\n          triangleProperty['pid'] = pid\n        }\n\n        if (0 < Object.keys(triangleProperty).length) {\n          triangleProperties.push(triangleProperty)\n        }\n      }\n\n      meshData['triangleProperties'] = triangleProperties\n      meshData['triangles'] = new Uint32Array(triangles)\n\n      return meshData\n    }\n\n    function parseComponentsNode(componentsNode) {\n      const components = []\n\n      const componentNodes = componentsNode.querySelectorAll('component')\n\n      for (let i = 0; i < componentNodes.length; i++) {\n        const componentNode = componentNodes[i]\n        const componentData = parseComponentNode(componentNode)\n        components.push(componentData)\n      }\n\n      return components\n    }\n\n    function parseComponentNode(componentNode) {\n      const componentData = {}\n\n      componentData['objectId'] = componentNode.getAttribute('objectid') // required\n\n      const transform = componentNode.getAttribute('transform')\n\n      if (transform) {\n        componentData['transform'] = parseTransform(transform)\n      }\n\n      return componentData\n    }\n\n    function parseTransform(transform) {\n      const t = []\n      transform.split(' ').forEach(function (s) {\n        t.push(parseFloat(s))\n      })\n\n      const matrix = new Matrix4()\n      matrix.set(t[0], t[3], t[6], t[9], t[1], t[4], t[7], t[10], t[2], t[5], t[8], t[11], 0.0, 0.0, 0.0, 1.0)\n\n      return matrix\n    }\n\n    function parseObjectNode(objectNode) {\n      const objectData = {\n        type: objectNode.getAttribute('type'),\n      }\n\n      const id = objectNode.getAttribute('id')\n\n      if (id) {\n        objectData['id'] = id\n      }\n\n      const pid = objectNode.getAttribute('pid')\n\n      if (pid) {\n        objectData['pid'] = pid\n      }\n\n      const pindex = objectNode.getAttribute('pindex')\n\n      if (pindex) {\n        objectData['pindex'] = pindex\n      }\n\n      const thumbnail = objectNode.getAttribute('thumbnail')\n\n      if (thumbnail) {\n        objectData['thumbnail'] = thumbnail\n      }\n\n      const partnumber = objectNode.getAttribute('partnumber')\n\n      if (partnumber) {\n        objectData['partnumber'] = partnumber\n      }\n\n      const name = objectNode.getAttribute('name')\n\n      if (name) {\n        objectData['name'] = name\n      }\n\n      const meshNode = objectNode.querySelector('mesh')\n\n      if (meshNode) {\n        objectData['mesh'] = parseMeshNode(meshNode)\n      }\n\n      const componentsNode = objectNode.querySelector('components')\n\n      if (componentsNode) {\n        objectData['components'] = parseComponentsNode(componentsNode)\n      }\n\n      return objectData\n    }\n\n    function parseResourcesNode(resourcesNode) {\n      const resourcesData = {}\n\n      resourcesData['basematerials'] = {}\n      const basematerialsNodes = resourcesNode.querySelectorAll('basematerials')\n\n      for (let i = 0; i < basematerialsNodes.length; i++) {\n        const basematerialsNode = basematerialsNodes[i]\n        const basematerialsData = parseBasematerialsNode(basematerialsNode)\n        resourcesData['basematerials'][basematerialsData['id']] = basematerialsData\n      }\n\n      //\n\n      resourcesData['texture2d'] = {}\n      const textures2DNodes = resourcesNode.querySelectorAll('texture2d')\n\n      for (let i = 0; i < textures2DNodes.length; i++) {\n        const textures2DNode = textures2DNodes[i]\n        const texture2DData = parseTexture2DNode(textures2DNode)\n        resourcesData['texture2d'][texture2DData['id']] = texture2DData\n      }\n\n      //\n\n      resourcesData['colorgroup'] = {}\n      const colorGroupNodes = resourcesNode.querySelectorAll('colorgroup')\n\n      for (let i = 0; i < colorGroupNodes.length; i++) {\n        const colorGroupNode = colorGroupNodes[i]\n        const colorGroupData = parseColorGroupNode(colorGroupNode)\n        resourcesData['colorgroup'][colorGroupData['id']] = colorGroupData\n      }\n\n      //\n\n      resourcesData['pbmetallicdisplayproperties'] = {}\n      const pbmetallicdisplaypropertiesNodes = resourcesNode.querySelectorAll('pbmetallicdisplayproperties')\n\n      for (let i = 0; i < pbmetallicdisplaypropertiesNodes.length; i++) {\n        const pbmetallicdisplaypropertiesNode = pbmetallicdisplaypropertiesNodes[i]\n        const pbmetallicdisplaypropertiesData = parseMetallicDisplaypropertiesNode(pbmetallicdisplaypropertiesNode)\n        resourcesData['pbmetallicdisplayproperties'][\n          pbmetallicdisplaypropertiesData['id']\n        ] = pbmetallicdisplaypropertiesData\n      }\n\n      //\n\n      resourcesData['texture2dgroup'] = {}\n      const textures2DGroupNodes = resourcesNode.querySelectorAll('texture2dgroup')\n\n      for (let i = 0; i < textures2DGroupNodes.length; i++) {\n        const textures2DGroupNode = textures2DGroupNodes[i]\n        const textures2DGroupData = parseTextures2DGroupNode(textures2DGroupNode)\n        resourcesData['texture2dgroup'][textures2DGroupData['id']] = textures2DGroupData\n      }\n\n      //\n\n      resourcesData['object'] = {}\n      const objectNodes = resourcesNode.querySelectorAll('object')\n\n      for (let i = 0; i < objectNodes.length; i++) {\n        const objectNode = objectNodes[i]\n        const objectData = parseObjectNode(objectNode)\n        resourcesData['object'][objectData['id']] = objectData\n      }\n\n      return resourcesData\n    }\n\n    function parseBuildNode(buildNode) {\n      const buildData = []\n      const itemNodes = buildNode.querySelectorAll('item')\n\n      for (let i = 0; i < itemNodes.length; i++) {\n        const itemNode = itemNodes[i]\n        const buildItem = {\n          objectId: itemNode.getAttribute('objectid'),\n        }\n        const transform = itemNode.getAttribute('transform')\n\n        if (transform) {\n          buildItem['transform'] = parseTransform(transform)\n        }\n\n        buildData.push(buildItem)\n      }\n\n      return buildData\n    }\n\n    function parseModelNode(modelNode) {\n      const modelData = { unit: modelNode.getAttribute('unit') || 'millimeter' }\n      const metadataNodes = modelNode.querySelectorAll('metadata')\n\n      if (metadataNodes) {\n        modelData['metadata'] = parseMetadataNodes(metadataNodes)\n      }\n\n      const resourcesNode = modelNode.querySelector('resources')\n\n      if (resourcesNode) {\n        modelData['resources'] = parseResourcesNode(resourcesNode)\n      }\n\n      const buildNode = modelNode.querySelector('build')\n\n      if (buildNode) {\n        modelData['build'] = parseBuildNode(buildNode)\n      }\n\n      return modelData\n    }\n\n    function buildTexture(texture2dgroup, objects, modelData, textureData) {\n      const texid = texture2dgroup.texid\n      const texture2ds = modelData.resources.texture2d\n      const texture2d = texture2ds[texid]\n\n      if (texture2d) {\n        const data = textureData[texture2d.path]\n        const type = texture2d.contenttype\n\n        const blob = new Blob([data], { type: type })\n        const sourceURI = URL.createObjectURL(blob)\n\n        const texture = textureLoader.load(sourceURI, function () {\n          URL.revokeObjectURL(sourceURI)\n        })\n\n        if ('colorSpace' in texture) texture.colorSpace = 'srgb'\n        else texture.encoding = 3001 // sRGBEncoding\n\n        // texture parameters\n\n        switch (texture2d.tilestyleu) {\n          case 'wrap':\n            texture.wrapS = RepeatWrapping\n            break\n\n          case 'mirror':\n            texture.wrapS = MirroredRepeatWrapping\n            break\n\n          case 'none':\n          case 'clamp':\n            texture.wrapS = ClampToEdgeWrapping\n            break\n\n          default:\n            texture.wrapS = RepeatWrapping\n        }\n\n        switch (texture2d.tilestylev) {\n          case 'wrap':\n            texture.wrapT = RepeatWrapping\n            break\n\n          case 'mirror':\n            texture.wrapT = MirroredRepeatWrapping\n            break\n\n          case 'none':\n          case 'clamp':\n            texture.wrapT = ClampToEdgeWrapping\n            break\n\n          default:\n            texture.wrapT = RepeatWrapping\n        }\n\n        switch (texture2d.filter) {\n          case 'auto':\n            texture.magFilter = LinearFilter\n            texture.minFilter = LinearMipmapLinearFilter\n            break\n\n          case 'linear':\n            texture.magFilter = LinearFilter\n            texture.minFilter = LinearFilter\n            break\n\n          case 'nearest':\n            texture.magFilter = NearestFilter\n            texture.minFilter = NearestFilter\n            break\n\n          default:\n            texture.magFilter = LinearFilter\n            texture.minFilter = LinearMipmapLinearFilter\n        }\n\n        return texture\n      } else {\n        return null\n      }\n    }\n\n    function buildBasematerialsMeshes(\n      basematerials,\n      triangleProperties,\n      meshData,\n      objects,\n      modelData,\n      textureData,\n      objectData,\n    ) {\n      const objectPindex = objectData.pindex\n\n      const materialMap = {}\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n        const pindex = triangleProperty.p1 !== undefined ? triangleProperty.p1 : objectPindex\n\n        if (materialMap[pindex] === undefined) materialMap[pindex] = []\n\n        materialMap[pindex].push(triangleProperty)\n      }\n\n      //\n\n      const keys = Object.keys(materialMap)\n      const meshes = []\n\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const materialIndex = keys[i]\n        const trianglePropertiesProps = materialMap[materialIndex]\n        const basematerialData = basematerials.basematerials[materialIndex]\n        const material = getBuild(basematerialData, objects, modelData, textureData, objectData, buildBasematerial)\n\n        //\n\n        const geometry = new BufferGeometry()\n\n        const positionData = []\n\n        const vertices = meshData.vertices\n\n        for (let j = 0, jl = trianglePropertiesProps.length; j < jl; j++) {\n          const triangleProperty = trianglePropertiesProps[j]\n\n          positionData.push(vertices[triangleProperty.v1 * 3 + 0])\n          positionData.push(vertices[triangleProperty.v1 * 3 + 1])\n          positionData.push(vertices[triangleProperty.v1 * 3 + 2])\n\n          positionData.push(vertices[triangleProperty.v2 * 3 + 0])\n          positionData.push(vertices[triangleProperty.v2 * 3 + 1])\n          positionData.push(vertices[triangleProperty.v2 * 3 + 2])\n\n          positionData.push(vertices[triangleProperty.v3 * 3 + 0])\n          positionData.push(vertices[triangleProperty.v3 * 3 + 1])\n          positionData.push(vertices[triangleProperty.v3 * 3 + 2])\n        }\n\n        geometry.setAttribute('position', new Float32BufferAttribute(positionData, 3))\n\n        //\n\n        const mesh = new Mesh(geometry, material)\n        meshes.push(mesh)\n      }\n\n      return meshes\n    }\n\n    function buildTexturedMesh(\n      texture2dgroup,\n      triangleProperties,\n      meshData,\n      objects,\n      modelData,\n      textureData,\n      objectData,\n    ) {\n      // geometry\n\n      const geometry = new BufferGeometry()\n\n      const positionData = []\n      const uvData = []\n\n      const vertices = meshData.vertices\n      const uvs = texture2dgroup.uvs\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n\n        positionData.push(vertices[triangleProperty.v1 * 3 + 0])\n        positionData.push(vertices[triangleProperty.v1 * 3 + 1])\n        positionData.push(vertices[triangleProperty.v1 * 3 + 2])\n\n        positionData.push(vertices[triangleProperty.v2 * 3 + 0])\n        positionData.push(vertices[triangleProperty.v2 * 3 + 1])\n        positionData.push(vertices[triangleProperty.v2 * 3 + 2])\n\n        positionData.push(vertices[triangleProperty.v3 * 3 + 0])\n        positionData.push(vertices[triangleProperty.v3 * 3 + 1])\n        positionData.push(vertices[triangleProperty.v3 * 3 + 2])\n\n        //\n\n        uvData.push(uvs[triangleProperty.p1 * 2 + 0])\n        uvData.push(uvs[triangleProperty.p1 * 2 + 1])\n\n        uvData.push(uvs[triangleProperty.p2 * 2 + 0])\n        uvData.push(uvs[triangleProperty.p2 * 2 + 1])\n\n        uvData.push(uvs[triangleProperty.p3 * 2 + 0])\n        uvData.push(uvs[triangleProperty.p3 * 2 + 1])\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(positionData, 3))\n      geometry.setAttribute('uv', new Float32BufferAttribute(uvData, 2))\n\n      // material\n\n      const texture = getBuild(texture2dgroup, objects, modelData, textureData, objectData, buildTexture)\n\n      const material = new MeshPhongMaterial({ map: texture, flatShading: true })\n\n      // mesh\n\n      const mesh = new Mesh(geometry, material)\n\n      return mesh\n    }\n\n    function buildVertexColorMesh(colorgroup, triangleProperties, meshData, objects, modelData, objectData) {\n      // geometry\n\n      const geometry = new BufferGeometry()\n\n      const positionData = []\n      const colorData = []\n\n      const vertices = meshData.vertices\n      const colors = colorgroup.colors\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n\n        const v1 = triangleProperty.v1\n        const v2 = triangleProperty.v2\n        const v3 = triangleProperty.v3\n\n        positionData.push(vertices[v1 * 3 + 0])\n        positionData.push(vertices[v1 * 3 + 1])\n        positionData.push(vertices[v1 * 3 + 2])\n\n        positionData.push(vertices[v2 * 3 + 0])\n        positionData.push(vertices[v2 * 3 + 1])\n        positionData.push(vertices[v2 * 3 + 2])\n\n        positionData.push(vertices[v3 * 3 + 0])\n        positionData.push(vertices[v3 * 3 + 1])\n        positionData.push(vertices[v3 * 3 + 2])\n\n        //\n\n        const p1 = triangleProperty.p1 !== undefined ? triangleProperty.p1 : objectData.pindex\n        const p2 = triangleProperty.p2 !== undefined ? triangleProperty.p2 : p1\n        const p3 = triangleProperty.p3 !== undefined ? triangleProperty.p3 : p1\n\n        colorData.push(colors[p1 * 3 + 0])\n        colorData.push(colors[p1 * 3 + 1])\n        colorData.push(colors[p1 * 3 + 2])\n\n        colorData.push(colors[p2 * 3 + 0])\n        colorData.push(colors[p2 * 3 + 1])\n        colorData.push(colors[p2 * 3 + 2])\n\n        colorData.push(colors[p3 * 3 + 0])\n        colorData.push(colors[p3 * 3 + 1])\n        colorData.push(colors[p3 * 3 + 2])\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(positionData, 3))\n      geometry.setAttribute('color', new Float32BufferAttribute(colorData, 3))\n\n      // material\n\n      const material = new MeshPhongMaterial({ vertexColors: true, flatShading: true })\n\n      // mesh\n\n      const mesh = new Mesh(geometry, material)\n\n      return mesh\n    }\n\n    function buildDefaultMesh(meshData) {\n      const geometry = new BufferGeometry()\n      geometry.setIndex(new BufferAttribute(meshData['triangles'], 1))\n      geometry.setAttribute('position', new BufferAttribute(meshData['vertices'], 3))\n\n      const material = new MeshPhongMaterial({ color: 0xaaaaff, flatShading: true })\n\n      const mesh = new Mesh(geometry, material)\n\n      return mesh\n    }\n\n    function buildMeshes(resourceMap, meshData, objects, modelData, textureData, objectData) {\n      const keys = Object.keys(resourceMap)\n      const meshes = []\n\n      for (let i = 0, il = keys.length; i < il; i++) {\n        const resourceId = keys[i]\n        const triangleProperties = resourceMap[resourceId]\n        const resourceType = getResourceType(resourceId, modelData)\n\n        switch (resourceType) {\n          case 'material':\n            const basematerials = modelData.resources.basematerials[resourceId]\n            const newMeshes = buildBasematerialsMeshes(\n              basematerials,\n              triangleProperties,\n              meshData,\n              objects,\n              modelData,\n              textureData,\n              objectData,\n            )\n\n            for (let j = 0, jl = newMeshes.length; j < jl; j++) {\n              meshes.push(newMeshes[j])\n            }\n\n            break\n\n          case 'texture':\n            const texture2dgroup = modelData.resources.texture2dgroup[resourceId]\n            meshes.push(\n              buildTexturedMesh(\n                texture2dgroup,\n                triangleProperties,\n                meshData,\n                objects,\n                modelData,\n                textureData,\n                objectData,\n              ),\n            )\n            break\n\n          case 'vertexColors':\n            const colorgroup = modelData.resources.colorgroup[resourceId]\n            meshes.push(buildVertexColorMesh(colorgroup, triangleProperties, meshData, objects, modelData, objectData))\n            break\n\n          case 'default':\n            meshes.push(buildDefaultMesh(meshData))\n            break\n\n          default:\n            console.error('THREE.3MFLoader: Unsupported resource type.')\n        }\n      }\n\n      return meshes\n    }\n\n    function getResourceType(pid, modelData) {\n      if (modelData.resources.texture2dgroup[pid] !== undefined) {\n        return 'texture'\n      } else if (modelData.resources.basematerials[pid] !== undefined) {\n        return 'material'\n      } else if (modelData.resources.colorgroup[pid] !== undefined) {\n        return 'vertexColors'\n      } else if (pid === 'default') {\n        return 'default'\n      } else {\n        return undefined\n      }\n    }\n\n    function analyzeObject(modelData, meshData, objectData) {\n      const resourceMap = {}\n\n      const triangleProperties = meshData['triangleProperties']\n\n      const objectPid = objectData.pid\n\n      for (let i = 0, l = triangleProperties.length; i < l; i++) {\n        const triangleProperty = triangleProperties[i]\n        let pid = triangleProperty.pid !== undefined ? triangleProperty.pid : objectPid\n\n        if (pid === undefined) pid = 'default'\n\n        if (resourceMap[pid] === undefined) resourceMap[pid] = []\n\n        resourceMap[pid].push(triangleProperty)\n      }\n\n      return resourceMap\n    }\n\n    function buildGroup(meshData, objects, modelData, textureData, objectData) {\n      const group = new Group()\n\n      const resourceMap = analyzeObject(modelData, meshData, objectData)\n      const meshes = buildMeshes(resourceMap, meshData, objects, modelData, textureData, objectData)\n\n      for (let i = 0, l = meshes.length; i < l; i++) {\n        group.add(meshes[i])\n      }\n\n      return group\n    }\n\n    function applyExtensions(extensions, meshData, modelXml) {\n      if (!extensions) {\n        return\n      }\n\n      const availableExtensions = []\n      const keys = Object.keys(extensions)\n\n      for (let i = 0; i < keys.length; i++) {\n        const ns = keys[i]\n\n        for (let j = 0; j < scope.availableExtensions.length; j++) {\n          const extension = scope.availableExtensions[j]\n\n          if (extension.ns === ns) {\n            availableExtensions.push(extension)\n          }\n        }\n      }\n\n      for (let i = 0; i < availableExtensions.length; i++) {\n        const extension = availableExtensions[i]\n        extension.apply(modelXml, extensions[extension['ns']], meshData)\n      }\n    }\n\n    function getBuild(data, objects, modelData, textureData, objectData, builder) {\n      if (data.build !== undefined) return data.build\n\n      data.build = builder(data, objects, modelData, textureData, objectData)\n\n      return data.build\n    }\n\n    function buildBasematerial(materialData, objects, modelData) {\n      let material\n\n      const displaypropertiesid = materialData.displaypropertiesid\n      const pbmetallicdisplayproperties = modelData.resources.pbmetallicdisplayproperties\n\n      if (displaypropertiesid !== null && pbmetallicdisplayproperties[displaypropertiesid] !== undefined) {\n        // metallic display property, use StandardMaterial\n\n        const pbmetallicdisplayproperty = pbmetallicdisplayproperties[displaypropertiesid]\n        const metallicData = pbmetallicdisplayproperty.data[materialData.index]\n\n        material = new MeshStandardMaterial({\n          flatShading: true,\n          roughness: metallicData.roughness,\n          metalness: metallicData.metallicness,\n        })\n      } else {\n        // otherwise use PhongMaterial\n\n        material = new MeshPhongMaterial({ flatShading: true })\n      }\n\n      material.name = materialData.name\n\n      // displaycolor MUST be specified with a value of a 6 or 8 digit hexadecimal number, e.g. \"#RRGGBB\" or \"#RRGGBBAA\"\n\n      const displaycolor = materialData.displaycolor\n\n      const color = displaycolor.substring(0, 7)\n      material.color.setStyle(color)\n      material.color.convertSRGBToLinear() // displaycolor is in sRGB\n\n      // process alpha if set\n\n      if (displaycolor.length === 9) {\n        material.opacity = parseInt(displaycolor.charAt(7) + displaycolor.charAt(8), 16) / 255\n      }\n\n      return material\n    }\n\n    function buildComposite(compositeData, objects, modelData, textureData) {\n      const composite = new Group()\n\n      for (let j = 0; j < compositeData.length; j++) {\n        const component = compositeData[j]\n        let build = objects[component.objectId]\n\n        if (build === undefined) {\n          buildObject(component.objectId, objects, modelData, textureData)\n          build = objects[component.objectId]\n        }\n\n        const object3D = build.clone()\n\n        // apply component transform\n\n        const transform = component.transform\n\n        if (transform) {\n          object3D.applyMatrix4(transform)\n        }\n\n        composite.add(object3D)\n      }\n\n      return composite\n    }\n\n    function buildObject(objectId, objects, modelData, textureData) {\n      const objectData = modelData['resources']['object'][objectId]\n\n      if (objectData['mesh']) {\n        const meshData = objectData['mesh']\n\n        const extensions = modelData['extensions']\n        const modelXml = modelData['xml']\n\n        applyExtensions(extensions, meshData, modelXml)\n\n        objects[objectData.id] = getBuild(meshData, objects, modelData, textureData, objectData, buildGroup)\n      } else {\n        const compositeData = objectData['components']\n\n        objects[objectData.id] = getBuild(compositeData, objects, modelData, textureData, objectData, buildComposite)\n      }\n    }\n\n    function buildObjects(data3mf) {\n      const modelsData = data3mf.model\n      const modelRels = data3mf.modelRels\n      const objects = {}\n      const modelsKeys = Object.keys(modelsData)\n      const textureData = {}\n\n      // evaluate model relationships to textures\n\n      if (modelRels) {\n        for (let i = 0, l = modelRels.length; i < l; i++) {\n          const modelRel = modelRels[i]\n          const textureKey = modelRel.target.substring(1)\n\n          if (data3mf.texture[textureKey]) {\n            textureData[modelRel.target] = data3mf.texture[textureKey]\n          }\n        }\n      }\n\n      // start build\n\n      for (let i = 0; i < modelsKeys.length; i++) {\n        const modelsKey = modelsKeys[i]\n        const modelData = modelsData[modelsKey]\n\n        const objectIds = Object.keys(modelData['resources']['object'])\n\n        for (let j = 0; j < objectIds.length; j++) {\n          const objectId = objectIds[j]\n\n          buildObject(objectId, objects, modelData, textureData)\n        }\n      }\n\n      return objects\n    }\n\n    function fetch3DModelPart(rels) {\n      for (let i = 0; i < rels.length; i++) {\n        const rel = rels[i]\n        const extension = rel.target.split('.').pop()\n\n        if (extension.toLowerCase() === 'model') return rel\n      }\n    }\n\n    function build(objects, data3mf) {\n      const group = new Group()\n\n      const relationship = fetch3DModelPart(data3mf['rels'])\n      const buildData = data3mf.model[relationship['target'].substring(1)]['build']\n\n      for (let i = 0; i < buildData.length; i++) {\n        const buildItem = buildData[i]\n        const object3D = objects[buildItem['objectId']]\n\n        // apply transform\n\n        const transform = buildItem['transform']\n\n        if (transform) {\n          object3D.applyMatrix4(transform)\n        }\n\n        group.add(object3D)\n      }\n\n      return group\n    }\n\n    const data3mf = loadDocument(data)\n    const objects = buildObjects(data3mf)\n\n    return build(objects, data3mf)\n  }\n\n  addExtension(extension) {\n    this.availableExtensions.push(extension)\n  }\n}\n\nexport { ThreeMFLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "TextureLoader", "data", "unzipSync", "decodeText", "rels<PERSON>iew", "relsFileText", "i", "Color", "Matrix4", "objects", "RepeatWrapping", "MirroredRepeatWrapping", "ClampToEdgeWrapping", "LinearFilter", "LinearMipmapLinearFilter", "NearestFilter", "BufferGeometry", "Float32BufferAttribute", "<PERSON><PERSON>", "MeshPhongMaterial", "BufferAttribute", "Group", "MeshStandardMaterial", "build", "data3mf"], "mappings": ";;;;;AA0CA,MAAM,sBAAsBA,MAAAA,OAAO;AAAA,EACjC,YAAY,SAAS;AACnB,UAAM,OAAO;AACb,SAAK,sBAAsB,CAAE;AAAA,EAC9B;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AACd,UAAM,SAAS,IAAIC,iBAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,QAAQ;AAChB,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,CAAC;AAAA,QAC3B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM;AACV,UAAM,QAAQ;AACd,UAAM,gBAAgB,IAAIC,oBAAc,KAAK,OAAO;AAEpD,aAAS,aAAaC,OAAM;AAC1B,UAAI,MAAM;AACV,UAAI,OAAO;AAEX,UAAI;AACJ,UAAI;AACJ,YAAM,iBAAiB,CAAE;AAEzB,YAAM,oBAAoB,CAAE;AAG5B,UAAI;AACJ,YAAM,aAAa,CAAE;AACrB,YAAM,mBAAmB,CAAE;AAC3B,YAAM,gBAAgB,CAAE;AACxB,YAAM,aAAa,CAAE;AAErB,UAAI;AACF,cAAMC,OAAS,UAAC,IAAI,WAAWD,KAAI,CAAC;AAAA,MACrC,SAAQ,GAAP;AACA,YAAI,aAAa,gBAAgB;AAC/B,kBAAQ,MAAM,yDAAyD;AACvE,iBAAO;AAAA,QACR;AAAA,MACF;AAED,WAAK,QAAQ,KAAK;AAChB,YAAI,KAAK,MAAM,gBAAgB,GAAG;AAChC,qBAAW;AAAA,QACZ,WAAU,KAAK,MAAM,6BAA6B,GAAG;AACpD,0BAAgB;AAAA,QACjB,WAAU,KAAK,MAAM,iBAAiB,GAAG;AACxC,yBAAe,KAAK,IAAI;AAAA,QAClC,WAAmB,KAAK,MAAM,yBAAyB;AAAG;AAAA,iBAEvC,KAAK,MAAM,oBAAoB,GAAG;AAC3C,4BAAkB,KAAK,IAAI;AAAA,QAC5B,WAAU,KAAK,MAAM,gBAAgB;AAAG;AAAA,MAG1C;AAID,YAAM,WAAW,IAAI,QAAQ;AAC7B,YAAM,eAAeE,YAAU,WAAC,QAAQ;AACxC,YAAM,OAAO,aAAa,YAAY;AAItC,UAAI,eAAe;AACjB,cAAMC,YAAW,IAAI,aAAa;AAClC,cAAMC,gBAAeF,YAAU,WAACC,SAAQ;AACxC,oBAAY,aAAaC,aAAY;AAAA,MACtC;AAID,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,YAAY,eAAe,CAAC;AAClC,cAAM,OAAO,IAAI,SAAS;AAE1B,cAAM,WAAWF,YAAU,WAAC,IAAI;AAChC,cAAM,UAAU,IAAI,UAAS,EAAG,gBAAgB,UAAU,iBAAiB;AAE3E,YAAI,QAAQ,gBAAgB,SAAS,YAAW,MAAO,SAAS;AAC9D,kBAAQ,MAAM,gEAAgE,SAAS;AAAA,QACxF;AAED,cAAM,YAAY,QAAQ,cAAc,OAAO;AAC/C,cAAM,aAAa,CAAE;AAErB,iBAASG,KAAI,GAAGA,KAAI,UAAU,WAAW,QAAQA,MAAK;AACpD,gBAAM,OAAO,UAAU,WAAWA,EAAC;AACnC,cAAI,KAAK,KAAK,MAAM,cAAc,GAAG;AACnC,uBAAW,KAAK,KAAK,IAAI,OAAO;AAAA,UACjC;AAAA,QACF;AAED,cAAM,YAAY,eAAe,SAAS;AAC1C,kBAAU,KAAK,IAAI;AAEnB,YAAI,IAAI,OAAO,KAAK,UAAU,EAAE,QAAQ;AACtC,oBAAU,YAAY,IAAI;AAAA,QAC3B;AAED,mBAAW,SAAS,IAAI;AAAA,MACzB;AAID,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,cAAM,mBAAmB,kBAAkB,CAAC;AAC5C,sBAAc,gBAAgB,IAAI,IAAI,gBAAgB,EAAE;AAAA,MACzD;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,IACF;AAED,aAAS,aAAa,cAAc;AAClC,YAAM,gBAAgB,CAAE;AAExB,YAAM,cAAc,IAAI,UAAS,EAAG,gBAAgB,cAAc,iBAAiB;AAEnF,YAAM,YAAY,YAAY,iBAAiB,cAAc;AAE7D,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,WAAW,UAAU,CAAC;AAE5B,cAAM,eAAe;AAAA,UACnB,QAAQ,SAAS,aAAa,QAAQ;AAAA;AAAA,UACtC,IAAI,SAAS,aAAa,IAAI;AAAA;AAAA,UAC9B,MAAM,SAAS,aAAa,MAAM;AAAA;AAAA,QACnC;AAED,sBAAc,KAAK,YAAY;AAAA,MAChC;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,eAAe;AACzC,YAAM,eAAe,CAAE;AAEvB,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,cAAM,eAAe,cAAc,CAAC;AACpC,cAAM,OAAO,aAAa,aAAa,MAAM;AAC7C,cAAM,aAAa;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAED,YAAI,KAAK,WAAW,QAAQ,IAAI,GAAG;AACjC,uBAAa,IAAI,IAAI,aAAa;AAAA,QACnC;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,uBAAuB,mBAAmB;AACjD,YAAM,oBAAoB;AAAA,QACxB,IAAI,kBAAkB,aAAa,IAAI;AAAA;AAAA,QACvC,eAAe,CAAE;AAAA,MAClB;AAED,YAAM,oBAAoB,kBAAkB,iBAAiB,MAAM;AAEnE,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,cAAM,mBAAmB,kBAAkB,CAAC;AAC5C,cAAM,mBAAmB,sBAAsB,gBAAgB;AAC/D,yBAAiB,QAAQ;AACzB,0BAAkB,cAAc,KAAK,gBAAgB;AAAA,MACtD;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,eAAe;AACzC,YAAM,gBAAgB;AAAA,QACpB,IAAI,cAAc,aAAa,IAAI;AAAA;AAAA,QACnC,MAAM,cAAc,aAAa,MAAM;AAAA;AAAA,QACvC,aAAa,cAAc,aAAa,aAAa;AAAA;AAAA,QACrD,YAAY,cAAc,aAAa,YAAY;AAAA,QACnD,YAAY,cAAc,aAAa,YAAY;AAAA,QACnD,QAAQ,cAAc,aAAa,QAAQ;AAAA,MAC5C;AAED,aAAO;AAAA,IACR;AAED,aAAS,yBAAyB,oBAAoB;AACpD,YAAM,qBAAqB;AAAA,QACzB,IAAI,mBAAmB,aAAa,IAAI;AAAA;AAAA,QACxC,OAAO,mBAAmB,aAAa,OAAO;AAAA;AAAA,QAC9C,qBAAqB,mBAAmB,aAAa,qBAAqB;AAAA,MAC3E;AAED,YAAM,iBAAiB,mBAAmB,iBAAiB,WAAW;AAEtE,YAAM,MAAM,CAAE;AAEd,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,gBAAgB,eAAe,CAAC;AACtC,cAAM,IAAI,cAAc,aAAa,GAAG;AACxC,cAAM,IAAI,cAAc,aAAa,GAAG;AAExC,YAAI,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,MACtC;AAED,yBAAmB,KAAK,IAAI,IAAI,aAAa,GAAG;AAEhD,aAAO;AAAA,IACR;AAED,aAAS,oBAAoB,gBAAgB;AAC3C,YAAM,iBAAiB;AAAA,QACrB,IAAI,eAAe,aAAa,IAAI;AAAA;AAAA,QACpC,qBAAqB,eAAe,aAAa,qBAAqB;AAAA,MACvE;AAED,YAAM,aAAa,eAAe,iBAAiB,OAAO;AAE1D,YAAM,SAAS,CAAE;AACjB,YAAM,cAAc,IAAIC,YAAO;AAE/B,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,cAAM,YAAY,WAAW,CAAC;AAC9B,cAAM,QAAQ,UAAU,aAAa,OAAO;AAE5C,oBAAY,SAAS,MAAM,UAAU,GAAG,CAAC,CAAC;AAC1C,oBAAY,oBAAqB;AAEjC,eAAO,KAAK,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC;AAAA,MACxD;AAED,qBAAe,QAAQ,IAAI,IAAI,aAAa,MAAM;AAElD,aAAO;AAAA,IACR;AAED,aAAS,mCAAmC,8BAA8B;AACxE,YAAM,gCAAgC;AAAA,QACpC,IAAI,6BAA6B,aAAa,IAAI;AAAA;AAAA,MACnD;AAED,YAAM,gBAAgB,6BAA6B,iBAAiB,YAAY;AAEhF,YAAM,eAAe,CAAE;AAEvB,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,cAAM,eAAe,cAAc,CAAC;AAEpC,qBAAa,KAAK;AAAA,UAChB,MAAM,aAAa,aAAa,MAAM;AAAA;AAAA,UACtC,cAAc,WAAW,aAAa,aAAa,cAAc,CAAC;AAAA;AAAA,UAClE,WAAW,WAAW,aAAa,aAAa,WAAW,CAAC;AAAA;AAAA,QACtE,CAAS;AAAA,MACF;AAED,oCAA8B,OAAO;AAErC,aAAO;AAAA,IACR;AAED,aAAS,sBAAsB,kBAAkB;AAC/C,YAAM,mBAAmB,CAAE;AAE3B,uBAAiB,MAAM,IAAI,iBAAiB,aAAa,MAAM;AAC/D,uBAAiB,cAAc,IAAI,iBAAiB,aAAa,cAAc;AAC/E,uBAAiB,qBAAqB,IAAI,iBAAiB,aAAa,qBAAqB;AAE7F,aAAO;AAAA,IACR;AAED,aAAS,cAAc,UAAU;AAC/B,YAAM,WAAW,CAAE;AAEnB,YAAM,WAAW,CAAE;AACnB,YAAM,cAAc,SAAS,iBAAiB,iBAAiB;AAE/D,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAM,aAAa,YAAY,CAAC;AAChC,cAAM,IAAI,WAAW,aAAa,GAAG;AACrC,cAAM,IAAI,WAAW,aAAa,GAAG;AACrC,cAAM,IAAI,WAAW,aAAa,GAAG;AAErC,iBAAS,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,MAC1D;AAED,eAAS,UAAU,IAAI,IAAI,aAAa,QAAQ;AAEhD,YAAM,qBAAqB,CAAE;AAC7B,YAAM,YAAY,CAAE;AACpB,YAAM,gBAAgB,SAAS,iBAAiB,oBAAoB;AAEpE,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,cAAM,eAAe,cAAc,CAAC;AACpC,cAAM,KAAK,aAAa,aAAa,IAAI;AACzC,cAAM,KAAK,aAAa,aAAa,IAAI;AACzC,cAAM,KAAK,aAAa,aAAa,IAAI;AACzC,cAAM,KAAK,aAAa,aAAa,IAAI;AACzC,cAAM,KAAK,aAAa,aAAa,IAAI;AACzC,cAAM,KAAK,aAAa,aAAa,IAAI;AACzC,cAAM,MAAM,aAAa,aAAa,KAAK;AAE3C,cAAM,mBAAmB,CAAE;AAE3B,yBAAiB,IAAI,IAAI,SAAS,IAAI,EAAE;AACxC,yBAAiB,IAAI,IAAI,SAAS,IAAI,EAAE;AACxC,yBAAiB,IAAI,IAAI,SAAS,IAAI,EAAE;AAExC,kBAAU,KAAK,iBAAiB,IAAI,GAAG,iBAAiB,IAAI,GAAG,iBAAiB,IAAI,CAAC;AAIrF,YAAI,IAAI;AACN,2BAAiB,IAAI,IAAI,SAAS,IAAI,EAAE;AAAA,QACzC;AAED,YAAI,IAAI;AACN,2BAAiB,IAAI,IAAI,SAAS,IAAI,EAAE;AAAA,QACzC;AAED,YAAI,IAAI;AACN,2BAAiB,IAAI,IAAI,SAAS,IAAI,EAAE;AAAA,QACzC;AAED,YAAI,KAAK;AACP,2BAAiB,KAAK,IAAI;AAAA,QAC3B;AAED,YAAI,IAAI,OAAO,KAAK,gBAAgB,EAAE,QAAQ;AAC5C,6BAAmB,KAAK,gBAAgB;AAAA,QACzC;AAAA,MACF;AAED,eAAS,oBAAoB,IAAI;AACjC,eAAS,WAAW,IAAI,IAAI,YAAY,SAAS;AAEjD,aAAO;AAAA,IACR;AAED,aAAS,oBAAoB,gBAAgB;AAC3C,YAAM,aAAa,CAAE;AAErB,YAAM,iBAAiB,eAAe,iBAAiB,WAAW;AAElE,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,cAAM,gBAAgB,eAAe,CAAC;AACtC,cAAM,gBAAgB,mBAAmB,aAAa;AACtD,mBAAW,KAAK,aAAa;AAAA,MAC9B;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,eAAe;AACzC,YAAM,gBAAgB,CAAE;AAExB,oBAAc,UAAU,IAAI,cAAc,aAAa,UAAU;AAEjE,YAAM,YAAY,cAAc,aAAa,WAAW;AAExD,UAAI,WAAW;AACb,sBAAc,WAAW,IAAI,eAAe,SAAS;AAAA,MACtD;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,WAAW;AACjC,YAAM,IAAI,CAAE;AACZ,gBAAU,MAAM,GAAG,EAAE,QAAQ,SAAU,GAAG;AACxC,UAAE,KAAK,WAAW,CAAC,CAAC;AAAA,MAC5B,CAAO;AAED,YAAM,SAAS,IAAIC,cAAS;AAC5B,aAAO,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,GAAK,GAAK,GAAK,CAAG;AAEvG,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,YAAY;AACnC,YAAM,aAAa;AAAA,QACjB,MAAM,WAAW,aAAa,MAAM;AAAA,MACrC;AAED,YAAM,KAAK,WAAW,aAAa,IAAI;AAEvC,UAAI,IAAI;AACN,mBAAW,IAAI,IAAI;AAAA,MACpB;AAED,YAAM,MAAM,WAAW,aAAa,KAAK;AAEzC,UAAI,KAAK;AACP,mBAAW,KAAK,IAAI;AAAA,MACrB;AAED,YAAM,SAAS,WAAW,aAAa,QAAQ;AAE/C,UAAI,QAAQ;AACV,mBAAW,QAAQ,IAAI;AAAA,MACxB;AAED,YAAM,YAAY,WAAW,aAAa,WAAW;AAErD,UAAI,WAAW;AACb,mBAAW,WAAW,IAAI;AAAA,MAC3B;AAED,YAAM,aAAa,WAAW,aAAa,YAAY;AAEvD,UAAI,YAAY;AACd,mBAAW,YAAY,IAAI;AAAA,MAC5B;AAED,YAAM,OAAO,WAAW,aAAa,MAAM;AAE3C,UAAI,MAAM;AACR,mBAAW,MAAM,IAAI;AAAA,MACtB;AAED,YAAM,WAAW,WAAW,cAAc,MAAM;AAEhD,UAAI,UAAU;AACZ,mBAAW,MAAM,IAAI,cAAc,QAAQ;AAAA,MAC5C;AAED,YAAM,iBAAiB,WAAW,cAAc,YAAY;AAE5D,UAAI,gBAAgB;AAClB,mBAAW,YAAY,IAAI,oBAAoB,cAAc;AAAA,MAC9D;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,eAAe;AACzC,YAAM,gBAAgB,CAAE;AAExB,oBAAc,eAAe,IAAI,CAAE;AACnC,YAAM,qBAAqB,cAAc,iBAAiB,eAAe;AAEzE,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,cAAM,oBAAoB,mBAAmB,CAAC;AAC9C,cAAM,oBAAoB,uBAAuB,iBAAiB;AAClE,sBAAc,eAAe,EAAE,kBAAkB,IAAI,CAAC,IAAI;AAAA,MAC3D;AAID,oBAAc,WAAW,IAAI,CAAE;AAC/B,YAAM,kBAAkB,cAAc,iBAAiB,WAAW;AAElE,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,cAAM,iBAAiB,gBAAgB,CAAC;AACxC,cAAM,gBAAgB,mBAAmB,cAAc;AACvD,sBAAc,WAAW,EAAE,cAAc,IAAI,CAAC,IAAI;AAAA,MACnD;AAID,oBAAc,YAAY,IAAI,CAAE;AAChC,YAAM,kBAAkB,cAAc,iBAAiB,YAAY;AAEnE,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,cAAM,iBAAiB,gBAAgB,CAAC;AACxC,cAAM,iBAAiB,oBAAoB,cAAc;AACzD,sBAAc,YAAY,EAAE,eAAe,IAAI,CAAC,IAAI;AAAA,MACrD;AAID,oBAAc,6BAA6B,IAAI,CAAE;AACjD,YAAM,mCAAmC,cAAc,iBAAiB,6BAA6B;AAErG,eAAS,IAAI,GAAG,IAAI,iCAAiC,QAAQ,KAAK;AAChE,cAAM,kCAAkC,iCAAiC,CAAC;AAC1E,cAAM,kCAAkC,mCAAmC,+BAA+B;AAC1G,sBAAc,6BAA6B,EACzC,gCAAgC,IAAI,CAC9C,IAAY;AAAA,MACL;AAID,oBAAc,gBAAgB,IAAI,CAAE;AACpC,YAAM,uBAAuB,cAAc,iBAAiB,gBAAgB;AAE5E,eAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AACpD,cAAM,sBAAsB,qBAAqB,CAAC;AAClD,cAAM,sBAAsB,yBAAyB,mBAAmB;AACxE,sBAAc,gBAAgB,EAAE,oBAAoB,IAAI,CAAC,IAAI;AAAA,MAC9D;AAID,oBAAc,QAAQ,IAAI,CAAE;AAC5B,YAAM,cAAc,cAAc,iBAAiB,QAAQ;AAE3D,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAM,aAAa,YAAY,CAAC;AAChC,cAAM,aAAa,gBAAgB,UAAU;AAC7C,sBAAc,QAAQ,EAAE,WAAW,IAAI,CAAC,IAAI;AAAA,MAC7C;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,WAAW;AACjC,YAAM,YAAY,CAAE;AACpB,YAAM,YAAY,UAAU,iBAAiB,MAAM;AAEnD,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,WAAW,UAAU,CAAC;AAC5B,cAAM,YAAY;AAAA,UAChB,UAAU,SAAS,aAAa,UAAU;AAAA,QAC3C;AACD,cAAM,YAAY,SAAS,aAAa,WAAW;AAEnD,YAAI,WAAW;AACb,oBAAU,WAAW,IAAI,eAAe,SAAS;AAAA,QAClD;AAED,kBAAU,KAAK,SAAS;AAAA,MACzB;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,WAAW;AACjC,YAAM,YAAY,EAAE,MAAM,UAAU,aAAa,MAAM,KAAK,aAAc;AAC1E,YAAM,gBAAgB,UAAU,iBAAiB,UAAU;AAE3D,UAAI,eAAe;AACjB,kBAAU,UAAU,IAAI,mBAAmB,aAAa;AAAA,MACzD;AAED,YAAM,gBAAgB,UAAU,cAAc,WAAW;AAEzD,UAAI,eAAe;AACjB,kBAAU,WAAW,IAAI,mBAAmB,aAAa;AAAA,MAC1D;AAED,YAAM,YAAY,UAAU,cAAc,OAAO;AAEjD,UAAI,WAAW;AACb,kBAAU,OAAO,IAAI,eAAe,SAAS;AAAA,MAC9C;AAED,aAAO;AAAA,IACR;AAED,aAAS,aAAa,gBAAgBC,UAAS,WAAW,aAAa;AACrE,YAAM,QAAQ,eAAe;AAC7B,YAAM,aAAa,UAAU,UAAU;AACvC,YAAM,YAAY,WAAW,KAAK;AAElC,UAAI,WAAW;AACb,cAAMR,QAAO,YAAY,UAAU,IAAI;AACvC,cAAM,OAAO,UAAU;AAEvB,cAAM,OAAO,IAAI,KAAK,CAACA,KAAI,GAAG,EAAE,MAAY;AAC5C,cAAM,YAAY,IAAI,gBAAgB,IAAI;AAE1C,cAAM,UAAU,cAAc,KAAK,WAAW,WAAY;AACxD,cAAI,gBAAgB,SAAS;AAAA,QACvC,CAAS;AAED,YAAI,gBAAgB;AAAS,kBAAQ,aAAa;AAAA;AAC7C,kBAAQ,WAAW;AAIxB,gBAAQ,UAAU,YAAU;AAAA,UAC1B,KAAK;AACH,oBAAQ,QAAQS,MAAc;AAC9B;AAAA,UAEF,KAAK;AACH,oBAAQ,QAAQC,MAAsB;AACtC;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AACH,oBAAQ,QAAQC,MAAmB;AACnC;AAAA,UAEF;AACE,oBAAQ,QAAQF,MAAc;AAAA,QACjC;AAED,gBAAQ,UAAU,YAAU;AAAA,UAC1B,KAAK;AACH,oBAAQ,QAAQA,MAAc;AAC9B;AAAA,UAEF,KAAK;AACH,oBAAQ,QAAQC,MAAsB;AACtC;AAAA,UAEF,KAAK;AAAA,UACL,KAAK;AACH,oBAAQ,QAAQC,MAAmB;AACnC;AAAA,UAEF;AACE,oBAAQ,QAAQF,MAAc;AAAA,QACjC;AAED,gBAAQ,UAAU,QAAM;AAAA,UACtB,KAAK;AACH,oBAAQ,YAAYG,MAAY;AAChC,oBAAQ,YAAYC,MAAwB;AAC5C;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAYD,MAAY;AAChC,oBAAQ,YAAYA,MAAY;AAChC;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAYE,MAAa;AACjC,oBAAQ,YAAYA,MAAa;AACjC;AAAA,UAEF;AACE,oBAAQ,YAAYF,MAAY;AAChC,oBAAQ,YAAYC,MAAwB;AAAA,QAC/C;AAED,eAAO;AAAA,MACf,OAAa;AACL,eAAO;AAAA,MACR;AAAA,IACF;AAED,aAAS,yBACP,eACA,oBACA,UACAL,UACA,WACA,aACA,YACA;AACA,YAAM,eAAe,WAAW;AAEhC,YAAM,cAAc,CAAE;AAEtB,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG,KAAK;AACzD,cAAM,mBAAmB,mBAAmB,CAAC;AAC7C,cAAM,SAAS,iBAAiB,OAAO,SAAY,iBAAiB,KAAK;AAEzE,YAAI,YAAY,MAAM,MAAM;AAAW,sBAAY,MAAM,IAAI,CAAE;AAE/D,oBAAY,MAAM,EAAE,KAAK,gBAAgB;AAAA,MAC1C;AAID,YAAM,OAAO,OAAO,KAAK,WAAW;AACpC,YAAM,SAAS,CAAE;AAEjB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAM,gBAAgB,KAAK,CAAC;AAC5B,cAAM,0BAA0B,YAAY,aAAa;AACzD,cAAM,mBAAmB,cAAc,cAAc,aAAa;AAClE,cAAM,WAAW,SAAS,kBAAkBA,UAAS,WAAW,aAAa,YAAY,iBAAiB;AAI1G,cAAM,WAAW,IAAIO,qBAAgB;AAErC,cAAM,eAAe,CAAE;AAEvB,cAAM,WAAW,SAAS;AAE1B,iBAAS,IAAI,GAAG,KAAK,wBAAwB,QAAQ,IAAI,IAAI,KAAK;AAChE,gBAAM,mBAAmB,wBAAwB,CAAC;AAElD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAEvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAEvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,uBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAAA,QACxD;AAED,iBAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,cAAc,CAAC,CAAC;AAI7E,cAAM,OAAO,IAAIC,WAAK,UAAU,QAAQ;AACxC,eAAO,KAAK,IAAI;AAAA,MACjB;AAED,aAAO;AAAA,IACR;AAED,aAAS,kBACP,gBACA,oBACA,UACAT,UACA,WACA,aACA,YACA;AAGA,YAAM,WAAW,IAAIO,qBAAgB;AAErC,YAAM,eAAe,CAAE;AACvB,YAAM,SAAS,CAAE;AAEjB,YAAM,WAAW,SAAS;AAC1B,YAAM,MAAM,eAAe;AAE3B,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG,KAAK;AACzD,cAAM,mBAAmB,mBAAmB,CAAC;AAE7C,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAEvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAEvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACvD,qBAAa,KAAK,SAAS,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAIvD,eAAO,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAC5C,eAAO,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAE5C,eAAO,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAC5C,eAAO,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAE5C,eAAO,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAC5C,eAAO,KAAK,IAAI,iBAAiB,KAAK,IAAI,CAAC,CAAC;AAAA,MAC7C;AAED,eAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,cAAc,CAAC,CAAC;AAC7E,eAAS,aAAa,MAAM,IAAIA,MAAAA,uBAAuB,QAAQ,CAAC,CAAC;AAIjE,YAAM,UAAU,SAAS,gBAAgBR,UAAS,WAAW,aAAa,YAAY,YAAY;AAElG,YAAM,WAAW,IAAIU,wBAAkB,EAAE,KAAK,SAAS,aAAa,MAAM;AAI1E,YAAM,OAAO,IAAID,WAAK,UAAU,QAAQ;AAExC,aAAO;AAAA,IACR;AAED,aAAS,qBAAqB,YAAY,oBAAoB,UAAUT,UAAS,WAAW,YAAY;AAGtG,YAAM,WAAW,IAAIO,qBAAgB;AAErC,YAAM,eAAe,CAAE;AACvB,YAAM,YAAY,CAAE;AAEpB,YAAM,WAAW,SAAS;AAC1B,YAAM,SAAS,WAAW;AAE1B,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG,KAAK;AACzD,cAAM,mBAAmB,mBAAmB,CAAC;AAE7C,cAAM,KAAK,iBAAiB;AAC5B,cAAM,KAAK,iBAAiB;AAC5B,cAAM,KAAK,iBAAiB;AAE5B,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AAEtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AAEtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,qBAAa,KAAK,SAAS,KAAK,IAAI,CAAC,CAAC;AAItC,cAAM,KAAK,iBAAiB,OAAO,SAAY,iBAAiB,KAAK,WAAW;AAChF,cAAM,KAAK,iBAAiB,OAAO,SAAY,iBAAiB,KAAK;AACrE,cAAM,KAAK,iBAAiB,OAAO,SAAY,iBAAiB,KAAK;AAErE,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AACjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AACjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AAEjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AACjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AACjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AAEjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AACjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AACjC,kBAAU,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AAAA,MAClC;AAED,eAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,cAAc,CAAC,CAAC;AAC7E,eAAS,aAAa,SAAS,IAAIA,MAAAA,uBAAuB,WAAW,CAAC,CAAC;AAIvE,YAAM,WAAW,IAAIE,wBAAkB,EAAE,cAAc,MAAM,aAAa,MAAM;AAIhF,YAAM,OAAO,IAAID,WAAK,UAAU,QAAQ;AAExC,aAAO;AAAA,IACR;AAED,aAAS,iBAAiB,UAAU;AAClC,YAAM,WAAW,IAAIF,qBAAgB;AACrC,eAAS,SAAS,IAAII,MAAe,gBAAC,SAAS,WAAW,GAAG,CAAC,CAAC;AAC/D,eAAS,aAAa,YAAY,IAAIA,MAAAA,gBAAgB,SAAS,UAAU,GAAG,CAAC,CAAC;AAE9E,YAAM,WAAW,IAAID,wBAAkB,EAAE,OAAO,UAAU,aAAa,MAAM;AAE7E,YAAM,OAAO,IAAID,WAAK,UAAU,QAAQ;AAExC,aAAO;AAAA,IACR;AAED,aAAS,YAAY,aAAa,UAAUT,UAAS,WAAW,aAAa,YAAY;AACvF,YAAM,OAAO,OAAO,KAAK,WAAW;AACpC,YAAM,SAAS,CAAE;AAEjB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,cAAM,aAAa,KAAK,CAAC;AACzB,cAAM,qBAAqB,YAAY,UAAU;AACjD,cAAM,eAAe,gBAAgB,YAAY,SAAS;AAE1D,gBAAQ,cAAY;AAAA,UAClB,KAAK;AACH,kBAAM,gBAAgB,UAAU,UAAU,cAAc,UAAU;AAClE,kBAAM,YAAY;AAAA,cAChB;AAAA,cACA;AAAA,cACA;AAAA,cACAA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAED,qBAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,KAAK;AAClD,qBAAO,KAAK,UAAU,CAAC,CAAC;AAAA,YACzB;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,iBAAiB,UAAU,UAAU,eAAe,UAAU;AACpE,mBAAO;AAAA,cACL;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA;AAAA,gBACAA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACD;AAAA,YACF;AACD;AAAA,UAEF,KAAK;AACH,kBAAM,aAAa,UAAU,UAAU,WAAW,UAAU;AAC5D,mBAAO,KAAK,qBAAqB,YAAY,oBAAoB,UAAUA,UAAS,WAAW,UAAU,CAAC;AAC1G;AAAA,UAEF,KAAK;AACH,mBAAO,KAAK,iBAAiB,QAAQ,CAAC;AACtC;AAAA,UAEF;AACE,oBAAQ,MAAM,6CAA6C;AAAA,QAC9D;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,KAAK,WAAW;AACvC,UAAI,UAAU,UAAU,eAAe,GAAG,MAAM,QAAW;AACzD,eAAO;AAAA,MACf,WAAiB,UAAU,UAAU,cAAc,GAAG,MAAM,QAAW;AAC/D,eAAO;AAAA,MACf,WAAiB,UAAU,UAAU,WAAW,GAAG,MAAM,QAAW;AAC5D,eAAO;AAAA,MACf,WAAiB,QAAQ,WAAW;AAC5B,eAAO;AAAA,MACf,OAAa;AACL,eAAO;AAAA,MACR;AAAA,IACF;AAED,aAAS,cAAc,WAAW,UAAU,YAAY;AACtD,YAAM,cAAc,CAAE;AAEtB,YAAM,qBAAqB,SAAS,oBAAoB;AAExD,YAAM,YAAY,WAAW;AAE7B,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,IAAI,GAAG,KAAK;AACzD,cAAM,mBAAmB,mBAAmB,CAAC;AAC7C,YAAI,MAAM,iBAAiB,QAAQ,SAAY,iBAAiB,MAAM;AAEtE,YAAI,QAAQ;AAAW,gBAAM;AAE7B,YAAI,YAAY,GAAG,MAAM;AAAW,sBAAY,GAAG,IAAI,CAAE;AAEzD,oBAAY,GAAG,EAAE,KAAK,gBAAgB;AAAA,MACvC;AAED,aAAO;AAAA,IACR;AAED,aAAS,WAAW,UAAUA,UAAS,WAAW,aAAa,YAAY;AACzE,YAAM,QAAQ,IAAIY,YAAO;AAEzB,YAAM,cAAc,cAAc,WAAW,UAAU,UAAU;AACjE,YAAM,SAAS,YAAY,aAAa,UAAUZ,UAAS,WAAW,aAAa,UAAU;AAE7F,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,IAAI,OAAO,CAAC,CAAC;AAAA,MACpB;AAED,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,YAAY,UAAU,UAAU;AACvD,UAAI,CAAC,YAAY;AACf;AAAA,MACD;AAED,YAAM,sBAAsB,CAAE;AAC9B,YAAM,OAAO,OAAO,KAAK,UAAU;AAEnC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,KAAK,KAAK,CAAC;AAEjB,iBAAS,IAAI,GAAG,IAAI,MAAM,oBAAoB,QAAQ,KAAK;AACzD,gBAAM,YAAY,MAAM,oBAAoB,CAAC;AAE7C,cAAI,UAAU,OAAO,IAAI;AACvB,gCAAoB,KAAK,SAAS;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAED,eAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAM,YAAY,oBAAoB,CAAC;AACvC,kBAAU,MAAM,UAAU,WAAW,UAAU,IAAI,CAAC,GAAG,QAAQ;AAAA,MAChE;AAAA,IACF;AAED,aAAS,SAASR,OAAMQ,UAAS,WAAW,aAAa,YAAY,SAAS;AAC5E,UAAIR,MAAK,UAAU;AAAW,eAAOA,MAAK;AAE1C,MAAAA,MAAK,QAAQ,QAAQA,OAAMQ,UAAS,WAAW,aAAa,UAAU;AAEtE,aAAOR,MAAK;AAAA,IACb;AAED,aAAS,kBAAkB,cAAcQ,UAAS,WAAW;AAC3D,UAAI;AAEJ,YAAM,sBAAsB,aAAa;AACzC,YAAM,8BAA8B,UAAU,UAAU;AAExD,UAAI,wBAAwB,QAAQ,4BAA4B,mBAAmB,MAAM,QAAW;AAGlG,cAAM,4BAA4B,4BAA4B,mBAAmB;AACjF,cAAM,eAAe,0BAA0B,KAAK,aAAa,KAAK;AAEtE,mBAAW,IAAIa,MAAAA,qBAAqB;AAAA,UAClC,aAAa;AAAA,UACb,WAAW,aAAa;AAAA,UACxB,WAAW,aAAa;AAAA,QAClC,CAAS;AAAA,MACT,OAAa;AAGL,mBAAW,IAAIH,MAAiB,kBAAC,EAAE,aAAa,KAAI,CAAE;AAAA,MACvD;AAED,eAAS,OAAO,aAAa;AAI7B,YAAM,eAAe,aAAa;AAElC,YAAM,QAAQ,aAAa,UAAU,GAAG,CAAC;AACzC,eAAS,MAAM,SAAS,KAAK;AAC7B,eAAS,MAAM,oBAAqB;AAIpC,UAAI,aAAa,WAAW,GAAG;AAC7B,iBAAS,UAAU,SAAS,aAAa,OAAO,CAAC,IAAI,aAAa,OAAO,CAAC,GAAG,EAAE,IAAI;AAAA,MACpF;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAe,eAAeV,UAAS,WAAW,aAAa;AACtE,YAAM,YAAY,IAAIY,YAAO;AAE7B,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,cAAM,YAAY,cAAc,CAAC;AACjC,YAAIE,SAAQd,SAAQ,UAAU,QAAQ;AAEtC,YAAIc,WAAU,QAAW;AACvB,sBAAY,UAAU,UAAUd,UAAS,WAAW,WAAW;AAC/D,UAAAc,SAAQd,SAAQ,UAAU,QAAQ;AAAA,QACnC;AAED,cAAM,WAAWc,OAAM,MAAO;AAI9B,cAAM,YAAY,UAAU;AAE5B,YAAI,WAAW;AACb,mBAAS,aAAa,SAAS;AAAA,QAChC;AAED,kBAAU,IAAI,QAAQ;AAAA,MACvB;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAY,UAAUd,UAAS,WAAW,aAAa;AAC9D,YAAM,aAAa,UAAU,WAAW,EAAE,QAAQ,EAAE,QAAQ;AAE5D,UAAI,WAAW,MAAM,GAAG;AACtB,cAAM,WAAW,WAAW,MAAM;AAElC,cAAM,aAAa,UAAU,YAAY;AACzC,cAAM,WAAW,UAAU,KAAK;AAEhC,wBAAgB,YAAY,UAAU,QAAQ;AAE9C,QAAAA,SAAQ,WAAW,EAAE,IAAI,SAAS,UAAUA,UAAS,WAAW,aAAa,YAAY,UAAU;AAAA,MAC3G,OAAa;AACL,cAAM,gBAAgB,WAAW,YAAY;AAE7C,QAAAA,SAAQ,WAAW,EAAE,IAAI,SAAS,eAAeA,UAAS,WAAW,aAAa,YAAY,cAAc;AAAA,MAC7G;AAAA,IACF;AAED,aAAS,aAAae,UAAS;AAC7B,YAAM,aAAaA,SAAQ;AAC3B,YAAM,YAAYA,SAAQ;AAC1B,YAAMf,WAAU,CAAE;AAClB,YAAM,aAAa,OAAO,KAAK,UAAU;AACzC,YAAM,cAAc,CAAE;AAItB,UAAI,WAAW;AACb,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,gBAAM,WAAW,UAAU,CAAC;AAC5B,gBAAM,aAAa,SAAS,OAAO,UAAU,CAAC;AAE9C,cAAIe,SAAQ,QAAQ,UAAU,GAAG;AAC/B,wBAAY,SAAS,MAAM,IAAIA,SAAQ,QAAQ,UAAU;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAID,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,cAAM,YAAY,WAAW,CAAC;AAC9B,cAAM,YAAY,WAAW,SAAS;AAEtC,cAAM,YAAY,OAAO,KAAK,UAAU,WAAW,EAAE,QAAQ,CAAC;AAE9D,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,WAAW,UAAU,CAAC;AAE5B,sBAAY,UAAUf,UAAS,WAAW,WAAW;AAAA,QACtD;AAAA,MACF;AAED,aAAOA;AAAA,IACR;AAED,aAAS,iBAAiB,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,YAAY,IAAI,OAAO,MAAM,GAAG,EAAE,IAAK;AAE7C,YAAI,UAAU,kBAAkB;AAAS,iBAAO;AAAA,MACjD;AAAA,IACF;AAED,aAAS,MAAMA,UAASe,UAAS;AAC/B,YAAM,QAAQ,IAAIH,YAAO;AAEzB,YAAM,eAAe,iBAAiBG,SAAQ,MAAM,CAAC;AACrD,YAAM,YAAYA,SAAQ,MAAM,aAAa,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO;AAE5E,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,YAAY,UAAU,CAAC;AAC7B,cAAM,WAAWf,SAAQ,UAAU,UAAU,CAAC;AAI9C,cAAM,YAAY,UAAU,WAAW;AAEvC,YAAI,WAAW;AACb,mBAAS,aAAa,SAAS;AAAA,QAChC;AAED,cAAM,IAAI,QAAQ;AAAA,MACnB;AAED,aAAO;AAAA,IACR;AAED,UAAM,UAAU,aAAa,IAAI;AACjC,UAAM,UAAU,aAAa,OAAO;AAEpC,WAAO,MAAM,SAAS,OAAO;AAAA,EAC9B;AAAA,EAED,aAAa,WAAW;AACtB,SAAK,oBAAoB,KAAK,SAAS;AAAA,EACxC;AACH;;"}